options:
 logging: CLOUD_LOGGING_ONLY  # Use Cloud Logging for logs

steps:

# Build the container image
- name: gcr.io/cloud-builders/docker
  args: ['build', '-t', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/$REPO_NAME/$TAG_NAME:${COMMIT_SHA}','-f','prod-Dockerfile', '.']
  id: Building the container image


# Push the container image to Container Registry
- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/$REPO_NAME/$TAG_NAME:${COMMIT_SHA}']
  id: Pushing the image to registry




# Deploy container image to Cloud Run
- name: 'gcr.io/cloud-builders/gcloud'
  args: ['run', 'deploy', '${_SERVICE_NAME}', '--image', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/$REPO_NAME/$TAG_NAME:${COMMIT_SHA}', '--region', 'us-central1', '--platform', 'managed', "--allow-unauthenticated"]


- name: 'gcr.io/cloud-builders/gcloud'
  args: [
    'compute', 'url-maps', 'invalidate-cdn-cache',
    'propvr-tech-lb',               # URL map name (load balancer)
    '--host', 'view.propvr.tech',
    '--path', '/*', # Invalidate all paths under
    '--async'
  ]
  id: 'Invalidating CDN cache for view.propvr.tech/*'


- name: 'gcr.io/cloud-builders/gcloud'
  args: [
    'compute', 'url-maps', 'invalidate-cdn-cache',
    'propvr-tech-lb',               # URL map name (load balancer)
    '--host', 'pixelstreaming.damaclabs.com',
    '--path', '/damac', # Invalidate all paths under
    '--async'
  ]
  id: 'Invalidating CDN cache for pixelstreaming.damaclabs.com/damac'

- name: 'gcr.io/cloud-builders/gcloud'
  args: [
    'compute', 'url-maps', 'invalidate-cdn-cache',
    'propvr-tech-lb',               # URL map name (load balancer)
    '--host', 'vitualtours.damaclabs.com',
    '--path', '/*', # Invalidate all paths under
    '--async'
  ]
  id: 'Invalidating CDN cache for vitualtours.damaclabs.com/yRnIS3'

- name: 'curlimages/curl'
  entrypoint: 'curl'
  args: [
      '-X', 'POST',
      'https://api.cloudflare.com/client/v4/zones/0f9b691d2247ba4b25773a2c4e160779/purge_cache',
      '-H', 'Authorization: Bearer DHwCe9TfnoSpExM76fJzOiVz8DZvZjC_arhC5Qa9',
      '-H', 'Content-Type: application/json',
       '--data',
      '{"files":["https://view.propvr.tech/*"]}'
  ]

