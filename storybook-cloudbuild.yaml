steps:

# Build the container image
- name: gcr.io/cloud-builders/docker
  args: ['build', '-t', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/$REPO_NAME/storybook/$BRANCH_NAME:${COMMIT_SHA}','-f','DockerfileStorybook', '.']
  id: Building the container image


# Push the container image to Container Registry
- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/$REPO_NAME/storybook/$BRANCH_NAME:${COMMIT_SHA}']
  id: Pushing the image to registry




# Deploy container image to Cloud Run
- name: 'gcr.io/cloud-builders/gcloud'
  args: ['run', 'deploy', '${_SERVICE_NAME}', '--image', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/$REPO_NAME/storybook/$BRANCH_NAME:${COMMIT_SHA}', '--region', 'us-central1', '--platform', 'managed', "--allow-unauthenticated"]
