<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script type="module">
      const cspMetaTag = document.createElement("meta");
      cspMetaTag.setAttribute("http-equiv", "Content-Security-Policy");
      const appUrl = import.meta.env.VITE_APP_BASE_URL;
      const bucketUrl = import.meta.env.VITE_APP_BUCKET_CDN;
      const cspContent = `
        default-src 'self';
        worker-src 'self' blob:; 
        script-src 'self' 'unsafe-eval' 'nonce-hu2832y1he22y82121u89' https://maps.googleapis.com https://my.matterport.com/api/v2/users/current https://www.clarity.ms/tag/ https://www.clarity.ms/s/ https://cdn.pannellum.org/ https://cdn.jsdelivr.net/ https://static.matterport.com/showcase-sdk/ https://www.googletagmanager.com/gtag/js https://aframe.io/releases/ https://ajax.googleapis.com/ajax/libs/ https://cdn.propvr.tech/js/ blob:;
        style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.googleapis.com/css2 https://cdn.propvr.tech/css/themify-icons.min.css https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.css https://cdn.pannellum.org/2.5/pannellum.css https://cdn.propvr.tech/css/;
        font-src 'self' https://cdn.glitch.com/ https://fonts.gstatic.com https://fonts.googleapis.com/css2 https://cdn.glitch.me/ data:;
        img-src 'self' data: https://maps.googleapis.com https://maps.gstatic.com https://${bucketUrl} https://firebasestorage.googleapis.com/ https://www.googletagmanager.com/ https://c.clarity.ms/c.gif https://storagecdn.propvr.tech/ https://storagecdn.propvr.ai/ https://cdn.glitch.com https://cdn.glitch.me/  https://flagcdn.com blob: https://*.google.co.in;
        frame-src 'self' https://showcase.propvr.tech/ https://fp.propvr.tech/ https://propvr.widget.floor-plan.online/;  
        connect-src 'self' https://mapsresources-pa.googleapis.com https://maps.googleapis.com/ https://${bucketUrl} https://www.google-analytics.com/ https://*.clarity.ms/collect ${appUrl} https://storagecdn.propvr.ai/ https://firebasestorage.googleapis.com/ https://cdn.propvr.tech/ https://propvr-in-31420.appspot.com https://*.google-analytics.com https://*.analytics.google.com https://*.googletagmanager.com https://*.google.com https://*.google.co.in data:;
        base-uri 'self';
        media-src 'self' https://storagecdn.propvr.tech/ https://storagecdn.propvr.ai/ https://cdn.propvr.tech/ https://firebasestorage.googleapis.com;
      `;
      cspMetaTag.setAttribute("content", cspContent.trim());
      document.head.appendChild(cspMetaTag);
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r79/three.min.js"></script>
    <script
      nonce="hu2832y1he22y82121u89"
      type="text/javascript"
      src="https://cdn.pannellum.org/2.5/pannellum.js"
    ></script>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.css"
    />
    <script
      nonce="hu2832y1he22y82121u89"
      src="https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.js"
    ></script>
    <!-- <script defer src="https://aframe.io/releases/1.0.4/aframe.min.js"></script>  -->
    <script defer src="https://cdn.propvr.tech/js/aframe-look-at-component.min.js"></script> 
    <script defer src="https://cdn.propvr.tech/newui/cameracontrols.min.js"></script>
    <!-- <script defer src="https://cdn.propvr.tech/exterior/js/htmlembedcomp.min.js"></script>    <link rel="stylesheet" href="https://cdn.pannellum.org/2.4/pannellum.css" /> -->

    <script
      nonce="hu2832y1he22y82121u89"
      src="https://static.matterport.com/showcase-sdk/latest.js"
    ></script>
    <script
      nonce="hu2832y1he22y82121u89"
      src="/assets/js/hammer.min.js"
    ></script>
    <!-- <script
      nonce="hu2832y1he22y82121u89"
      async
      src="https://www.googletagmanager.com/gtag/js?id=%VITE_APP_GA%"
    ></script> -->
    <script
      nonce="hu2832y1he22y82121u89"
      src="https://aframe.io/releases/1.7.1/aframe.min.js"
    ></script>
    <script
      nonce="hu2832y1he22y82121u89"
      src="/assets/js/aframe-orbit-controls.min.js"
    ></script>
    <link rel="stylesheet" href="https://cdn.pannellum.org/2.5/pannellum.css" />
    <link
      href="/public/assets/css/video-js.css"
      rel="stylesheet"
      type="text/css"
    />

    <!-- <script nonce="hu2832y1he22y82121u89">
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "%VITE_APP_GA%");
    </script> -->
    <!-- <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());
      // We'll set this variable from our Vue app
      window.GA_MEASUREMENT_ID = null;
      // This function will be called from the Vue app
      function initializeGA(measurementId) {
        window.GA_MEASUREMENT_ID = measurementId;
        gtag("config", measurementId);
      }
    </script> -->
    <script nonce="hu2832y1he22y82121u89" type="module">
      let clarityId = import.meta.env.VITE_APP_MC;   
      if (clarityId) {
        (function (c, l, a, r, i, t, y) {
          c[a] =
            c[a] ||
            function () {
              (c[a].q = c[a].q || []).push(arguments);
            };
          t = l.createElement(r);
          t.async = 1;
          t.src = "https://www.clarity.ms/tag/" + i;
          y = l.getElementsByTagName(r)[0];
          y.parentNode.insertBefore(t, y);
        })(window, document, "clarity", "script", clarityId);
      }
    </script>
    <link
      href="https://cdn.propvr.tech/css/magflip.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <link
      href="https://cdn.propvr.tech/css/themify-icons.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <script
      nonce="hu2832y1he22y82121u89"
      src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"
      type="text/javascript"
    ></script>
    <script
      nonce="hu2832y1he22y82121u89"
      src="https://cdn.propvr.tech/js/magflip.min.js"
      type="text/javascript"
    ></script>
  </head>

  <body oncontextmenu="return false;">
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
  <script nonce="hu2832y1he22y82121u89" src="/assets/js/video.js"></script>
  <script
    nonce="hu2832y1he22y82121u89"
    src="/assets/js/videojs-pannellum-plugin.js"
  ></script>
</html>