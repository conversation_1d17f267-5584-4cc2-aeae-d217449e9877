import { defineStore } from "pinia";
import { GetRequestWithHeaders, GetRequest, PostRequestWithHeaders } from "../helpers/API";
import { cdn } from "../helpers/helper";

export const creationToolStore = defineStore("creationToolStore", {
  state: () => ({
    screenType: false,
    projectData: {
      master: {
        scene: {},
      },
    },
    projectCardData: {},
    hideStatus: false,
    toolsButtonList: {
      fullScreen: {
        status: false,
        visible: false,
      },
      radius: {
        status: false,
        visible: false,
      },
    },
    translationMap: false,
    currencyData: {
      currency: null,
      exchangeRatio: [],
    },
    breadCrumb: [],
    sceneVisited: [],
    currentVRView: false,
    landmarkData: false,
    unitData: false,
    buildingData: {},
    allBuildings: false,
    unitplanData: false,
    buildingDataCard: {},
    communityData: {},
    SceneData: {},
    amenityCardData: {},
    amenityOptions: [],
    vrtourData: {},
    listOfVrTourData: null,
    allUnitCardData: {},
    floorData: {},
    favoritesData: {},
    unitPlanFavoritesData: {},
    organization_thumbnail: false,
    organization_name: false,
    addtofavorites: [],
    sidebarOptions: {},
    activeOptionId: false,
    amenityCategories: false,
    galleryList: false,
    galleryCategories: false,
    isMobile: false,
    selectedTabOption: false,
    showInfoModal: [],
    isTouchScreen: false,
    isFullScreen: false,
    enabledFloors: false,
    toggleState: false,
    rotatableFrames: {},
    currentImageId: false,
    Navigator: {},
    currentZoomlevel: 0,
    showRadius: false,
    filteredDataPoints: {},
    filteredUnits: {},
    show360: false,
    showFilter: false,
    hideLogo: false,
    splashLoader: false,
    logo: false,
    currentSceneVideo: false,
    currentSceneVideoThumb: false,
    loadedFont: false,
    orgizationLoadedFont: false,
    organizationDetails: {},
    sceneType: false,
    isLandscape: false,
    svgVisibility: {
      toggle: false,
      showSVG: true,
    },
    isNavigatedFromMasterScene: null,
    isExperienceStartEventSent: false,
    isIOS: false,
    // Store last visited routes for each sidebar option
    lastVisitedRoutes: {},
  }),
  actions: {
    async getMasterScenes (organization) {
      return new Promise((resolve, reject) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/getAllScenes`,
        }).then((res) => {
          if (res.status) {
            this.SceneData = { ...this.SceneData, ...res.data };
            resolve(res.data);
          } else {
            reject();
          }
        });
      });
    },
    async getProjectScenes (organization, project_id) {
      return new Promise((resolve, reject) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/project/${project_id}/getAllScenes`,
        }).then((res) => {
          if (res.status) {
            this.SceneData = { ...this.SceneData, ...res.data };
            console.log(this.SceneData);

            resolve(res.data);
          } else {
            reject();
          }
        });
      });
    },
    async getRotatableFrames (project_id, organization, type, parent) {
      return new Promise((resolve, reject) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/project/${project_id}/getAllScenes?type=${type}&parent=${parent}`,
        }).then((res) => {
          if (res.status) {
            this.rotatableFrames[project_id] = res.data;
            resolve(res.data);
          } else {
            reject();
          }
        });
      });
    },
    async GetSceneData (sceneId, organization) {
      return new Promise((resolve, reject) => {
        GetRequestWithHeaders({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            "masterScene/getScene/" +
            sceneId,
          organization: organization,
        })
          .then((res) => {
            if (res.status) {
              this.projectData.master.scene[sceneId] = res;
            }
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    async getListofLandmark (project_id, organization) {
      return new Promise((resolve, reject) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/project/${project_id}/getListofProjectLandmark`,
        }).then((res) => {
          if (res.status) {
            this.landmarkData = res.data;
            resolve(res);
          } else {
            reject();
          }
        });
      });
    },
    async getListOfBuildings (project_id, organization_id) {
      return new Promise((resolve, reject) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization_id}/project/${project_id}/getCountforBuilding`,
        })
          .then((res) => {
            this.buildingData = res.data;
            this.getAllListofBuildings(project_id, organization_id);
            resolve(res.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    async getAllListofBuildings (project_id, organization_id) {
      return new Promise((resolve, reject) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization_id}/project/${project_id}/getListOfBuildings`,
        })
          .then((res) => {
            this.allBuildings = res.data;
            resolve(res.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    async getListofUnits (project_id, organization_id) {
      return new Promise((resolve, reject) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization_id}/project/${project_id}/getListofUnits`,
        })
          .then((res) => {
            this.unitData = res.data;
            resolve(res.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    async getCountForUnits (project_id, organization_id) {
      return new Promise((resolve, reject) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization_id}/project/${project_id}/getCountForUnits`,
        })
          .then((res) => {
            this.allUnitCardData = res.data;
            resolve(res.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    async getListOfUnitplan (project_id, organization_id) {
      return new Promise((resolve, reject) => {
        GetRequestWithHeaders({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization_id}/project/${project_id}/getListOfUnitplan`,
        })
          .then((res) => {
            this.unitplanData = res.data;
            resolve(res.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    async getFloorplanDetails (building_id, project_id, organization_id) {
      return new Promise((resolve, reject) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization_id}/project/${project_id}/getFloorDetails?building_id=${building_id}`,
        })
          .then((res) => {
            this.floorData[building_id] = res.data;
            resolve(res.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },

    async getTranslation (organization_id) {
      return new Promise((resolve, reject) => {
        GetRequest({
          url: import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization_id}/getTranslations`,
        })
          .then((res) => {
            console.log("get translation", res);
            // window.translationMap = res.data;
            this.translationMap = res.data.translations;
            resolve(res.data);

          })
          .catch((err) => {
            this.translationMap = [],
            reject(err);
          });
      });
    },

    getFloorStatus (floor_id, building_id) {
      if (floor_id && this.buildingData[building_id]) {
        const units = Object.values(this.unitData).filter(
          (unit) =>
            unit.floor_id === floor_id.toString() &&
            building_id === unit.building_id,
        );
        const result = Object.values(units).filter(
          (unit) => unit.status === "available",
        );
        return result.length > 0 ? true : false;
      }
      return false;
    },
    async getCommunities (project_id, organization_id) {
      return new Promise((resolve, reject) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization_id}/project/${project_id}/getCommunities`,
        })
          .then((res) => {
            this.communityData = res.data;
            resolve(res.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    async getListofAmenities (project_id, organization) {
      return new Promise((resolve) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/project/${project_id}/GetAmenities`,
        }).then((res) => {
          if (res.status) {
            this.amenityCardData = res.data;
          }
          Object.keys(res.data).forEach((key) => {
            this.amenityOptions.push({
              id: key,
              value: res.data[key].name,
              category: res.data[key].category,
            });
          });
          resolve(res);
        });
      });
    },
    async getFilterDataPoints (organization, project_id, query) {
      return new Promise((resolve, reject) => {
        GetRequestWithHeaders({
          url: query
            ? import.meta.env.VITE_APP_BASE_URL +
              `publicapis/organization/${organization}/project/${project_id}/filterDataPoints?${query}`
            : import.meta.env.VITE_APP_BASE_URL +
              `publicapis/organization/${organization}/project/${project_id}/filterDataPoints`,
        })
          .then((response) => {
            this.filteredDataPoints = response.data;
            resolve(response.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    async getFilteredUnits (organization, project_id, query) {
      return new Promise((resolve, reject) => {
        GetRequestWithHeaders({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/project/${project_id}/filterunits?${query}`,
        })
          .then((response) => {
            this.filteredUnits = response.data;
            resolve(response.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    async getListofProjects (organization, project_id) {
      return new Promise((resolve, reject) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/listProjectsFromOrganization`,
        })
          .then((res) => {
            if (res.status) {
              this.projectCardData = res.data;
              if (project_id){
                this.hideStatus = res.data[project_id].projectSettings.general
                  .hideStatus
                  ? res.data[project_id].projectSettings.general.hideStatus
                  : false;
              }
            }
            // Object.keys(res.data).forEach((index) => {
            //   GetRequest({
            //     Url: import.meta.env.VITE_APP_BASE_URL+
            //                         "publicapis/getCountforProject?organization_id="+organization+"&project_id="+index,
            //   }).then((res) => {
            //     If (Object.keys(res.data).length>0) {
            //       This.projectCardData[index]={...this.projectCardData[index], ...res.data};
            //     }
            //   }).catch((err) => {
            //     Reject(err);
            //   });
            // });
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    async getVRId (tour_id, organization, project_id) {
      return new Promise((resolve) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/project/${project_id}/GetTourById?tour_id=` +
            tour_id,
        }).then((res) => {
          this.vrtourData = res.data;
        });
        resolve();
      });
    },

    async getListOfVRId (organization, project_id) {
      return new Promise((resolve) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/project/${project_id}/ListTours`,
        }).then((res) => {
          this.listOfVrTourData = res.data;
          resolve(res.data);
        });
      });
    },

    async getProjectSceneId (project_id, organization, floor_id) {
      return new Promise((resolve, reject) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/project/${project_id}/getProjectSceneId?floor_id="${floor_id}`,
        }).then((res) => {
          if (res.status) {
            resolve(res.scene_id);
          } else {
            reject();
          }
        });
      });
    },
    async getOrganization (organization) {
      return new Promise((resolve) => {
        GetRequestWithHeaders({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/getOrganization`,
        }).then((res) => {
          this.organization_name = res.data.name;
          this.organization_thumbnail = cdn(
            res.data.thumbnail,
            "propvr-in-31420.appspot.com",
            "storagecdn.propvr.ai",
          );
          this.organizationDetails = res.data;
          this.currencyData.exchangeRatio = res.data.exchangeRatio;
          resolve(res.data);
        });
      });
    },
    async getOrganizationByUniqueId (organization) {
      return new Promise((resolve) => {
        GetRequestWithHeaders({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/${organization}/getOrganizationByUniqueOrgId`,
        }).then((res) => {
          this.organization_thumbnail = cdn(
            res.data.thumbnail,
            "propvr-in-31420.appspot.com",
            "storagecdn.propvr.ai",
          );
          resolve(res.data);
        });

      });
    },
    async getOptions (organization, project_id) {
      return new Promise((resolve) => {
        GetRequest({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/project/${project_id}/getOptions`,
        }).then((res) => {
          if (Object.keys(res.data).length > 0) {
            this.sidebarOptions [project_id]= res.data;
            resolve(res.data);
          }
        });
      });
    },
    async getCategories (organization, project_id) {
      return new Promise((resolve) => {
        GetRequestWithHeaders({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/project/${project_id}/getCategories`,
        }).then((res) => {
          this.amenityCategories = res.data;
          resolve(res.data);
        });
      });
    },
    async getGallery (organization, project_id) {
      return new Promise((resolve) => {
        GetRequestWithHeaders({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/project/${project_id}/getGallery`,
        }).then((res) => {
          this.galleryList = res.data;
          resolve(res);
        });
      });
    },
    async getGalleryCategories (organization, project_id) {
      return new Promise((resolve) => {
        GetRequestWithHeaders({
          url:
            import.meta.env.VITE_APP_BASE_URL +
            `publicapis/organization/${organization}/project/${project_id}/getGalleryCategories`,
        }).then((res) => {
          this.galleryCategories = res.data;
          resolve(res);
        });
      });
    },
    async callbackFunctionMonitorChanges () {
      if (window.innerWidth <= 640) {
        this.isMobile = true;
      } else {
        this.isMobile = false;
      }

      // Detect iOS
      const userAgent = window.navigator.userAgent.toLowerCase();
      this.isIOS = /iphone|ipad|ipod/.test(userAgent) ||
                   (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);

      const isDesktop = window.innerWidth >= 1024 && !('ontouchstart' in window);

      if (isDesktop) {
        return;
      }
      if ( window.matchMedia("(orientation: landscape)").matches){
        this.isLandscape = true;
      } else {
        this.isLandscape = false;
      }
    },
    updateAddRemovefavorites (val) {
      const DTO = {
        status: null,
        currency: null,
        price: null,

        "Tower Name": null,
        unit_id: null,
        floor: null,

        type: null,
        measurement_type: null,
        measurement: null,
        bedrooms: null,
        bathroom: null,
        is_furnished: null,
        _id: null,
      };

      if (
        !Object.values(this.favoritesData).some(
          (innerObj) => innerObj.unit_id === val._id,
        )
      ) {
        if (val) {
          DTO._id = val.unitplan_id;

          /* UnitData */
          if (val._id) {
            // From UnitsData
            if (this.unitData[val._id]) {
              DTO.status = val.status;
              DTO.currency = val.currency.toUpperCase();
              DTO.price = val.price ? Number(val.price) : null;
              DTO.unit_id = val._id;
              DTO.floor = val.floor_id;
              DTO.name = val.name;
            }

            // From Building Data
            if (val.building_id) {
              if (this.allBuildings[val.building_id]) {
                DTO["Tower Name"] = this.allBuildings[val.building_id].name; // Tower name
              }
            }
          }

          /* UnitPlanData */
          if (val.unitplan_id) {
            DTO.type = val.type;
            DTO.measurement_type = val.measurement_type;
            DTO.measurement = val.measurement;
            DTO.bedrooms = val.bedroom;
            DTO.bathroom = this.unitplanData[val.unitplan_id].bathrooms;
            DTO.is_furnished = this.unitplanData[val.unitplan_id].is_furnished;
          }

          if (DTO.unit_id && DTO._id) {
            this.favoritesData[DTO.unit_id] = DTO;
          }
        }
      } else {
        delete this.favoritesData[val._id]; // Remove
      }
    },
    updateAddRemoveUnitPlanFavorites (val) {
      const DTO = {
        name: null,
        type: null,
        measurement_type: null,
        measurement: null,
        min_measurement: null,
        max_measurement: null,
        bedrooms: null,
        bathrooms: null,
        is_furnished: null,
        _id: null,
      };

      if (
        !Object.values(this.unitPlanFavoritesData).some(
          (innerObj) => innerObj._id === val._id,
        )
      ) {
        if (val) {
          /* UnitData */
          if (val._id) {
            DTO.name = val.name.replace(/[^a-zA-Z0-9]/g, " ");
            DTO.type = val.type;
            DTO.measurement_type = val.measurement_type;
            DTO.measurement = val.measurement;
            DTO.min_measurement = val.minMeasurement;
            DTO.max_measurement = val.maxMeasurement;
            DTO.bedrooms = val.bedrooms;
            DTO.bathrooms = val.bathrooms;
            DTO.is_furnished = val.is_furnished;
            DTO._id = val._id;
          }

          if (DTO._id) {
            this.unitPlanFavoritesData[DTO.name] = DTO;
          }
        }
      } else {
        delete this.unitPlanFavoritesData[
          val.name.replace(/[^a-zA-Z0-9]/g, " ")
        ]; // Remove
      }
    },
    findTileSourceDimensions () {
      var tileSource = window.viewer.world.getItemAt(0).source; // Get the original width and height of tilesource of deep_zoom
      var originalWidth = tileSource.dimensions.x;
      var originalHeight = tileSource.dimensions.y;
      return { width: originalWidth, height: originalHeight };
    },
    async destroyViewer () {
      return new Promise((resolve) => {
        window.viewer.destroy(); // Destroy
        window.viewer = null; // Reset the viewer
        resolve();
      });
    },
    async deleteOverlays (elementIds) {
      return new Promise((resolve) => {
        if (elementIds) {
          // Clear only the specified elements in array
          elementIds.forEach((id) => window.viewer.removeOverlay(id)); // Clear the overlays of previous elements
        } else {
          // Clear All
          window.viewer.clearOverlays(); // Clear the overlays of previous elements
        }
        resolve();
      });
    },
    async addShadowOverlay (classList, overlapIndex, id) {
      const shadowOverlaysSize = this.findTileSourceDimensions();
      const element = document.createElement("div");
      element.id = id;
      element.style.zIndex = overlapIndex;
      element.className = classList;
      window.viewer.addOverlay({
        element: element,
        px: 0,
        py: 0,
        width: shadowOverlaysSize.width,
        height: shadowOverlaysSize.height,
      });
    },

    async updateSceneType (path) {
      this.sceneType = path.includes('projectscene') ? true : false;
    },
    async CreateLead (DTO, project_id, unit_id) {
      if (DTO && project_id && unit_id) {
        return new Promise((resolve, reject) => {
          PostRequestWithHeaders({
            url: `${import.meta.env.VITE_APP_BASE_URL}publicapis/CreateLead`,
            body: {
              name: DTO.name,
              email: DTO.email,
              phone_number: DTO.phonenumber ? DTO.phonenumber : '',
              project_id: project_id,
              unit_id: unit_id,
              type: 'unit',
              status: 'new',
              source: 'weblite',
              organization_id: this.organizationDetails._id,
            },
          })
            .then((res) => {
              resolve(res);
            })
            .catch((error) => {
              reject(error);
            });
        });
      }
      return { status: 0, error: 'No data provided' };
    },

    // Save the current route as last visited for the given sidebar option
    saveLastVisitedRoute (optionId, route) {
      this.lastVisitedRoutes[optionId] = route;
    },

    // Get the last visited route for a sidebar option
    getLastVisitedRoute (optionId) {
      return this.lastVisitedRoutes[optionId] || null;
    },

    // Check if a sidebar option has a last visited route
    hasLastVisitedRoute (optionId) {
      return this.lastVisitedRoutes[optionId] !== undefined;
    },

    // Clear all last visited routes
    clearAllLastVisitedRoutes () {
      this.lastVisitedRoutes = {};
    },

    // Clear last visited route for a specific option
    clearLastVisitedRoute (optionId) {
      if (this.lastVisitedRoutes[optionId]) {
        delete this.lastVisitedRoutes[optionId];
      }
    },

  },
});
