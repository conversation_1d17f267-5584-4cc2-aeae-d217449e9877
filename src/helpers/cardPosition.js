export function cardPosition (rect, cursorX, cursorY, cardWidth, cardHeight, masterview) {
  const offsetX = 10;
  const offsetY = 10;
  const newPosition = {
    x: cursorX + offsetX,
    y: cursorY + offsetY,
  };

  const visibleLeft = rect.left;
  const visibleRight = window.innerWidth - rect.right;
  const availableBottomSpace = window.innerHeight - (rect.bottom + cardHeight);
  if (visibleRight >= cardWidth) {
    newPosition.x = rect.right + offsetX;
  } else if (visibleLeft >= cardWidth) {
    newPosition.x = rect.left - cardWidth - offsetX;
  } else if (cursorX + cardWidth + offsetX > window.innerWidth) {
    newPosition.x = cursorX - cardWidth - offsetX;
  }

  if (availableBottomSpace >= 0) {
    if (masterview){
      newPosition.y = rect.bottom - offsetY -120;
    } else {
      newPosition.y = rect.bottom - offsetY -200;
    }
  } else if (rect.top >= cardHeight) {
    newPosition.y = rect.top - cardHeight - offsetY;
  } else if (cursorY - cardHeight >= 0) {
    newPosition.y = cursorY - cardHeight - offsetY;
  } else {
    newPosition.y = window.innerHeight - cardHeight;
  }
  newPosition.x = Math.max(0, Math.min(newPosition.x, window.innerWidth - cardWidth));
  newPosition.y = Math.max(0, Math.min(newPosition.y, window.innerHeight - cardHeight));
  return newPosition;
}
