import * as yup from "yup";

export const RegXEmailPattern = new RegExp(
  /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}))$/,
);
export const onlyNumber = new RegExp("^[0-9]+$");

export const registerinterestSchema = yup.object({
  email: yup
    .string()
    .matches(RegXEmailPattern, "Invalid Email")
    .required("Email ID is required"),
  fullName: yup.string().required("Full Name is required"),
  phone: yup.string().matches(onlyNumber, 'Invalid Phone Number').min(10, 'Phone number must be at least 10 digits').max(15, 'Phone number must be at most 15 digits').required('Phone number is required'),
});
