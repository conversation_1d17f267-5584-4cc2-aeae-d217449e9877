export async function fetchTranslationFromGoogleAPI (text, targetLanguage, organizationId) {
  if (!text) {
    console.warn("No text provided for translation.");
    return text;
  }

  const sourceLanguage = "en";

  if (sourceLanguage === targetLanguage) {
    console.log("Source and target languages are the same. Skipping translation.");
    return text;
  }

  try {
    const url = `${import.meta.env.VITE_APP_BASE_URL}publicapis/organization/${organizationId}/translate` +
                `?sourceLanguageCode=${sourceLanguage}&targetLanguageCode=${targetLanguage}&text=${encodeURIComponent(text)}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorDetails = await response.text();
      throw new Error(`Translation API request failed: ${response.status} - ${errorDetails}`);
    }

    const data = await response.json();

    if (Array.isArray(data) && data.length > 0) {
      return data.map((item) => item[targetLanguage] || text).join(" ");
    }
    console.warn("Unexpected response format:", data);
    return text;

  } catch (error) {
    console.error("API translation failed:", error);
    return text;
  }
}
