@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "Roboto";
  src: url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap");
}

html,
body {
  width: 100%;
  height: 100%;
}

#app {
  height: 100%;
  width: 100%;
  overflow: hidden;
  user-select: none;
  background: #000000;
  font-family: "Roboto";
}

.pnlm-controls-container {
  /* border: 2px solid red; */
  position: absolute;
  top: 0;
  left: 4px;
  z-index: 1;
  display: none;
}

g {
  /* transition: 0.2s linear all; */
}
.areaSVGCls,
.mediaSvgCls,
.landmarkSvgCls,
.locationSvgCls,
.vrTourSVgCls,
.labelsSvgCls {
  cursor: pointer;
}

.routeSvgCls > polyline,
.routeSvgCls > path {
  stroke-dasharray: 10000;
  stroke-dashoffset: 10000;
}
.routeSvgCls.active > polyline,
.routeSvgCls.active > path {
  animation: dash 20s linear forwards;
}
@keyframes dash {
  to {
    stroke-dashoffset: 0;
  }
}

.hideSVGElement {
  display: none !important;
}

g.active,
g.active g {
  opacity: 1;
}

.areaSVGCls.active {
  stroke-width: 3;
  opacity: 1;
}

#app {
  --app-svg-opacity: 1;
}

.UIComponentsDivCls {
  opacity: 0;
  transition: opacity 0.25s linear;
}

.UIComponentsDivCls.active {
  opacity: 1;
}

.svgLayer {
  /* width: auto; */
  height: 100%;
  display: block;
  overflow: auto;
}

@media (orientation: landscape) {
  .svgLayer {
    width: 100%;
    height: 100%;
  }
}

@media only screen and (min-width: 991px) {
  .svgLayer {
    width: 100%;
  }
}

@media only screen and (min-height: 700px) {
  .svgLayer {
    height: 100%;
  }
}

.rect_overlay {
  height: fit-content;
  width: fit-content;
}

/* Global Svg Classes */

.themesvg-tertiary-stroke,
.themesvg-tertiary-stroke > svg,
.themesvg-tertiary-stroke > svg > path {
  stroke: var(--bg-tertiary) !important;
}
.themesvg-tertiary-fill,
.themesvg-tertiary-fill > svg,
.themesvg-tertiary-fill > svg > path {
  fill: var(--bg-tertiary) !important;
}

/* .sample svg path{
  fill: var(--text-secondary) !important;
}
 */

.pnlm-container {
  background: transparent !important;
  color: transparent !important;
}
.pnlm-lmsg {
  display: none;
}
.pnlm-load-box > p {
  display: none;
}
.pnlm-lbar {
  position: fixed;
  top: 50%;
  left: 50%;
  height: 16px;
  border-radius: 100px;
  transform: translate(-50%, -50%);
}
.pnlm-lbar-fill {
  border-radius: 100px;
}
.a-enter-vr-button {
  display: none;
}
.pin>g{
  opacity: 1 !important;
}
