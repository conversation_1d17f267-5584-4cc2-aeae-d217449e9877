<script setup lang="ts">
import { computed } from "vue";

interface ShinyTextProps {
  text: string;
  color?: string;        // base color
  hoverColor?: string;   // hover color
  disabled?: boolean;
  speed?: number;
  className?: string;
}

const props = withDefaults(defineProps<ShinyTextProps>(), {
  text: "",
  color: "var(--primaryText)",
  disabled: false,
  speed: 5,
  className: "",
});

const animationDuration = computed(() => `${props.speed}s`);
</script>

<template>
  <div
    class="shiny-text"
    :class="[
      !props.disabled ? 'animate-shine' : '',
      props.className,
    ]"
    :style="{
      '--shine-color': props.color,
      animationDuration: animationDuration,
    }"
  >
    {{ props.text }}
  </div>
</template>

<style scoped>
.shiny-text {
  text-transparent: transparent;
  background-clip: text;
  -webkit-background-clip: text;
  display: inline-block;
  background-image: linear-gradient(
    120deg,
    var(--shine-color) 40%,
    rgba(255, 255, 255, 0.6) 50%,
    var(--shine-color) 60%
  );
  background-size: 200% 100%;
  transition: all 0.3s ease;
  color: transparent;
}


@keyframes shine {
  0% {
    background-position: 100%;
  }
  100% {
    background-position: -100%;
  }
}

.animate-shine {
  animation: shine linear infinite;
}
</style>
