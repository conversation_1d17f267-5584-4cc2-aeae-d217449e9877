<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, defineComponent, h } from 'vue';
import { useMotionValue, useSpring, useTransform, type SpringOptions } from 'motion-v';
import { creationToolStore } from '../../store';

export type DockItemData = {
  _id?: string;
  icon?: unknown;
  activeIcon?: unknown;
  inactiveIcon?: unknown;
  label: unknown;
  onClick: () => void;
  className?: string;
};

export type DockProps = {
  items: DockItemData[];
  className?: string;
  distance?: number;
  panelHeight?: number;
  baseItemSize?: number;
  dockHeight?: number;
  magnification?: number;
  spring?: SpringOptions;
  orientation?: 'horizontal' | 'vertical';
};

const props = withDefaults(defineProps<DockProps>(), {
  className: '',
  distance: 200,
  panelHeight: 64,
  baseItemSize: 50,
  dockHeight: 256,
  magnification: 40,
  spring: () => ({ mass: 0.1, stiffness: 130, damping: 15 }),
  orientation: 'vertical', // change default as needed
});

const Store = creationToolStore();

const mouseX = useMotionValue(Infinity);
const mouseY = useMotionValue(Infinity);
const isHovered = useMotionValue(0);
const currentHeight = ref(props.panelHeight);

const maxHeight = computed(() =>
  Math.max(props.dockHeight, props.magnification + props.magnification / 2 + 3)
);
const heightRow = useTransform(isHovered, [0, 1], [props.panelHeight, maxHeight.value]);
const height = useSpring(heightRow, props.spring);

let unsubscribeHeight: (() => void) | null = null;
onMounted(() => {
  unsubscribeHeight = height.on('change', (latest: number) => {
    currentHeight.value = latest;
  });
});
onUnmounted(() => {
  if (unsubscribeHeight) unsubscribeHeight();
});

const handleMouseMove = (event: MouseEvent) => {
  isHovered.set(1);
  mouseX.set(event.pageX);
  mouseY.set(event.pageY);
};
const handleMouseLeave = () => {
  isHovered.set(0);
  mouseX.set(Infinity);
  mouseY.set(Infinity);
};

// ------- DockItem -----------
const DockItem = defineComponent({
  name: 'DockItem',
  props: {
    className: { type: String, default: '' },
    onClick: { type: Function, default: () => {} },
    mouseX: { type: Object as () => ReturnType<typeof useMotionValue<number>>, required: true },
    mouseY: { type: Object as () => ReturnType<typeof useMotionValue<number>>, required: true },
    spring: { type: Object as () => SpringOptions, required: true },
    distance: { type: Number, required: true },
    baseItemSize: { type: Number, required: true },
    magnification: { type: Number, required: true },
    item: { type: Object as () => DockItemData, required: true },
    orientation: { type: String as () => 'horizontal' | 'vertical', required: true },
  },
  setup(props) {
    const itemRef = ref<HTMLDivElement>();
    const isHovered = useMotionValue(0);
    const currentSize = ref(props.baseItemSize);

    // Compute mouseDistance based on orientation
    const mouseDistance = useTransform([props.mouseX, props.mouseY], ([x, y]) => {
      const rect = itemRef.value?.getBoundingClientRect() ?? {
        x: 0,
        y: 0,
        width: props.baseItemSize,
        height: props.baseItemSize,
      };
      return props.orientation === 'horizontal'
        ? x - rect.x - props.baseItemSize / 2
        : y - rect.y - props.baseItemSize / 2;
    });

    const springConfig = computed(() => {
      if (props.orientation === 'vertical') {
        return { mass: 0.1, stiffness: 130, damping: 15 };
      }
      return props.spring;
    });

    const targetSize = useTransform(
      mouseDistance,
      [-props.distance, 0, props.distance],
      [props.baseItemSize, props.magnification, props.baseItemSize]
    );

    const size = useSpring(targetSize, springConfig.value);

    let unsubscribeSize: (() => void) | null = null;
    onMounted(() => {
      unsubscribeSize = size.on('change', (latest: number) => {
        currentSize.value = latest;
      });
    });
    onUnmounted(() => {
      if (unsubscribeSize) unsubscribeSize();
    });

    const handleHoverStart = () => isHovered.set(1);
    const handleHoverEnd = () => isHovered.set(0);
    const handleFocus = () => isHovered.set(1);
    const handleBlur = () => isHovered.set(0);

    return {
      itemRef,
      size,
      currentSize,
      isHovered,
      handleHoverStart,
      handleHoverEnd,
      handleFocus,
      handleBlur,
    };
  },
  render() {
    
    // icon fallback logic
    const icon =
      Store.activeOptionId === this.item._id
        ? this.item.activeIcon ?? this.item.icon
        : this.item.inactiveIcon ?? this.item.icon;

    const labelText =
      typeof this.item.label === 'function' ? this.item.label() : this.item.label;
    const iconFallback = !icon;

    const iconNode = iconFallback
      ? h(
          'div',
          {
            class: [
              'w-full h-full flex items-center justify-center',
              Store.activeOptionId === this.item._id ? 'activeStrokeClass' : 'strokeclass'
            ].join(' '),
            style: {
              fontWeight: 700,
              fontSize: '1.25em',
              color: 'inherit',
              textAlign: 'center',
              userSelect: 'none'
            }
          },
          [labelText]
        )
      : (
          typeof icon === 'string'
            ? h('div', {
                class: [
                  'w-full h-full flex items-center justify-center',
                  Store.activeOptionId === this.item._id ? 'activeStrokeClass' : 'strokeclass'
                ].join(' '),
                innerHTML: icon,
              })
            : h(
                'div',
                {
                  class: [
                    'w-full h-full flex items-center justify-center',
                    Store.activeOptionId === this.item._id ? 'activeStrokeClass' : 'strokeclass'
                  ].join(' '),
                },
                [typeof icon === 'function' ? icon() : icon]
              )
        );

    // Compute scale and one-direction translation on hover/magnification
    const scale = Math.min(this.currentSize / this.baseItemSize, 1); // don't grow above magnification
    const translateX = this.orientation === 'vertical' ? 5 : 0;  // rightwards for vertical
    const translateY = this.orientation === 'horizontal' ? -5 : 0; // up for horizontal
    const transformStyle = `scale(${scale}) translate(${translateX}px, ${translateY}px)`;

    // Label (tooltip)
    const labelNode = h(
      DockLabel,
      { isHovered: this.isHovered, orientation: this.orientation },
      () => (typeof labelText === 'string' ? labelText : labelText) // support slots/JSX/strings
    );

    return h(
      'div',
      {
        ref: 'itemRef',
        style: {
          width: icon ? this.currentSize + 'px' : 'auto',
          height: this.currentSize + 'px',
          transform: transformStyle,
          transition: 'transform 0.2s ease',
        },
        onMouseenter: this.handleHoverStart,
        onMouseleave: this.handleHoverEnd,
        onFocus: this.handleFocus,
        onBlur: this.handleBlur,
        onClick: this.onClick,
        class: [
          'relative cursor-pointer inline-flex items-center justify-center',
          icon ? 'rounded-full' : 'rounded-lg',
          Store.activeOptionId === this.item._id ? 'bg-primary' : 'bg-secondary',
          'border-neutral-700 border-2 p-2 shadow-md',
          this.className
        ].join(' '),
        tabindex: 0,
        role: 'button',
        'aria-haspopup': 'true',
      },
      [iconNode, labelNode]
    );
  },
});

// ------- DockLabel (tooltip) ----------
const DockLabel = defineComponent({
  name: 'DockLabel',
  props: {
    className: { type: String, default: '' },
    isHovered: {
      type: Object as () => ReturnType<typeof useMotionValue<number>>,
      required: true,
    },
    orientation: {
      type: String as () => 'horizontal' | 'vertical',
      required: true,
    },
  },
  setup(props) {
    const isVisible = ref(false);
    let unsubscribe: (() => void) | null = null;
    onMounted(() => {
      unsubscribe = props.isHovered.on('change', (latest: number) => {
        isVisible.value = latest === 1;
      });
    });
    onUnmounted(() => {
      if (unsubscribe) unsubscribe();
    });
    return { isVisible };
  },
  render() {
    // Position label above for horizontal, right for vertical
    let styleOverrides = {};
    let transform = 'translateX(-50%)';
    if (this.orientation === 'vertical') {
      styleOverrides = {
        top: '0',
        left: 'calc(100% + 12px)',
        width: 'min-content',
      };
      transform = 'none';
    }
    return h(
      'div',
      {
        class: [
          this.className,
          'absolute z-30',
          this.orientation === 'horizontal'
            ? '-top-8 left-1/2'
            : 'right-0 top-0'
        ].join(' '),
        role: 'tooltip',
        style: {
          ...styleOverrides,
          opacity: this.isVisible ? 1 : 0,
          visibility: this.isVisible ? 'visible' : 'hidden',
          background: '#111',
          color: '#fff',
          borderRadius: '0.375rem',
          border: '1px solid var(--neutral-700, #333)',
          padding: '2px 8px',
          fontSize: '0.75rem',
          pointerEvents: 'none',
          transition: 'opacity 0.2s',
          transform,
        },
      },
      this.$slots.default?.()
    );
  },
});
</script>

<template>
  <div :style="{ height: currentHeight + 'px', scrollbarWidth: 'none' }" class="mx-2 max-w-full relative">
    <div
      @mousemove="handleMouseMove"
      @mouseleave="handleMouseLeave"
      :class="[
        props.className,
        'fixed',
        'z-50',
        props.orientation === 'horizontal' ? 'bottom-4 left-1/2' : 'left-[20px] bottom-1/2',
        'transform',
        'flex',
        props.orientation === 'horizontal'
          ? 'items-end gap-4 rounded-2xl border-neutral-700 border-2 px-2 py-1'
          : 'flex-col gap-4 rounded-2xl border-neutral-700 border-2 py-2 px-1',
      ].join(' ')"
      :style="props.orientation === 'horizontal'
        ? { height: props.panelHeight + 'px', transform: 'translateX(-50%)' }
        : { width: props.panelHeight + 'px', transform: 'translateY(50%)' }
      "
      role="toolbar"
      aria-label="Application dock"
    >
      <DockItem
        v-for="(item, index) in props.items"
        :key="index"
        :onClick="item.onClick"
        :className="item.className"
        :mouseX="mouseX"
        :mouseY="mouseY"
        :spring="props.spring"
        :distance="props.distance"
        :magnification="props.magnification"
        :baseItemSize="props.baseItemSize"
        :orientation="props.orientation"
        :item="item"
      />
    </div>
  </div>
</template>

<style scoped>
.strokeclass svg path {
  stroke: var(--secondaryText, #666);
}
.activeStrokeClass svg path {
  fill: var(--primaryText, #fff);
}
</style>
