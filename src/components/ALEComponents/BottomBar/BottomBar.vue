
<script setup>

import { ref, defineEmits, watch } from 'vue';
import OverflowSlider from '../OverflowSlider/OverflowSlider.vue';
import { cdn } from '../../../helpers/helper';
const emit = defineEmits(['mediaClick', 'selectCategory', 'filteredData', 'toggle']);

const props = defineProps({
  data: {type: Object, default () {
    return {};
  }},
  selectedImageId: {type: String, default: ''},
  categories: {type: Object, default () {
    return {};
  }},
  category: {type: String, default: ''},
  type: {type: String, default: ''},
  open: {type: Boolean, default: true},
});

const open = ref(props.open);
watch(() => props.open, () => {
  open.value = props.open;
});
const imageSlidePerView = ref(null);
const filteredList = ref(null);

/* Methods */
const calculatePerSlideView = (screenWidth, sizePerElem, spaceBetweenRatio) => {
  const calculatePerSlideView =  Math.floor(Number(screenWidth / (sizePerElem + spaceBetweenRatio)));
  return Number(calculatePerSlideView + '.' + 5);
};

const calculateSpaceBetween = (length, sizePerElem) => {
  if (screen.width < 640){
    // Mobile devices
    if ((length * sizePerElem) > screen.width){
      imageSlidePerView.value = calculatePerSlideView(screen.width, sizePerElem, 10);
    } else {
      imageSlidePerView.value = null;
    }
  } else {
    // Above 640 devices
    const elemWidth = (Number(screen.width)/100)*60; // Container width
    if ((length * sizePerElem) > elemWidth){
      imageSlidePerView.value = calculatePerSlideView(elemWidth, sizePerElem, 10);
    } else {
      imageSlidePerView.value = null;
    }
  }
};

function getFilteredData (category) {
  if (category === "Show All"){
    filteredList.value = Object.values(props.data);
    calculateSpaceBetween(Object.keys(props.data).length, 74);
  } else {
    filteredList.value = Object.values(props.data).filter((item) => item.category.toLowerCase() === category.toLowerCase());
    calculateSpaceBetween(filteredList.value.length, 74);
  }
  emit('filteredData', filteredList.value);
}
getFilteredData (props.category);
watch(() => props.category, () => {
  getFilteredData (props.category);
});

watch(() => props.category, () => {
  getFilteredData(props.category);
});
</script>

<template>
  <div :class="['sm:w-[60%] sm:-translate-x-[-33%] w-full left-0 transition-all z-20 transition-timing-function:cubic-bezier(0.45, 0.05, 0.55, 0.95); duration-200',(open === true ? 'bottom-0' : !categories.length ? '-bottom-[165px] sm:-bottom-[131px]' : '-bottom-[286px] sm:-bottom-[220px] z-[2]' ),(type.toLowerCase() == 'amenities' ? 'sm:hidden fixed' : 'fixed')]">
    <button
      v-if="!open"
      class="ml-auto mr-5 sm:hidden flex gap-2 items-center py-3 px-8 rounded-[41px] bg-black bg-opacity-20 backdrop-blur-[80px] text-white text-xs font-medium absolute right-0"
      :class="!categories.length ? 'bottom-[16.4rem]' : 'bottom-96'"
      :onclick="() => open = !open"
    >
      <span class="capitalize">{{ type }}</span>
      <span>
        <svg
          class="w-[19px] h-[18px]"
          viewBox="0 0 19 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M2.3002 16.0895L9.5002 10.4477L16.7002 16.0895L16.7002 14.1015L9.5002 8.45969L2.3002 14.1015L2.3002 16.0895Z"
            fill="#FEFEFE"
            fill-opacity="0.5"
          />
          <path
            d="M2.3002 10.3296L9.5002 4.6878L16.7002 10.3296L16.7002 8.34153L9.5002 2.69974L2.3002 8.34153L2.3002 10.3296Z"
            fill="white"
          />
        </svg>
      </span>
    </button>
    <button
      class="w-16 h-8 pt-2 pb-1 rounded-tl-[20px] rounded-tr-[20px] bg-white bg-opacity-75 sm:block hidden m-auto"
      :onclick="() => open = !open"
    >
      <svg
        :class="['m-auto transition-all ease-linear duration-200 w-[18px] h-[18px]',(!open ? 'rotate-180' : '')]"
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1.75 1.85694L8.75 7.34201L15.75 1.85693L15.75 3.78977L8.75 9.27484L1.75 3.78977L1.75 1.85694Z"
          fill="#151515"
          fill-opacity="0.5"
        />
        <path
          d="M1.75 7.45703L8.75 12.9421L15.75 7.45703L15.75 9.38987L8.75 14.8749L1.75 9.38987L1.75 7.45703Z"
          fill="#ACACAC"
        />
      </svg>
    </button>
    <div :class="{ 'translate-y-[-11rem]': !open, 'translate-y-0': open }">
      <slot />
    </div>
    <div
      class="drawer-proj-list h-auto text-sidebarHeaderText rounded-tr-[20px] rounded-tl-[20px] sm:rounded-[34px] backdrop-blur-[100px] sm:mb-6"
    >
      <div class="flex sm:hidden px-5 pt-6 pb-5 justify-between items-center">
        <p class="text-lg font-medium capitalize">
          {{ type }}
        </p>
        <button :onclick="() => {open = !open;emit('toggle');console.log('inside close')}">
          <svg
            class="w-6 h-6"
            viewBox="0 0 27 27"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M20.25 20.25L6.75 6.75"
              stroke="white"
              stroke-width="1.575"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M20.25 6.75L6.75 20.25"
              stroke="white"
              stroke-width="1.575"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>

      <div class="sm:pt-4 ps-5 pe-5 sm:ps-8 sm:pe-8 pb-4 ">
        <OverflowSlider
          :slides-per-view="imageSlidePerView ? imageSlidePerView : 'auto'"
          direction="horizontal"
          :mousewheel="true"
          space-between="5"
          class="w-full"
          :lazy-loading="true"
        >
          <template #options>
            <swiper-slide
              v-for="item,index in filteredList"
              :key="index"
              lazy="true"
              :class="['relative shrink-0 rounded-md w-[74px] h-[74px] overflow-hidden cursor-pointer',(selectedImageId === item._id ? 'border-2 border-primary' : 'border-2 border-transparent')]"
              @click="()=>{$emit('mediaClick',item);if(type === 'amenities')open=!open}"
            >
              <img
                :src="cdn(item.thumbnail)"
                alt=""
                :class="['w-full h-[74px]',(selectedImageId === item._id ? '' : 'opacity-50' )]"
                loading="lazy"
              >
              <div
                v-if="item.type=='video'"
                class="absolute top-0 left-0 flex w-full h-full"
              >
                <svg
                  class="m-auto w-[17px] h-[17px]"
                  viewBox="0 0 17 17"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clip-path="url(#clip0_141_2381)">
                    <path
                      d="M3.71176 0.534479C2.20917 -0.327429 0.990967 0.378656 0.990967 2.1103V14.8891C0.990967 16.6225 2.20917 17.3276 3.71176 16.4666L14.8811 10.061C16.3842 9.19882 16.3842 7.80191 14.8811 6.9399L3.71176 0.534479Z"
                      fill="#999999"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_141_2381">
                      <rect
                        width="16.6666"
                        height="16.6666"
                        fill="white"
                        transform="translate(0.166504 0.166992)"
                      />
                    </clipPath>
                  </defs>
                </svg>
              </div>
              <div
                v-if="item.type=='360'"
                class="absolute top-0 left-0 flex w-full h-full"
              >
                <svg
                  class="m-auto w-[17px] h-[17px]"
                  viewBox="0 0 17 17"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clip-path="url(#clip0_141_2357)">
                    <path
                      d="M8.68579 16.823C9.96029 16.6419 11.0776 15.3117 11.7806 13.3472C10.7643 13.1196 9.72715 12.9975 8.68579 12.9829V16.823Z"
                      fill="#999999"
                    />
                    <path
                      d="M10.7226 16.5108C10.7979 16.489 10.873 16.467 10.9474 16.4429C11.0103 16.4226 11.0722 16.4008 11.1342 16.3792C11.2075 16.3538 11.2805 16.3278 11.3532 16.3002C11.4152 16.2766 11.4766 16.2516 11.5381 16.2266C11.6091 16.1968 11.6799 16.1671 11.75 16.1373C11.8112 16.1104 11.872 16.0822 11.9328 16.0538C12.0016 16.0214 12.0701 15.9884 12.138 15.954C12.1977 15.9236 12.2571 15.8927 12.3168 15.8608C12.3835 15.825 12.4498 15.7885 12.5157 15.751C12.5754 15.7172 12.6328 15.6834 12.6908 15.6485C12.7554 15.6087 12.8197 15.569 12.8836 15.5293C12.9407 15.493 12.9977 15.456 13.054 15.4179C13.1168 15.3756 13.1788 15.3315 13.2405 15.2875C13.2959 15.2478 13.351 15.2082 13.4051 15.1684C13.4647 15.1224 13.5258 15.0748 13.5856 15.0271C13.6387 14.9848 13.6916 14.9428 13.7438 14.8993C13.8033 14.8498 13.8603 14.7986 13.9181 14.7474C13.9687 14.7025 14.0196 14.658 14.069 14.6119C14.1259 14.5588 14.1813 14.5038 14.2367 14.4493C14.2847 14.4019 14.3333 14.3552 14.3803 14.3066C14.389 14.2976 14.3973 14.2879 14.4063 14.2785C13.7566 13.943 13.0723 13.6796 12.3653 13.4932C11.9977 14.6657 11.3433 15.7283 10.4617 16.5844C10.4858 16.5781 10.51 16.573 10.5341 16.5668C10.5978 16.5489 10.6603 16.5292 10.7226 16.5108Z"
                      fill="#999999"
                    />
                    <path
                      d="M16.7214 8.80469H13.15C13.1365 10.1992 12.9341 11.5854 12.5486 12.9256C13.3348 13.1356 14.0936 13.4376 14.8092 13.8252C15.984 12.4092 16.6566 10.6436 16.7214 8.80469Z"
                      fill="#999999"
                    />
                    <path
                      d="M8.68579 8.20918H12.5541C12.539 6.864 12.3416 5.52711 11.9677 4.23486C10.8905 4.48051 9.79042 4.6117 8.68579 4.62625V8.20918Z"
                      fill="#999999"
                    />
                    <path
                      d="M8.68579 0.19043V4.03051C9.72715 4.01582 10.7643 3.89379 11.7806 3.66618C11.0776 1.70171 9.96029 0.371504 8.68579 0.19043Z"
                      fill="#999999"
                    />
                    <path
                      d="M8.68579 12.3876C9.79042 12.4023 10.8905 12.5335 11.9677 12.7792C12.3416 11.4869 12.539 10.1499 12.5541 8.80469H8.68579V12.3876Z"
                      fill="#999999"
                    />
                    <path
                      d="M14.8092 3.18799C14.0936 3.57559 13.3348 3.87752 12.5486 4.08754C12.9341 5.42778 13.1365 6.81398 13.15 8.20847H16.7214C16.6564 6.36966 15.9839 4.60415 14.8092 3.18799Z"
                      fill="#999999"
                    />
                    <path
                      d="M14.4069 2.73625C14.3982 2.72738 14.3899 2.71778 14.3813 2.70861C14.3342 2.66004 14.2854 2.6132 14.2374 2.56594C14.1821 2.5114 14.127 2.45627 14.07 2.40333C14.0206 2.35737 13.9696 2.31389 13.919 2.26807C13.8612 2.21688 13.8035 2.16525 13.7442 2.1158C13.6927 2.07231 13.6399 2.03071 13.5875 1.98897C13.5279 1.94068 13.4668 1.89269 13.4054 1.84658C13.3516 1.80601 13.2971 1.76674 13.2422 1.72747C13.18 1.68267 13.1175 1.63802 13.054 1.59541C12.9983 1.55788 12.9418 1.52123 12.8852 1.48531C12.8208 1.44386 12.7565 1.40328 12.6909 1.36401C12.6334 1.32954 12.5754 1.29551 12.5172 1.26249C12.4506 1.22439 12.3839 1.18745 12.3165 1.15138C12.2569 1.12156 12.1974 1.08913 12.1378 1.05931C12.0693 1.0247 12.0002 0.991391 11.9294 0.958667C11.8697 0.930306 11.8092 0.902672 11.7478 0.875766C11.6773 0.844496 11.606 0.81628 11.5349 0.786465C11.4735 0.761449 11.4125 0.736724 11.3505 0.713162C11.2782 0.685529 11.2049 0.65964 11.1316 0.634333C11.0696 0.612517 11.0077 0.590847 10.9449 0.570485C10.8704 0.546342 10.7959 0.52438 10.7194 0.502273C10.6575 0.484093 10.5952 0.465622 10.5325 0.449042C10.5085 0.442497 10.4841 0.437406 10.46 0.431152C11.3416 1.28722 11.9958 2.34966 12.3636 3.52236C13.0714 3.3359 13.7567 3.07236 14.4069 2.73625Z"
                      fill="#999999"
                    />
                    <path
                      d="M0.0544434 8.20847H3.62589C3.63942 6.81398 3.84173 5.42778 4.22715 4.08754C3.44104 3.87767 2.68227 3.57573 1.9667 3.18799C0.791829 4.604 0.11931 6.36966 0.0544434 8.20847Z"
                      fill="#999999"
                    />
                    <path
                      d="M8.0902 16.823V12.9829C7.04884 12.9976 6.0117 13.1196 4.99536 13.3472C5.69828 15.3117 6.8157 16.6419 8.0902 16.823Z"
                      fill="#999999"
                    />
                    <path
                      d="M8.09046 8.80469H4.22217C4.23729 10.1499 4.43451 11.4869 4.80859 12.7792C5.88572 12.5334 6.98569 12.4022 8.09046 12.3876V8.80469Z"
                      fill="#999999"
                    />
                    <path
                      d="M8.0902 0.19043C6.8157 0.371504 5.69828 1.70171 4.99536 3.66618C6.0117 3.89394 7.04884 4.01596 8.0902 4.03051V0.19043Z"
                      fill="#999999"
                    />
                    <path
                      d="M8.09046 4.62625C6.98584 4.6117 5.88572 4.48051 4.80859 4.23486C4.43451 5.52711 4.23729 6.864 4.22217 8.20918H8.09046V4.62625Z"
                      fill="#999999"
                    />
                    <path
                      d="M6.31414 0.430664C6.29 0.436918 6.26585 0.442008 6.24171 0.448262C6.17859 0.465134 6.11634 0.483895 6.05351 0.502076C5.97875 0.523892 5.90458 0.545853 5.82953 0.569706C5.76626 0.590213 5.70401 0.612029 5.64177 0.6337C5.5689 0.659443 5.49589 0.68504 5.42375 0.712383C5.3615 0.736236 5.30013 0.760961 5.23846 0.786267C5.16763 0.816083 5.09665 0.845898 5.0267 0.875568C4.96532 0.902475 4.90467 0.93069 4.84388 0.959051C4.77508 0.991485 4.70658 1.0245 4.63866 1.05882C4.57903 1.08922 4.5194 1.12035 4.45991 1.15205C4.39315 1.18783 4.32698 1.22448 4.26124 1.262C4.20161 1.29531 4.143 1.32949 4.08555 1.3644C4.02083 1.40337 3.95683 1.44366 3.89313 1.48351C3.83582 1.52016 3.77896 1.5571 3.72267 1.59521C3.65984 1.63753 3.59788 1.6816 3.53622 1.72567C3.4808 1.76538 3.42568 1.80494 3.37143 1.84493C3.31049 1.89075 3.25232 1.93845 3.19094 1.98601C3.138 2.02833 3.08462 2.07037 3.03285 2.11414C2.97321 2.1633 2.91664 2.21421 2.85919 2.26511C2.80828 2.31049 2.75694 2.35456 2.7072 2.40154C2.65063 2.45433 2.5955 2.50916 2.54024 2.56356C2.49224 2.61097 2.44337 2.6578 2.39625 2.70652C2.38767 2.71554 2.37938 2.72543 2.37036 2.7346C3.0199 3.07027 3.70435 3.33367 4.41133 3.52012C4.77901 2.34816 5.43291 1.28629 6.31414 0.430664Z"
                      fill="#999999"
                    />
                    <path
                      d="M2.53869 14.447C2.59411 14.5016 2.64923 14.5567 2.7061 14.6098C2.75555 14.6556 2.80645 14.6991 2.85721 14.7449C2.91495 14.7963 2.97269 14.8477 3.03203 14.8972C3.08352 14.9404 3.13602 14.9821 3.18867 15.024C3.2483 15.0723 3.30779 15.1203 3.37091 15.1667C3.42457 15.207 3.47912 15.246 3.53366 15.286C3.5959 15.3306 3.65873 15.3753 3.72215 15.4182C3.77785 15.4557 3.83443 15.4923 3.891 15.5284C3.95543 15.5697 4.01972 15.6103 4.08531 15.6496C4.14276 15.6842 4.20079 15.7181 4.25897 15.7512C4.32558 15.7893 4.39234 15.8263 4.45968 15.8622C4.51916 15.892 4.57879 15.9244 4.63843 15.9543C4.70693 15.9889 4.77601 16.0222 4.84684 16.055C4.90647 16.0831 4.96698 16.1107 5.02835 16.1378C5.09889 16.1691 5.17001 16.1973 5.24128 16.2271C5.30265 16.2521 5.36374 16.2768 5.4257 16.3004C5.49798 16.3282 5.57128 16.3541 5.64459 16.3794C5.70654 16.401 5.7685 16.4229 5.83133 16.4431C5.9058 16.4672 5.98026 16.4893 6.05677 16.5113C6.11872 16.5295 6.18097 16.5479 6.24351 16.5647C6.26766 16.5712 6.29209 16.5763 6.31623 16.5826C5.43457 15.7263 4.78038 14.6639 4.41256 13.4912C3.70557 13.6778 3.02112 13.9412 2.37158 14.277C2.38031 14.2859 2.3886 14.2955 2.39733 14.3047C2.44198 14.3529 2.49012 14.3998 2.53869 14.447Z"
                      fill="#999999"
                    />
                    <path
                      d="M1.9667 13.8252C2.68227 13.4376 3.44104 13.1356 4.22715 12.9256C3.84173 11.5854 3.63942 10.1992 3.62589 8.80469H0.0544434C0.119455 10.6435 0.791829 12.409 1.9667 13.8252Z"
                      fill="#999999"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_141_2357">
                      <rect
                        width="16.6666"
                        height="16.6666"
                        fill="white"
                        transform="translate(0.0544434 0.166504)"
                      />
                    </clipPath>
                  </defs>
                </svg>
              </div>
              <div
                v-if="item.name"
                class="w-full absolute bottom-0 left-0 px-2 pb-2 overflow-hidden text-xs font-medium text-center whitespace-nowrap text-ellipsis"
              >
                {{ item.name }}
              </div>
            </swiper-slide>
          </template>
        </OverflowSlider>
      </div>

      <div
        v-if="categories && Object.keys(categories).length !== 0"
        class="border border-stone-300 border-opacity-50"
      />

      <div
        v-if="categories && Object.keys(categories).length !== 0"
        class="pt-4 sm:pt-5 ps-5 sm:ps-8 pb-6 sm:pb-4 font-medium w-full"
      >
        <OverflowSlider
          slides-per-view="auto"
          direction="horizontal"
          :mousewheel="true"
          space-between="10"
          class="w-full"
          :initial-slide="category ? categories.findIndex((e)=>e==category) : 0"
        >
          <template #options>
            <swiper-slide
              v-for="item,index in categories"
              :key="index"
              :class="['rounded-[50px] px-[57px] py-3 w-fit flex items-center justify-center capitalize border hover:cursor-pointer',(category == (type === 'amenities' ? item.category : item) ? 'border-primary bg-primary text-secondary' : 'bg-black bg-opacity-40 backdrop-blur-[20px]')]"
              @click="$emit('selectCategory',(type === 'amenities' ? item.category : item));"
            >
              {{ item.category ? item.category : item }} <span v-if="type.toLowerCase()=='amenities'">&nbsp;({{ item.count }})</span>
            </swiper-slide>
          </template>
        </OverflowSlider>
      </div>
    </div>
  </div>
</template>

<style scoped>
.drawer-proj-list {
@apply text-white h-full flex-col-reverse overflow-y-auto overflow-x-hidden items-center justify-center;
height: auto;
}

.drawer-proj-list{
background: rgba(0, 0, 0, 0.60);
backdrop-filter: blur(50px);
-webkit-backdrop-filter: blur(50px);
background-blend-mode: luminosity;
border: 1.4px solid rgba(255, 255, 255, 0.40);
}

.invisibleScroll::-webkit-scrollbar {
width: 0px;
height:0px;
}

.invisibleScroll::-webkit-scrollbar-track {
background: transparent;
}

.invisibleScroll::-webkit-scrollbar-thumb {
background: transparent;
}
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  border-radius:8px;
}

/* Track */
::-webkit-scrollbar-track {
  background: transparent;
    border-radius:8px;

}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #e9e9e9;
    border-radius:8px;

}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: white;
    border-radius:8px;
    width: 10px;
  height: 10px;

}

</style>
