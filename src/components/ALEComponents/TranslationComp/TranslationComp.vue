<template>
  <TranslationTest
    v-if="Store.translationMap"
    :text="text"
  />

  <div
    v-else
    :class="[
      'skeleton-loader',
      {
        'skeleton-circle': circle,
        'skeleton-rectangular': variant === 'rectangular',
        'skeleton-text': variant === 'text',
      }
    ]"
    :style="dynamicStyles"
  />
</template>

<script setup>

import { creationToolStore } from "../../../store/index";
import TranslationTest from "./TranslationTest.vue";

// const props = defineProps({
//   text: { type: String, default: "" },
//   variant: {
//     type: String,
//     default: "text",
//     validator: (value) => ["text", "rectangular", "circle"].includes(value),
//   },
//   circle: { type: Boolean, default: false },
//   width: { type: [String, Number], default: null },
//   height: { type: [String, Number], default: null },
// });

//   const resultText = ref("");

const Store = creationToolStore();

</script>

  <style scoped>
  .skeleton-loader {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
  }

  .skeleton-circle {
    border-radius: 50%;
  }

  .skeleton-rectangular {
    border-radius: 4px;
  }

  .skeleton-text {
    border-radius: 4px;
  }

  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
  </style>
