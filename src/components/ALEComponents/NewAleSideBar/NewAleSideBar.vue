<script setup>
import { defineEmits, ref, computed, onMounted, onUnmounted } from 'vue';
import { sidebar_icons } from "../../../config/masterdata";
import { creationToolStore } from '../../../store';
import { Googleanalytics } from '../../../helpers/helper';
import { onClickOutside } from '@vueuse/core';
import TranslationComp from '../TranslationComp/TranslationComp.vue';
import { useRoute } from 'vue-router';
import Dock from '../../Dock/Dock.vue';
import {h} from 'vue';
const route = useRoute();
const Store = creationToolStore();
const list = ref();
const emit = defineEmits(['selectOption', 'closeX']);

const items = [
  {
    icon: () => h('i', { class: 'pi pi-home', style: { fontSize: '18px', color: 'white' } }),
    label: 'Home',
    onClick: () => console.log('Home clicked!'),
  },
  {
    icon: () => h('i', { class: 'pi pi-inbox', style: { fontSize: '18px', color: 'white' } }),
    label: 'Archive',
    onClick: () => console.log('Archive clicked!'),
  },
  {
    icon: () => h('i', { class: 'pi pi-user', style: { fontSize: '18px', color: 'white' } }),
    label: 'Profile',
    onClick: () => console.log('Profile clicked!'),
  },
  {
    icon: () => h('i', { class: 'pi pi-cog', style: { fontSize: '18px', color: 'white' } }),
    label: 'Settings',
    onClick: () => console.log('Settings clicked!'),
  },
];
const props = defineProps({
  sidebarList: {type: Object, default () {
    return {};
  }},
});

const startTime = ref(new Date), timespent = ref(0);
const showMore = ref(false);
const morePanelRef = ref(null);

const isMoreMenuOpen = computed({
  get: () => showMore.value,
  set: (val) => {
    showMore.value = val;
  },
});

const availableHeight = ref(window.innerHeight * 0.85); // 85vh as per your sidebar

const updateAvailableHeight = () => {
  availableHeight.value = window.innerHeight * 0.85;
};

onMounted(() => {
  window.addEventListener('resize', updateAvailableHeight);
});
onUnmounted(() => {
  window.removeEventListener('resize', updateAvailableHeight);
});

function selectOption (id, scene_id, type){
  var endtime = new Date();
  var difference = Math.abs(startTime.value - endtime) / 1000;
  timespent.value = difference;
  startTime.value=new Date();

  // Save current route as last visited for the current active option
  if (Store.activeOptionId && Store.activeOptionId !== id) {
    const currentRoute = {
      name: route.name,
      params: { ...route.params },
      query: { ...route.query },
      fullPath: route.fullPath,
    };
    Store.saveLastVisitedRoute(Store.activeOptionId, currentRoute);
  }

  // Store previous URL when type is 'unitplan' or 'gallery'
  if (type === 'unitplan' || type === 'gallery' || type === 'map') {
    const previousRoute = {
      name: route.name,
      params: { ...route.params },
      query: { ...route.query },
      fullPath: route.fullPath,
    };
    Store.saveLastVisitedRoute('previous_' + type, previousRoute);
  }

  Googleanalytics("menu_clicked", {
    menu_name: props.sidebarList[id].name,
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
  });
  Store.activeOptionId = id;
  emit('selectOption', scene_id, type, id);
  isMoreMenuOpen.value = false;
}

const initialSidebarList = computed(() => {
  const list = Object.values(props.sidebarList || {}).filter((item) => !item.hide);
  if (Store.isLandscape) {
    // Show only first 4 in sidebar, rest go to More
    return list.slice(0, 3);
  }
  if (Store.isMobile) {
    return list.slice(0, 4);
  }
  return list.slice(0, 5);
});

const additionalSidebarList = computed(() => {
  const list = Object.values(props.sidebarList || {}).filter((item) => !item.hide);
  if (Store.isLandscape) {
    return Object.values(list).slice(3);
  }
  if (Store.isMobile) {
    return Object.values(list).slice(4);
  }
  return Object.values(list).slice(5);
});

const isAnyAdditionalItemSelected = computed(() => {
  return additionalSidebarList.value.some((item) => item._id === Store.activeOptionId);
});

const gridClasses = computed(() => {
  const visibleCount = initialSidebarList.value.length;
  const hasMoreButton = additionalSidebarList.value.length > 0;
  const totalVisible = hasMoreButton ? visibleCount + 1 : visibleCount;
  if (Store.isLandscape) {
    // Estimate how many items can fit
    const availableHeight = window.innerHeight - parseInt(getComputedStyle(document.documentElement).getPropertyValue('--navbar-height') || 56);
    const itemHeight = 48; // px, reduced for compact sidebar
    const canFit = totalVisible * itemHeight <= availableHeight;
    if (canFit) {
      const columnClasses = { 5: 'grid-rows-5', 4: 'grid-rows-4', 3: 'grid-rows-3', 2: 'grid-rows-2', 1: 'grid-rows-1', 0: 'grid-rows-0' };
      return 'grid ' + (columnClasses[totalVisible] || '');
    }
    return 'flex flex-col';

  }
  const columnClasses = { 5: 'grid-cols-5', 4: 'grid-cols-4', 3: 'grid-cols-3', 2: 'grid-cols-2', 1: 'grid-cols-1', 0: 'grid-cols-0' };
  return 'grid ' + columnClasses[totalVisible];
});

onClickOutside(list, () => showMore.value = false);
onClickOutside(morePanelRef, () => {
  if (isMoreMenuOpen.value) {
    isMoreMenuOpen.value = false;
  }
});
console.log("Sidebar List:", initialSidebarList);

const newList = initialSidebarList.value.map((item) => {
  return {
    ...item,
    label: item.name,
    onClick: () => selectOption(item._id, item.scene_id, item.type),
    activeIcon: sidebar_icons[item.icon_id].active,
    inactiveIcon: sidebar_icons[item.icon_id].inactive
  };
});

</script>

<template>
  <div>
    <Dock
        :items="newList"
        :panel-height="68"
        :base-item-size="50"
        :magnification="70"
        :distance="200"
        :dock-height="256"
        :orientation="(Store.isLandscape || Store.isMobile) ? 'horizontal' : 'vertical'"
        :spring="{ mass: 0.1, stiffness: 150, damping: 12 }"
      />
   
  </div>
</template>

<style scoped>
.textColors{
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent;
}

.invisibleScroll::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}

.invisibleScroll::-webkit-scrollbar-track {
  background: transparent;
}
</style>

<style lang="scss">
.strokeclass svg path {
  stroke:var(--secondaryText);
}
.activeStrokeClass svg path{
  fill: var(--primaryText);
}
</style>
