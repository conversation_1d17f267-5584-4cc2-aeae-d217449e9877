<script setup>
import { ref, defineProps, watch, computed } from 'vue';
import { statusColorMap } from '../../../config/masterdata';
import TranslationComp from '../TranslationComp/TranslationComp.vue';
import { creationToolStore } from '../../../store';
import { getOrderedBedroomLegend } from '../../../helpers/helper';

const Store = creationToolStore();
const props = defineProps({
  unitData: {
    type: Array,
    default () {
      return [];
    },
  },
});

const uniqueBedroomsList = ref([]), orderedBedroomList = ref([]);
const units = ref(props.unitData);

async function fetchData () {
  try {
    const uniqueBedroomsSet = new Set([]);
    Object.keys(units.value).forEach(function (key) {
      const data = units.value[key];
      uniqueBedroomsSet.add(data.bedroom);
    });
    const uniqueBedrooms = Array.from(uniqueBedroomsSet);
    return uniqueBedrooms;
  } catch (error) {
    return [];
  }
}

fetchData().then(async (uniqueBedrooms) => {
  const studios = uniqueBedrooms.filter((bedroom) => bedroom === 'studio');
  const nonStudios = uniqueBedrooms.filter((bedroom) => bedroom !== 'studio');
  uniqueBedroomsList.value = [...studios, ...nonStudios];
  orderedBedroomList.value = await getOrderedBedroomLegend(uniqueBedroomsList.value);
});

function getBedroomClass (bedrooms) {
  if (!bedrooms) {
    return `w-3 h-3 bg-secondary rounded-full border border-white backdrop-blur-lg md:w-5 md:h-5`;
  }
  const { bg } = statusColorMap[bedrooms] || { bg: 'bg-secondary' };
  return `w-3 h-3 ${bg} rounded-full backdrop-blur-lg md:w-5 md:h-5`;
}

// Computed property to format the bedroom number
const formattedBedrooms = computed(() => {
  return (bedrooms) => {
    if (!bedrooms) {
      return '';
    }
    const bedroomsLower = bedrooms.toLowerCase();

    const nonNumericTypes = ["studio", "penthouse", "townhouse", "plot", "duplex", "suite"];
    if (nonNumericTypes.includes(bedroomsLower)) {
      return bedrooms;
    }
    const match = bedroomsLower.match(/(\d+(?:\.\d+)?)bhk/i);
    if (match) {
      const value = match[1];
      return !Store.isMobile && !Store.isLandscape? `${value} Bedroom` : `${value} BR`;
    }

    return bedrooms;
  };
});
watch(props.unitData, () => {
  units.value = props.unitData;
  fetchData().then( async (uniqueBedrooms) => {
    const studios = uniqueBedrooms.filter((bedroom) => bedroom === 'studio');
    const nonStudios = uniqueBedrooms.filter((bedroom) => bedroom !== 'studio');
    uniqueBedroomsList.value = [...studios, ...nonStudios];
    orderedBedroomList.value = await getOrderedBedroomLegend(uniqueBedroomsList.value);
  });
});
</script>

<template>
  <div
    v-if="orderedBedroomList.length > 0"
    class="absolute left-1/2 transform -translate-x-1/2 md:bottom-8 h-fit z-[1]"
    :class="Store.isLandscape? 'top-12':'top-16 sm:top-5 '"
  >
    <div
      class=" w-fit h-7 px-4 py-2 bg-opacity-40 bg-secondary md:rounded-lg rounded-full backdrop-blur-[20px] justify-start items-start gap-3 inline-flex md:h-9"
    >
      <div
        v-for="item in orderedBedroomList"
        :key="item"
      >
        <div class="justify-start items-center gap-2 flex">
          <div
            style="fill-opacity:0.3 !important;"
            :class="getBedroomClass(item)"
          />
          <div class="text-secondaryText text-xs font-medium whitespace-nowrap leading-tight md:text-sm">
            <p class="md:block hidden capitalize">
              <TranslationComp :text="formattedBedrooms(item)" />
            </p>
            <p class="md:hidden block capitalize">
              <TranslationComp :text="formattedBedrooms(item)" />
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
