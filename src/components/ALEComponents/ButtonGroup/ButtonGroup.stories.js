import ButtonGroup from './ButtonGroup.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/ButtonGroup',
  component: ButtonGroup,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    'floorNames': {
      'Basement': {
        'name': 'Basement',
      },
      'Ground_Floor': {
        'name': 'Ground Floor',
      },
      'First_Floor': {
        'name': 'First Floor',
      },
    },
  },

  parameters:
  {
    design:
    {
      type: "figma",
      url: "https://www.figma.com/design/oumtOpooHvw6pGpb8JkAqs/Client-Delivery-PropVR-Lite?node-id=12-7945&t=2wirUUj0F4FSZr4u-4",
    },
  },
};
