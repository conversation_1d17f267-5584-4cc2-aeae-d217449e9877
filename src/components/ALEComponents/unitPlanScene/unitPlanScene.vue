<script setup>
import { ref, defineProps, watch } from 'vue';
import { creationToolStore } from '../../../store';
import { useRoute } from 'vue-router';
import router from '../../../router';
import { loadImageData, cdn } from '../../../helpers/helper';
import OverflowSlider from '../OverflowSlider/OverflowSlider.vue';
// import BadgeComp from '../Badge/BadgeComp.vue';
import RotatableOverlay from '../../svgOverlay/rotatableOverlay.vue';
const emit = defineEmits(['removeLoader']);
const Store = creationToolStore();
const route = useRoute();
const loaderProgress = ref(0);
const galHighResdata = ref(false);
const props = defineProps({
  filteredList: {type: Object, default: () => ({})},
  selectedMediaIndex: {type: Number},
  isVisible: {type: <PERSON>olean},
  activeTab: {type: Number},
  showScene: {type: Boolean},
  scene_data: {type: Object, default: () => ({})},
});
const localSelectedMediaIndex = ref(props.selectedMediaIndex);

const handlePrev = () => {
  router.push({ name: "unit.Child", query: { ...route.query, current_gallery: localSelectedMediaIndex.value } });
  document.getElementById('galleryImagesSlider').swiper.slidePrev(); // Previous
};

const handleNext = () => {
  router.push({ name: "unit.Child", query: { ...route.query, current_gallery: localSelectedMediaIndex.value } });
  document.getElementById('galleryImagesSlider').swiper.slideNext(); // Next
};

const handleSwiperSlideChange = (val) => {
  loaderProgress.value=0;
  const currentIndex = val.detail[0].realIndex;
  localSelectedMediaIndex.value = currentIndex + 1;
  // Circularloader.value = true;
  galHighResdata.value = false;
};

const loadHighRes = (imgURL) => {
  loaderProgress.value = 50;
  loadImageData(imgURL, (progress) => {
    loaderProgress.value=50+(progress/2);
  }, () => loaderProgress.value=false).then((highRes) => {
    galHighResdata.value = highRes;
  });
};

watch(() => localSelectedMediaIndex.value, (newVal) => {
  localSelectedMediaIndex.value = newVal;
});

// watch(() => galHighResdata.value, (newVal) => {
//     galHighResdata.value = newVal
// })
function removeLoader (){
  emit('removeLoader');
}

</script>

<template>
  <div
    v-if="filteredList.length > 0"
    class=""
  >
    <!-- <BadgeComp
      :title="filteredList[localSelectedMediaIndex - 1].name"
      class="absolute z-40 left-4"
      :class="Store.isMobile ? 'top-14' : 'top-10 left-1/2 -translate-x-1/2 transform'"
    /> -->
    <div
      class="flex flex-row fixed  left-1/2 transform -translate-x-1/2 sm:transform-none sm:right-5 sm:bottom-8 items-center gap-1 justify-start z-10 "
      :class="Store.isMobile ? !isVisible ? 'bottom-44' : 'bottom-24' : 'bottom-[10%]'"
    >
      <button
        :disabled="(localSelectedMediaIndex - 1) === 0 ? true : false"
        class="h-11 w-11 border-transparent text-white bg-black bg-opacity-40 backdrop-blur-[20px] flex justify-center items-center rounded-[50%] cursor-pointer  disabled:opacity-15 disabled:cursor-default"
        @click="handlePrev"
      >
        <svg
          class="w-6 h-6"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M21 12L3 12"
            stroke="white"
            stroke-width="1.4"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M8 17L3 12L8 7"
            stroke="white"
            stroke-width="1.4"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>

      <div
        class="h-11 w-11 bg-black flex justify-center items-center rounded-[50%] cursor-pointer text-white text-sm font-Roboto"
      >
        {{ localSelectedMediaIndex }}/{{ filteredList.length }}
      </div>

      <button
        :disabled="localSelectedMediaIndex === filteredList.length ? true : false"
        class="h-11 w-11  flex justify-center items-center rounded-[50%] cursor-pointer border-transparent text-white bg-black bg-opacity-40 backdrop-blur-[20px] disabled:opacity-50 disabled:cursor-default"
        @click="handleNext"
      >
        <svg
          class="w-6 h-6"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M3 12L21 12"
            stroke="white"
            stroke-width="1.4"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M16 7L21 12L16 17"
            stroke="white"
            stroke-width="1.4"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>
    <OverflowSlider
      id="galleryImagesSlider"
      slidesPerView="1"
      direction="horizontal"
      space-between="3"
      class=" absolute top-0 left-0 w-full h-full"
      :initial-slide="activeTab"
      Keyboard="true"
      allow-touch-move="false"
      @swiper-slide-change="handleSwiperSlideChange"
    >
      <template #options>
        <swiper-slide
          v-for="item, index in filteredList"
          :key="index"
        >
          <!-- Image -->
          <div
            class="relative overflow-hidden bg-no-repeat text-center h-full w-full bg-cover"
            :style="{ backgroundImage: `url('${cdn(item.thumbnail)}')` }"
          >
            <div class="h-full w-full bg-black bg-opacity-40 backdrop-blur-[20px]">
              <div
                v-if="(localSelectedMediaIndex - 1) === index"
                class="flex justify-center items-center h-full w-full relative"
              >
                <img
                  v-if="!galHighResdata"
                  class="m-auto sm:h-full sm:w-auto h-auto w-full "
                  :src="cdn(item.thumbnail)"
                  :alt="item.name"
                  @load="loadHighRes(item.url)"
                >
                <img
                  v-if="galHighResdata"
                  class="m-auto sm:h-full sm:w-auto h-auto w-full "
                  :src="galHighResdata"
                  :alt="item.name"
                >
              </div>
            </div>
          </div>
        </swiper-slide>
      </template>
    </OverflowSlider>
  </div>
  <RotatableOverlay
    v-if="showScene"
    ref="containerRef"
    if="containerRef"
    :data="scene_data"
    style="height:100%;width:100%"
    bucket-u-r-l="propvr-in-31420.appspot.com"
    replace-u-r-l="storagecdn.propvr.ai"
    @remove-loader="removeLoader"
  />
</template>
