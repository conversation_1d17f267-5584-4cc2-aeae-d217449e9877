<script setup>
import { ref, onMounted } from 'vue';
import { cdn } from '../../../helpers/helper';
import InfoModal from '../InfoModal/InfoModal.vue';
import infoSvg from '../../../../public/assets/infoModal/rotate_white.svg';

defineProps({
  videoSrc: {
    type: String,
    required: true,
  },
});

const isPlaying = ref(false);
const videoEl = ref(null);
const sceneEl = ref(null);

const playVideo = () => {
  if (videoEl.value) {
    videoEl.value.play();
    isPlaying.value = true;
  }
};

onMounted(() => {
  if (videoEl.value) {
    videoEl.value.load();
    videoEl.value.pause();
  }
});
</script>

<template>
  <div
    class="video-360-container"
    @click="playVideo"
  >
    <a-scene
      ref="sceneEl"
      embedded
      class="h-[100vh] sample"
      loading-screen="enabled: false"
      vr-mode-ui="enabled: false"
      device-orientation-permission-ui="enabled: false"
    >
      <a-assets>
        <video
          id="video360"
          ref="videoEl"
          :src="cdn(videoSrc)"
          crossorigin="anonymous"
          loop
          playsinline
        />
      </a-assets>
      <a-sky :src="'#video360'" />
      <a-entity
        camera
        look-controls="enabled: true; reverseMouseDrag: true"
        :animation="isPlaying ? 'property: rotation; to: 0 -360 0; loop: true; dur: 100000; easing: linear' : ''"
      />
    </a-scene>

    <InfoModal
      v-if="!isPlaying"
      :infoText="'Click and drag to explore the 360-degree view'"
      :infoIcon="infoSvg"
    />
  </div>
</template>

<style scoped>
.video-360-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}
</style>
