<script setup>
import { defineProps, defineEmits  } from 'vue';
import { FwbCard, FwbP } from 'flowbite-vue';
import TranslationComp from '../TranslationComp/TranslationComp.vue';
import AnimatedContent from '../../../animations/AnimatedContent/AnimatedContent.vue';
defineProps({
  name: {type: String, default: ""},
  description: {type: String, default: ""},
  distance: {type: String, default: ""},
  carTiming: {type: String, default: ""},
  transitTiming: {type: String, default: ""},
  walkTiming: {type: String, default: ""},
  landmarkId: {type: String, default: ""},
  thumbnail: {type: String, default: ""},
  isMobile: {type: String, default: ""},
});

const emit = defineEmits(['closeCard']);
const onClickClose = () => {
  emit('closeCard', true);
};
</script>

<template>
    <AnimatedContent
    :distance="100"
    direction="horizontal"
    :reverse="false"
    :duration="0.8"
    ease="power3.out"
    :initial-opacity="0"
    :animate-opacity="true"
    :scale="1"
    :threshold="0.1"
    :delay="0"
  >
  <div class="cardContainer sm:w-fit w-full h-fit px-2 flex flex-row">
    <FwbCard
      :img-alt="name"
      variant="horizontal"
      class="h-fit card !border-gray-700 !flex-row !rounded-lg !bg-secondary w-full sm:w-fit flex"
      @click="onClickClose"
    >
      <div class="flex flex-row">
        <img
          :src="thumbnail"
          class="h-inherit rounded-lg"
        >
        <div class="flex flex-col h-full relative w-full sm:w-fit gap-2 justify-between ml-3 p-3">
          <span class="text-secondaryText text-base font-bold leading-normal">
            <TranslationComp
              :text="name"
            />
          </span>
          <div
            v-if="description && description.trim() !== ''"
            class="text-primary max-h-[75px] overflow-y-auto scroll-smooth scroll-6 text-justify pr-4"
          >
            <TranslationComp
              :text="description"
            />
          </div>
          <div class="grid grid-cols-3 items-center sm:flex-start gap-5 h-fit w-full sm:w-fit">
            <div
              v-if="distance && distance !== 0"
              class=" flex flex-row items-center gap-2"
            >
              <span>
                <svg
                  class="h-6 w-6 landmarkcardsvg"
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                >
                  <path
                    d="M3.38233 12.3607C3.96301 11.5716 5.19881 9.71044 5.19881 8.40019C5.19881 7.23883 4.26076 6.30078 3.09941 6.30078C1.93805 6.30078 1 7.23883 1 8.40019C1 9.71046 2.25067 11.5716 2.81648 12.3607C2.96542 12.5543 3.24822 12.5543 3.3823 12.3607H3.38233ZM2.10186 8.3853C2.10186 7.81948 2.56339 7.37288 3.11427 7.37288C3.66514 7.37288 4.12668 7.83442 4.12668 8.3853C4.12668 8.95111 3.66514 9.39771 3.11427 9.39771C2.56339 9.39781 2.10186 8.95111 2.10186 8.3853Z"
                    fill="white"
                  />
                  <path
                    d="M13.3281 7.05994C13.9088 6.27081 15.1446 4.40966 15.1446 3.09941C15.1446 1.93805 14.2066 1 13.0452 1C11.8838 1 10.9458 1.93805 10.9458 3.09941C10.9458 4.40968 12.1965 6.27081 12.7623 7.05994C12.8964 7.25353 13.1792 7.25353 13.3281 7.05994ZM12.0328 3.09941C12.0328 2.53359 12.4943 2.08699 13.0452 2.08699C13.611 2.08699 14.0576 2.54853 14.0576 3.09941C14.0576 3.66522 13.5961 4.11182 13.0452 4.11182C12.4943 4.11192 12.0328 3.66522 12.0328 3.09941Z"
                    fill="white"
                  />
                  <path
                    d="M13.0452 7.96777H8.6678C7.68509 7.96777 6.88112 8.77174 6.88112 9.75445C6.88112 10.7372 7.68509 11.5411 8.6678 11.5411H9.99294C10.4694 11.5411 10.8565 11.9282 10.8565 12.4047C10.8565 12.8812 10.4694 13.2683 9.99294 13.2683H3.09923C2.84613 13.2683 2.6377 13.4767 2.6377 13.7298C2.6377 13.9829 2.84614 14.1914 3.09923 14.1914H9.99294C10.9757 14.1914 11.7796 13.3874 11.7796 12.4047C11.7796 11.422 10.9757 10.618 9.99294 10.618H8.66769C8.19119 10.618 7.80412 10.2309 7.80412 9.75443C7.80412 9.27794 8.19121 8.89085 8.66769 8.89085H13.0451C13.2982 8.89085 13.5067 8.68241 13.5067 8.42931C13.5067 8.17622 13.2982 7.96777 13.0451 7.96777H13.0452Z"
                    fill="white"
                  />
                </svg>
              </span>
              <fwb-p
                class="text-secondaryText text-xs font-normal leading-[18px] mb-0 !whitespace-nowrap"
              >
                {{ distance }}
                <TranslationComp
                  text="KM"
                />
              </fwb-p>
            </div>

            <div
              v-if="carTiming && carTiming !== 0"
              class="flex flex-row items-center gap-2"
            >
              <span>
                <svg
                  class="h-6 w-6 landmarkcardsvg"
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                >
                  <g clip-path="url(#clip0_510_12327)">
                    <path
                      d="M15.9421 8.67411C15.822 7.32691 15.6243 7.06454 15.5497 6.96586C15.378 6.73783 15.1033 6.5888 14.8126 6.43245C14.7961 6.42375 14.7819 6.41136 14.7711 6.39624C14.7603 6.38112 14.7531 6.3637 14.7502 6.34534C14.7472 6.32697 14.7486 6.30818 14.7541 6.29043C14.7597 6.27269 14.7693 6.25648 14.7822 6.24309C14.8361 6.18849 14.8774 6.12288 14.9035 6.05077C14.9296 5.97866 14.9397 5.90176 14.9332 5.82536C14.9207 5.68988 14.8577 5.56405 14.7567 5.47284C14.6558 5.38164 14.5242 5.3317 14.3882 5.33295H13.8681C13.8458 5.33308 13.8235 5.33453 13.8014 5.33729C13.7862 5.3307 13.7705 5.32546 13.7544 5.32162C13.4464 4.67052 13.0246 3.77906 12.1505 3.34399C10.854 2.69923 8.46563 2.66589 7.9989 2.66589C7.53216 2.66589 5.14381 2.69923 3.84895 3.34299C2.97482 3.77806 2.55309 4.66952 2.24505 5.32062L2.24238 5.32595C2.22702 5.32811 2.21193 5.33191 2.19738 5.33729C2.17525 5.33453 2.15299 5.33308 2.1307 5.33295H1.60962C1.47357 5.3317 1.34201 5.38164 1.24104 5.47284C1.14008 5.56405 1.07708 5.68988 1.06454 5.82536C1.05862 5.90158 1.06923 5.97819 1.09564 6.04994C1.12205 6.12169 1.16364 6.18689 1.21756 6.24109C1.23047 6.25448 1.24008 6.27069 1.24564 6.28843C1.25121 6.30618 1.25257 6.32497 1.24963 6.34333C1.24669 6.3617 1.23952 6.37912 1.22869 6.39424C1.21786 6.40936 1.20367 6.42175 1.18723 6.43045C0.896517 6.5878 0.620476 6.73683 0.450118 6.96386C0.37544 7.06387 0.178078 7.32491 0.0577267 8.67211C-0.00894978 9.43022 -0.0189512 10.215 0.0333898 10.7204C0.143073 11.7706 0.34877 12.4053 0.357438 12.4317C0.389002 12.5275 0.447104 12.6124 0.524999 12.6766C0.602893 12.7407 0.697385 12.7815 0.797502 12.794V12.8007C0.797502 12.9422 0.853701 13.0779 0.953735 13.1779C1.05377 13.2779 1.18944 13.3341 1.33091 13.3341H3.19786C3.33932 13.3341 3.475 13.2779 3.57503 13.1779C3.67507 13.0779 3.73127 12.9422 3.73127 12.8007C4.01831 12.8007 4.21801 12.7494 4.4297 12.6947C4.73534 12.6123 5.04775 12.5576 5.36317 12.531C6.38032 12.434 7.39181 12.4007 7.9989 12.4007C8.59365 12.4007 9.6498 12.434 10.6686 12.531C10.9853 12.5576 11.2989 12.6126 11.6058 12.6954C11.8085 12.7474 12.0008 12.7954 12.2672 12.8004C12.2672 12.9419 12.3234 13.0775 12.4234 13.1776C12.5235 13.2776 12.6591 13.3338 12.8006 13.3338H14.6675C14.809 13.3338 14.9447 13.2776 15.0447 13.1776C15.1448 13.0775 15.201 12.9419 15.201 12.8004V12.7964C15.3013 12.784 15.3961 12.7434 15.4742 12.6792C15.5524 12.6151 15.6107 12.53 15.6424 12.434C15.651 12.4077 15.8567 11.7729 15.9664 10.7227C16.0187 10.217 16.0094 9.43356 15.9421 8.67411ZM3.20919 5.77668C3.4759 5.20993 3.78094 4.56851 4.32402 4.29813C5.1088 3.90741 6.73538 3.73138 7.9989 3.73138C9.26241 3.73138 10.889 3.90608 11.6738 4.29813C12.2168 4.56851 12.5206 5.21027 12.7886 5.77668L12.8219 5.84903C12.8414 5.89021 12.8501 5.93571 12.847 5.98117C12.8439 6.02663 12.8292 6.07054 12.8043 6.10872C12.7795 6.1469 12.7452 6.17807 12.7049 6.19926C12.6645 6.22044 12.6195 6.23094 12.5739 6.22975C11.4661 6.19975 9.13239 6.10373 7.9989 6.10373C6.8654 6.10373 4.53172 6.20208 3.42222 6.23208C3.37667 6.23328 3.33158 6.22278 3.29124 6.20159C3.2509 6.1804 3.21666 6.14923 3.19178 6.11106C3.16691 6.07288 3.15223 6.02896 3.14915 5.9835C3.14607 5.93804 3.15469 5.89255 3.17419 5.85136C3.18585 5.82669 3.19819 5.80169 3.20919 5.77668ZM3.60692 8.43141C3.03343 8.50043 2.45628 8.5345 1.87866 8.53342C1.52528 8.53342 1.16089 8.43341 1.09321 8.1187C1.04687 7.907 1.05187 7.78798 1.07688 7.6683C1.09788 7.56661 1.13122 7.4926 1.29791 7.4666C1.73131 7.39992 1.97368 7.4836 2.68311 7.69263C3.15352 7.83099 3.4929 8.01535 3.68626 8.16137C3.78327 8.23338 3.7316 8.42141 3.60692 8.43141ZM10.9873 11.1651C10.5486 11.2152 9.67114 11.1968 8.0089 11.1968C6.34665 11.1968 5.46952 11.2152 5.03079 11.1651C4.57806 11.1148 4.00097 10.6867 4.39503 10.3054C4.6574 10.054 5.26949 9.86595 6.08461 9.76027C6.89973 9.65459 7.24478 9.60025 8.00556 9.60025C8.76634 9.60025 9.07639 9.63358 9.92651 9.7606C10.7766 9.88762 11.4191 10.078 11.6161 10.3057C11.9755 10.7137 11.4397 11.1121 10.9873 11.1671V11.1651ZM14.9046 8.11836C14.8379 8.43441 14.4712 8.53309 14.1191 8.53309C13.5305 8.53323 12.9423 8.49917 12.3575 8.43107C12.2555 8.42141 12.2082 8.24238 12.3115 8.16103C12.5019 8.01135 12.8449 7.83065 13.3147 7.6923C14.0241 7.48327 14.4332 7.39959 14.7846 7.46927C14.8702 7.48627 14.9156 7.57828 14.9209 7.63596C14.9444 7.79655 14.9389 7.96006 14.9046 8.1187V8.11836Z"
                      fill="white"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_510_12327">
                      <rect
                        width="16"
                        height="16"
                        fill="white"
                      />
                    </clipPath>
                  </defs>
                </svg>
              </span>
              <fwb-p
                class="text-secondaryText text-xs font-normal leading-[18px]  mb-0 !whitespace-nowrap"
              >
                {{ carTiming >= 60 ? Math.floor(carTiming / 60) + ' hr ' : '' }}{{ Math.floor(carTiming % 60) }}

                <TranslationComp
                  text="min"
                />
              </fwb-p>
            </div>

            <div
              v-if="transitTiming && transitTiming !== 0"
              class="flex flex-row items-center gap-2"
            >
              <span>
                <svg
                  class="h-6 w-6 landmarkcardsvg"
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                >
                  <path
                    d="M14.605 9.0895C13.9205 4.9815 10.401 2 6.237 2H1V4H3.5C3.776 4 4 4.224 4 4.5V7.5C4 7.776 3.776 8 3.5 8H1V12H12.139C13.6845 12 14.859 10.6115 14.605 9.0895ZM11.8605 7.7505C11.771 7.905 11.606 8 11.428 8H6C5.724 8 5.5 7.776 5.5 7.5V4.5C5.5 4.224 5.724 4 6 4H6.237C8.559 4 10.7145 5.246 11.862 7.2515C11.9505 7.4065 11.95 7.5965 11.8605 7.7505Z"
                    fill="white"
                  />
                  <path
                    d="M15 14H1V13H15V14Z"
                    fill="white"
                  />
                </svg>
              </span>
              <fwb-p
                class="text-secondaryText text-xs font-normal leading-[18px]  mb-0 !whitespace-nowrap"
              >
                {{ transitTiming >= 60 ? Math.floor(transitTiming / 60) + ' hr ' : '' }}{{ Math.floor(transitTiming % 60) }}
                min
              </fwb-p>
            </div>
          </div>
        </div>
      </div>
    </FwbCard>
  </div>
  </AnimatedContent>
</template>
<style lang="scss">
.cardContainer{
  .card img{
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    border-top-right-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
    width:95px !important;
    // height:88px !important;
    object-fit:cover;
  }
  .card>img+div{
    padding: 16px;
  }

::-webkit-scrollbar-track
{
	border: 5px solid rgb(255, 255, 255);
  border-radius: 15px;
}

::-webkit-scrollbar
{
	width: 5px;
  border-radius: 15px;
}

::-webkit-scrollbar-thumb
{
	background-color: #acacac;
	border-radius: 15px;
}
 .card p{
    text-wrap: balance;
  }
  .card>div{
    width: 100%;
  }
}

.landmarkcardsvg path{
  stroke:var(--secondaryText);
  fill:var(--secondaryText);
}
</style>
