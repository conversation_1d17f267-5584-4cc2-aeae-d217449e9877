import LandMarkCard from './LandMarkCard.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/LandMarkCard',
  component: LandMarkCard,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    name: "Buckingham palace",
    description: "World's Highest Observation Wheel in Dubai, It stands at 250 m (820 ft) tall. It opened in October 2021",
    distance: "3",
    carTiming: "2",
    transitTiming: "4",
    walkTiming: "8",
    thumbnail: "https://imgs.search.brave.com/2fgXaIVWvD19O3AY7rum-qBw7s5CuwNzMLnn9xjYj1k/rs:fit:860:0:0/g:ce/aHR0cHM6Ly9pbWFn/ZXMudW5zcGxhc2gu/Y29tL3Bob3RvLTE1/NzIzNzA2NjYxMjAt/ZDE3MWRmNmQ4NDcz/P3E9ODAmdz0xMDAw/JmF1dG89Zm9ybWF0/JmZpdD1jcm9wJml4/bGliPXJiLTQuMC4z/Jml4aWQ9TTN3eE1q/QTNmREI4TUh4elpX/RnlZMmg4Tkh4OFlu/VmphMmx1WjJoaGJT/VXlNSEJoYkdGalpY/eGxibnd3Zkh3d2ZI/eDhNQT09",
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=918-2491&mode=design&t=3sAHVBNuISnpywTC-4",
    },
  },
};
