import TabsView from './TabsView.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/TabsView',
  component: TabsView,
  tags: ['autodocs'],
};

export const Primary = {
  args: {
    data: ['floor plan', '360 tour' ],
    defaultId: "floor plan",
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/3ScwDHZPtk2FRiRLsSU2T2/ALE-V.2?type=design&node-id=135-11600&mode=dev",
    },
  },
};
