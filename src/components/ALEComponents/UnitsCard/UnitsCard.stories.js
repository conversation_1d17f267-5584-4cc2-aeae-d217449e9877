import UnitsCard from './UnitsCard.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/UnitsCard',
  component: UnitsCard,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    unitNo: 456,
    availabilityStatus: true,
    isFavourite: true,
    area: 2500,
    floor: 1,
    bed: 2,
    thumbnail:
        "https://www.makemyhouse.com/blogs/wp-content/uploads/2023/04/garden.webp",
  },
  parameters:
  {
    design:
    {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=312-43610&mode=design&t=MJoHYvcCnJNPKlHr-4",
    },
  },
};
