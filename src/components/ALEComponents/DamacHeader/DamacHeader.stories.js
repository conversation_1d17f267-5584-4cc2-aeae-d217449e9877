import DamacHeader from './DamacHeader.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/DamacHeader',
  component: DamacHeader,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    propertyData: {
      "unit-104":
          {
            "name": "unit -104",
            "status": "Available",
            "units": 4,
          },
      "unit-562": {
        "name": "unit -562",
        "status": "Sold",
        "units": 5,
      },
      " unit-529": {
        "name": "unit -529",
        "status": "Available",
        "units": 8,
      },
      "unit-362": {
        "name": "unit -362",
        "status": "",
        "units": 1,
      },
      "unit-114": {
        "name": "unit -114",
        "status": "Sold",
        "units": 9,
      },
      "unit -262": {
        "name": "unit -262",
        "status": "",
        "units": 2,
      },
      "unit -100": {
        "name": "unit -100",
        "status": "Available",
        "units": 9,
      },
      "unit -562": {
        "name": "unit -562",
        "status": "",
        "units": 4,
      },

    },
    count: 2,

  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=918-1036&mode=design&t=MJoHYvcCnJNPKlHr-4",
    },
  },
};
