<script setup>
import { defineProps, defineEmits, ref, onMounted } from 'vue';
import svgButton from '../svgButton/svgButton.vue';
import SearchComponent from '../SearchComponent/SearchComponent.vue';
import GoToFavoriteBtn from '../GoToFavoriteBtn/GoToFavoriteBtn.vue';
import { creationToolStore } from '../../../store';

const Store = creationToolStore();
const emit = defineEmits(['goToProj', 'Gotounit', 'openFavourites']);
const props = defineProps({
  propertyData: {
    type: Object,
    default () {
      return {};
    },
  },
  count: {
    type: Number,
    default: 0,
  },
});
const noAddFavoritesToolTip = ref(false);
const properties = Object.values(props.propertyData);

function goToProject () {
  emit('goToProj' );
}

function gotoFavourites (){
  noAddFavoritesToolTip.value = false;
  emit('openFavourites');
  // If (Object.keys(Store.favoritesData).length > 0 ){
  // } else {
  //   NoAddFavoritesToolTip.value =   !noAddFavoritesToolTip.value;
  // }
}

function favoriteViewTooltipClickHandler (event) {
  if (event.target.id !== 'goToFavotiteText' && event.target.id !== 'goToFavotiteSvg' && event.target.id !== 'goToFavorite'){
    noAddFavoritesToolTip.value = false;
  }
}

onMounted(() => {
  document.addEventListener('click', favoriteViewTooltipClickHandler);
});

</script>

<template>
  <div class="h-[3.75rem] w-full bg-damacHeaderBg px-6 flex items-center ">
    <div class=" w-full flex items-center justify-between">
      <svg
        class="h-4 w-fit"
        xmlns="http://www.w3.org/2000/svg"
        width="134"
        height="16"
        viewBox="0 0 134 16"
        fill="none"
      >
        <path
          d="M133.714 0H121.906C118.446 0 115.499 0.695427 113.075 2.07936C110.65 3.46329 108.902 5.40599 107.835 7.90226C107.157 9.48859 106.964 10.94 107.256 12.2322C107.6 13.6023 108.457 14.5832 109.848 15.1783C111.113 15.7128 113.242 15.9844 116.213 15.9844H126.799L127.858 13.4484H121.893C119.02 13.4484 117.105 13.0851 116.15 12.362C115.107 11.5783 115.224 9.76365 116.036 7.83825C117.46 4.51162 120.434 2.43918 124.545 2.43918H129.169L128.108 4.94237H131.617L133.714 0.00172992V0ZM92.5073 9.60623L99.6325 2.81457L101.113 9.60623H92.5073ZM31.9087 9.63045L38.8484 3.13115L40.101 9.63045H31.9087ZM99.0229 0.0138393V0.0259488H94.6786L77.9831 15.3236L84.5093 0.0311385H77.3523L68.7078 6.66364L64.2556 10.2428H64.0525L66.1478 5.02714L63.5949 0.0311385H56.4043L49.8675 15.3547L46.1008 0.0311385H38.2212L20.819 15.9983H25.1687L29.6173 11.8223H40.5816L41.4543 15.9983H53.1059L57.3177 6.1291C57.5297 5.63434 57.7329 5.0652 57.9308 4.42686C57.9502 4.94237 58.0933 5.48903 58.353 6.06682L62.0578 14.2995H62.685L73.2818 6.09796C73.6793 5.79003 74.2818 5.233 75.0839 4.42859C74.6722 5.17245 74.3878 5.73813 74.2199 6.13083L70.0081 16H77.7004V15.9948H85.8114L90.2105 11.7998H101.599L102.512 15.9948H106.516L102.609 0.0138393H99.0229ZM25.8736 7.92129C26.9372 5.42675 26.8453 3.48924 25.5998 2.10358C24.3525 0.724835 21.9992 0.0311385 18.5435 0.0311385H6.84074L5.78425 2.53087H13.8423C17.9517 2.53087 19.1442 4.53584 17.7255 7.85728C16.9058 9.77922 16.1814 11.5455 14.4712 12.3326C12.9059 13.054 11.2893 13.265 8.40959 13.265L4.68358 13.2581L7.46793 6.79165H3.95569L0 15.9983H10.6074C13.5826 15.9983 15.9288 15.7284 17.6619 15.1904C19.5576 14.5936 21.2572 13.6144 22.7571 12.2513C24.1564 10.9521 25.1952 9.50935 25.8736 7.92129Z"
          fill="white"
        />
      </svg>
      <div class="flex gap-4">
        <div class="flex gap-4 justify-center items-center">
          <SearchComponent
            class="w-fit"
            :properties="properties"
            @gotounit="(id) => emit('Gotounit',id)"
          />

          <div class="relative">
            <GoToFavoriteBtn
              id="goToFavorite"
              :count="Object.keys(Store.favoritesData).length > 0 ? Object.keys(Store.favoritesData).length : 0"
              @go-to-favourites="gotoFavourites"
            />

            <div
              v-if="noAddFavoritesToolTip"
              class="w-fit h-fit  px-3 py-2 bg-damacHeaderDropDownBg bg-opacity-75 rounded-[1.8rem] justify-center items-center whitespace-nowrap inline-flex absolute top-[3.4rem] md:-left-3 -left-6 z-10"
            >
              <p class="text-center text-damacHeaderDropDownWhite text-sm font-medium">
                No favourites added.
              </p>
            </div>
          </div>
        </div>

        <svgButton
          class="border-gray-700 border-2 text-xs font-normal !p-2 !rounded h-9 "
          theme="dark"
          title="Get in touch"
          @go-to="goToProject( )"
        >
          <template #svg>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              class="w-4.5 h-4.5 "
            >
              <path
                d="M3.33325 6.66667H16.6666"
                stroke="white"
                stroke-width="1.4"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M15.8333 3.33333H4.16659C3.70635 3.33333 3.33325 3.70643 3.33325 4.16667V15.8333C3.33325 16.2936 3.70635 16.6667 4.16659 16.6667H15.8333C16.2935 16.6667 16.6666 16.2936 16.6666 15.8333V4.16667C16.6666 3.70643 16.2935 3.33333 15.8333 3.33333Z"
                stroke="white"
                stroke-width="1.4"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M13.3333 1.66667V3.33333"
                stroke="white"
                stroke-width="1.4"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M6.66675 1.66667V3.33333"
                stroke="white"
                stroke-width="1.4"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </template>
        </svgButton>
      </div>
    </div>
  </div>
</template>
