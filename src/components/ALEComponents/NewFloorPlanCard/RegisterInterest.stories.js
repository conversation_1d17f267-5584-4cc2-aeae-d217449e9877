import RegisterInterest from './RegisterInterest.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/RegisterInterest',
  component: RegisterInterest,
  tags: ['autodocs'],
  argTypes: {
    unitId: {
      control: 'text',
      description: 'Unit ID for the interest registration',
    },
    unitName: {
      control: 'text',
      description: 'Unit name for display',
    },
  },
  parameters: {
    layout: 'fullscreen',
  },
};

export const Primary = {
  args: {
    unitId: 'unit-123',
    unitName: 'Unit A-456',
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=918-6595&mode=dev",
    },
  },
};

export const WithoutUnitInfo = {
  args: {
    unitId: '',
    unitName: '',
  },
};

export const LongUnitName = {
  args: {
    unitId: 'unit-789',
    unitName: 'Premium Penthouse Unit B-789 with Garden View',
  },
};

export const Mobile = {
  args: {
    unitId: 'unit-456',
    unitName: 'Unit C-456',
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
};
