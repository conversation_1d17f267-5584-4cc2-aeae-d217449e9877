<script setup>
import { ref, defineProps, defineEmits, watch, computed } from "vue";
import { useRoute } from "vue-router";
import StatusPill from "../StatusPill/StatusPill.vue";
import TranslationComp from "../TranslationComp/TranslationComp.vue";
import { creationToolStore } from "../../../store";
import { formatBedrooms, Googleanalytics, thousandSeparator, getCookie } from "../../../helpers/helper";
import { FwbButton } from "flowbite-vue";
import CurrencyComp from "../CurrencyComp/CurrencyComp.vue";

// Props definition
const props = defineProps({
  unitPlans: { type: Array, required: true },
  unitName: { type: String, required: true },
  location: { type: String, required: true },
  isMobile: Boolean,
  hideClose: Boolean,
  bedroomNumber: { type: String, default: "" },
  unitType: { type: String, default: "" },
  status: { type: String, default: "" },
  price: { type: String, default: "" },
  maxPrice: { type: String, default: "" },
  measurement: { type: String, default: "" },
  measurementType: { type: String, default: "" },
  balcony: { type: String, default: "" },
  balconyType: { type: String, default: "" },
  suiteArea: { type: String, default: "" },
  suiteAreaType: { type: String, default: "" },
  currencyTitle: { type: String, default: "" },
  totalArea: { type: String, default: "" },
  favUnits: { type: Array, default: () => [] },
  unitId: { type: String, default: "" },
  styleType: { type: String, default: " " },
  ctaLink: { type: String, default: "" },
  hideStatus: { type: Boolean, default: false},
  favKey: { type: String, default: "unit_id"},
  is_Commercial: { type: Boolean, default: false},
  unitplanCta: { type: Boolean, default: false },
});
const ancestorOrigins = window.location.ancestorOrigins[0];
const isonsalestool = !['https://dashboard.propvr.tech', 'https://propvr.tech', 'https://pixelstreaming.damaclabs.com'].includes(ancestorOrigins);
// Emits
const emits = defineEmits(["closeModal", "handleFavorite", "handleCta"]);

// Reactive variables
const switchs = ref(false);
const Store = creationToolStore();
const route = useRoute();
const showContent = ref(true), showDefault = ref(), cardDetails = ref([]);
const selectedCurrency = ref(Store.currencyData.currency || getCookie('selectedCurrency'));

if (Store.projectCardData[route.params.projectId]?.projectSettings.ale.unit_card_customize_type){
  cardDetails.value = Store.projectCardData[route.params.projectId].projectSettings.ale.unitcard_config;
} else {
  showDefault.value = true;
}

// Ensure project data is loaded
if (Object.keys(Store.projectCardData).length === 0) {
  Store.getListofProjects(route.params.organizationId, route.params.projectId);
}

// Helper methods
const closeModal = () => {
  switchs.value = !switchs.value;
  emits("closeModal");
};

watch(() => Store.currencyData, (newCurrency) => {
  selectedCurrency.value = newCurrency.currency;
},
{ deep: true });

// Computed or utility functions
const getBedroomLabel = (bedroomNumber) => {
  const types = {
    studio: "Studio",
    penthouse: "Penthouse",
    townhouse: "Townhouse",
    duplex: "Duplex",
    suite: "Suite",
  };
  if (types[bedroomNumber]) {
    return types[bedroomNumber];
  }
  const match = String(bedroomNumber).match(/(\d+)BHK/i)?.[1];
  return match ? `${match} Bedroom` : "";
};

const minizier = () => {
  showContent.value = !showContent.value;
};
const handleCta = () => {
  emits('handleCta', Store.projectCardData[route.params.projectId].projectSettings.ale.cta_type);
  Googleanalytics('cta_clicked', {
    cta_name: Store.projectCardData[route.params.projectId].projectSettings.ale.cta_name,
    unit_name: props.unitName,
    unit_id: props.unitId,
    project_city: Store.projectCardData?.[route.params.projectId]?.city,
    project_type: Store.buildingData[route.query.building_id]?.category,
    bedroom_number: formatBedrooms(props.bedroomNumber),
    unit_size: props.measurement,
    measurement_type: props.measurementType,
    unit_price: props.price,
    currency_type: props.currencyTitle,
    availability_tag: props.status,
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
  });
};

const validUnitPlans = computed(() =>
  props.unitPlans.filter((unit) =>
    unit.measurement !== 0,
  ),
);

</script>

<template>
  <div
    class="flex w-full flex-col gap-1 justify-between z-50 text-secondaryText rounded-lg shadow-lg "
    :class="[switchs?'!items-end  !bg-none border-none !bottom-3':'bg-secondary p-3' , Store.isMobile || Store.isLandscape? 'bottom-20 -mb-4':'relative']"
  >
    <div v-if="!switchs">
      <!-- title  -->
      <div class="flex flex-col justify-between items-center min-w-[18.45rem]">
        <div class="w-full flex justify-between items-center gap-5">
          <h2
            v-if="!is_commercial"
            class="text-sm text-secondaryText font-semibold whitespace-nowrap capitalize"
          >
            <TranslationComp
              :key="bedroomNumber"
              :text="getBedroomLabel(bedroomNumber)"
            />
          </h2>
          <p
            v-if="is_commercial"
            class="text-sm text-secondaryText font-semibold whitespace-nowrap capitalize"
          >
            <TranslationComp
              :key="unitName"
              :text="unitName"
            />
            <span v-if="location"> , </span>
            <TranslationComp
              v-if="location"
              :text="location"
            />
          </p>
          <div class="flex gap-3">
            <StatusPill
              v-if="!hideStatus && status && (cardDetails.status || showDefault)"
              :availability-status="status"
              class="text-xs"
            />
            <button
              v-if="!unitplanCta"
              class=" h-8 w-8 p-2 bg-tertiary50opacity  rounded-full flex justify-start items-center gap-2 "
              @click="()=>{
                emits('handleFavorite')}"
            >
              <svg
                class="w-[1.125rem] h-4"
                viewBox="0 0 18 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9 14.6666C9 14.6666 1.5 10.4895 1.5 5.47693C1.5 0.464333 7.33333 0.0466183 9 3.96848C10.6667 0.0466183 16.5 0.464333 16.5 5.47693C16.5 10.4895 9 14.6666 9 14.6666Z"
                  :stroke="Object.values(favUnits).some(innerObj => innerObj[favKey] === unitId)?'#FF4747':'#FFFFFF'"
                  stroke-width="1.4"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  :fill="Object.values(favUnits).some(innerObj => innerObj[favKey] === unitId)?'#FF4747':'none'"
                />
              </svg>
            </button>
            <div
              v-if="(isMobile || Store.isLandscape) && hideClose===false "
              class="h-7 w-7 "
              @click="closeModal()"
            >
              <svg
                class="w-7 h-7"
                viewBox="0 0 27 27"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M20.25 20.25L6.75 6.75"
                  stroke="var(--secondaryText)"
                  stroke-width="1.575"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M20.25 6.75L6.75 20.25"
                  stroke="var(--secondaryText)"
                  stroke-width="1.575"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
          </div>
        </div>

        <div
          class="w-full flex flex-col gap-1 justify-start items-start"
          :class="!hideStatus?'mt-2':''"
        >
          <p
            v-if="!is_commercial"
            class="text-xs font-normal text-secondaryText capitalize"
          >
            <TranslationComp
              text="UNIT"
            />
            <span> - </span>
            <TranslationComp
              :key="unitName"
              :text="unitName"
            />
            <span v-if="location"> , </span>
            <TranslationComp
              v-if="location"
              :text="location"
            />
          </p>

          <div v-if="styleType">
            <span class="gap-2 flex items-center flex-nowrap">
              <div class=" opacity-50  ">
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M3.23911 9.92134L9.06165 4.82633L9.0618 4.8262C10.4531 3.60874 11.1488 3 12 3C12.8512 3 13.5469 3.60877 14.9383 4.8263L14.9383 4.82633L20.7609 9.92134C21.2602 10.3583 20.9254 11.1343 20.2376 11.1343C19.8174 11.1343 19.4768 11.4489 19.4768 11.8371V15.0806C19.4768 17.871 19.4768 19.2662 18.5382 20.1331C17.5997 21 16.0892 21 13.0681 21H10.9319C7.91082 21 6.40029 21 5.46176 20.1331C4.52324 19.2662 4.52324 17.871 4.52324 15.0806V11.8371C4.52324 11.4489 4.18259 11.1343 3.76238 11.1343C3.07458 11.1343 2.7398 10.3583 3.23911 9.92134ZM12 13C10.3431 13 9 14.3431 9 16C9 17.6569 10.3431 19 12 19H12.009C13.6659 19 15.009 17.6569 15.009 16C15.009 14.3431 13.6659 13 12.009 13H12Z"
                    fill="#9CA3AF"
                  />
                  <path
                    d="M9.06165 4.82633L9.55554 5.39075L9.55591 5.39043L9.06165 4.82633ZM3.23911 9.92134L2.74522 9.35692L2.74519 9.35694L3.23911 9.92134ZM9.0618 4.8262L8.56791 4.26177L8.56754 4.2621L9.0618 4.8262ZM14.9383 4.8263L14.4444 5.39073L14.4481 5.39392L14.9383 4.8263ZM14.9383 4.82633L15.4322 4.2619L15.4285 4.25871L14.9383 4.82633ZM20.7609 9.92134L21.2548 9.35695L21.2548 9.35692L20.7609 9.92134ZM18.5382 20.1331L18.0293 19.5821L18.0293 19.5822L18.5382 20.1331ZM5.46176 20.1331L4.95287 20.684L4.95287 20.684L5.46176 20.1331ZM8.56776 4.26191L2.74522 9.35692L3.733 10.4858L9.55554 5.39075L8.56776 4.26191ZM8.56754 4.2621L8.56739 4.26223L9.55591 5.39043L9.55606 5.3903L8.56754 4.2621ZM12 2.25C11.3713 2.25 10.839 2.48305 10.316 2.83349C9.8122 3.17105 9.24861 3.66615 8.56791 4.26177L9.55569 5.39062C10.2663 4.76879 10.7463 4.35078 11.151 4.07961C11.5364 3.82131 11.7776 3.75 12 3.75V2.25ZM15.4322 4.26189C14.7515 3.66623 14.1878 3.1711 13.6841 2.83352C13.1611 2.48307 12.6288 2.25 12 2.25V3.75C12.2224 3.75 12.4636 3.82132 12.8491 4.07963C13.2538 4.35082 13.7337 4.76885 14.4444 5.39072L15.4322 4.26189ZM15.4285 4.25871L15.4285 4.25869L14.4481 5.39392L14.4481 5.39395L15.4285 4.25871ZM21.2548 9.35692L15.4322 4.26191L14.4444 5.39075L20.267 10.4858L21.2548 9.35692ZM20.2376 11.8843C20.884 11.8843 21.4085 11.5094 21.6331 10.9888C21.8614 10.4596 21.7643 9.80279 21.2548 9.35695L20.267 10.4857C20.2719 10.49 20.2693 10.4889 20.2645 10.4807C20.2597 10.4727 20.2551 10.4617 20.2524 10.4488C20.247 10.4228 20.2518 10.4037 20.2558 10.3946C20.2596 10.3856 20.2641 10.3819 20.266 10.3807C20.2704 10.3777 20.2649 10.3843 20.2376 10.3843V11.8843ZM20.2268 11.8371C20.2268 11.8511 20.2235 11.8649 20.2185 11.8757C20.2139 11.8858 20.2094 11.8903 20.2085 11.891C20.2073 11.8922 20.2157 11.8843 20.2376 11.8843V10.3843C19.4603 10.3843 18.7268 10.9798 18.7268 11.8371H20.2268ZM20.2268 15.0806V11.8371H18.7268V15.0806H20.2268ZM19.0471 20.6841C19.689 20.0911 19.9706 19.3368 20.1017 18.4362C20.2287 17.5637 20.2268 16.4529 20.2268 15.0806H18.7268C18.7268 16.4987 18.7249 17.4807 18.6173 18.2201C18.5137 18.9315 18.326 19.3082 18.0293 19.5821L19.0471 20.6841ZM13.0681 21.75C14.559 21.75 15.747 21.7514 16.6766 21.6359C17.622 21.5185 18.4137 21.2691 19.0471 20.684L18.0293 19.5822C17.7242 19.864 17.2914 20.048 16.4917 20.1474C15.6761 20.2486 14.5983 20.25 13.0681 20.25V21.75ZM10.9319 21.75H13.0681V20.25H10.9319V21.75ZM4.95287 20.684C5.58631 21.2691 6.37798 21.5185 7.32344 21.6359C8.25304 21.7514 9.44098 21.75 10.9319 21.75V20.25C9.40174 20.25 8.32388 20.2486 7.5083 20.1474C6.7086 20.048 6.27573 19.864 5.97065 19.5822L4.95287 20.684ZM3.77324 15.0806C3.77324 16.4529 3.77138 17.5637 3.89838 18.4362C4.02946 19.3368 4.31097 20.0911 4.95287 20.684L5.97065 19.5822C5.67403 19.3082 5.48628 18.9315 5.38273 18.2201C5.2751 17.4807 5.27324 16.4987 5.27324 15.0806H3.77324ZM3.77324 11.8371V15.0806H5.27324V11.8371H3.77324ZM3.76238 11.8843C3.78435 11.8843 3.79274 11.8922 3.79151 11.8911C3.79068 11.8903 3.78614 11.8858 3.78154 11.8758C3.77659 11.865 3.77324 11.8512 3.77324 11.8371H5.27324C5.27324 10.9797 4.5396 10.3843 3.76238 10.3843V11.8843ZM2.74519 9.35694C2.23575 9.80277 2.13862 10.4596 2.36693 10.9888C2.59152 11.5094 3.11596 11.8843 3.76238 11.8843V10.3843C3.73513 10.3843 3.72956 10.3777 3.73401 10.3807C3.73587 10.3819 3.74037 10.3856 3.74423 10.3946C3.74817 10.4037 3.75303 10.4229 3.7476 10.4488C3.74491 10.4617 3.74033 10.4727 3.73555 10.4808C3.73073 10.4889 3.72814 10.49 3.73303 10.4857L2.74519 9.35694ZM9.75 16C9.75 14.7574 10.7574 13.75 12 13.75V12.25C9.92893 12.25 8.25 13.9289 8.25 16H9.75ZM12 18.25C10.7574 18.25 9.75 17.2426 9.75 16H8.25C8.25 18.0711 9.92893 19.75 12 19.75V18.25ZM12.009 18.25H12V19.75H12.009V18.25ZM14.259 16C14.259 17.2426 13.2516 18.25 12.009 18.25V19.75C14.0801 19.75 15.759 18.0711 15.759 16H14.259ZM12.009 13.75C13.2516 13.75 14.259 14.7574 14.259 16H15.759C15.759 13.9289 14.0801 12.25 12.009 12.25V13.75ZM12 13.75H12.009V12.25H12V13.75Z"
                    fill="#9CA3AF"
                  />
                </svg>
              </div>

              <p class="text-colormix text-xs font-normal leading-2 capitalize">
                <TranslationComp
                  :text="styleType"
                />

              </p>
            </span>
          </div>
        </div>

        <div
          v-if="showContent"
          class="flex flex-col w-full"
        >
          <!-- price  -->
          <div
            v-if="price && (price !== 0 && price !== '' && price !== '0') && (cardDetails.price || showDefault) && maxPrice >= 1 && status === 'available'"
            class="py-0.5 pt-2"
          >
            <span class="font-bold !text-primary !text-xs uppercase">{{ selectedCurrency ? selectedCurrency : currencyTitle }}  <CurrencyComp
              :price="price"
              :unitCurrencyType="currencyTitle"
            /> </span> - <span class="font-bold !text-primary !text-xs uppercase">{{ selectedCurrency ? selectedCurrency : currencyTitle }}  <CurrencyComp
              :price="maxPrice"
              :unitCurrencyType="currencyTitle"
            /> </span>
          </div>
          <div
            v-else-if="price && (price !== 0 && price !== '' && price !== '0') && (cardDetails.price || showDefault) && status === 'available'"
            class="flex justify-between py-0.5 pt-2"
          >
            <span class="font-bold !text-primary !text-xs uppercase">{{ selectedCurrency ? selectedCurrency : currencyTitle }}  <CurrencyComp
              :price="price"
              :unitCurrencyType="currencyTitle"
            /> </span>
          </div>
          <!-- units  -->
          <div
            v-if="(cardDetails.units || showDefault)"
            class="mt-1"
          >
            <div
              v-for="(unit, index) in validUnitPlans"
              :key="index"
              class="flex justify-between py-1 "
              :class="[isMobile || Store.isLandscape?'':' gap-40', index === validUnitPlans.length -1 && totalArea ?'border-b border-gray-500':'']"
            >
              <span class="text-colormix text-xs font-normal whitespace-nowrap">
                <TranslationComp
                  :key="unit.name"
                  :text="unit.name"
                />:</span>
              <span class="text-secondaryText text-xs font-normal whitespace-nowrap">{{ thousandSeparator(unit.measurement) }} {{ unit.measurement_type === 'sqmt' ? 'Sq mt' : 'Sq ft' }}</span>
            </div>
          </div>
          <!-- balcony  -->
          <div
            v-if="balcony"
            class="flex justify-between py-1 pt-2 mt-2"
            :class="[isMobile || Store.isLandscape?'':' gap-40']"
          >
            <span class="text-secondaryText text-xs font-normal">
              <TranslationComp
                class="text-secondaryText text-xs font-normal"
                text="Balcony Area :"
              />
            </span>
            <span class="font-bold text-secondaryText  text-xs ">{{ thousandSeparator(balcony) }} {{ balconyType === 'sqmt' ? 'Sq mt' : 'Sq ft' }}</span>
          </div>
          <!-- Suite Area -->
          <div
            v-if="suiteArea"
            class="flex justify-between py-1 pt-2 mt-2"
            :class="[isMobile || Store.isLandscape?'':' gap-40']"
          >
            <span class="text-secondaryText text-xs font-normal">
              <TranslationComp
                class="text-secondaryText text-xs font-normal"
                text="Suite Area :"
              />
            </span>
            <span class="font-bold text-secondaryText text-xs ">{{ thousandSeparator(suiteArea) }} {{ suiteAreaType === 'sqmt' ? 'Sq mt' : 'Sq ft' }}</span>
          </div>
          <!-- measurement -->
          <div
            v-if="totalArea && (cardDetails.measurement || showDefault)"
            class="flex justify-between py-1 pt-2 mt-0"
            :class="[isMobile || Store.isLandscape?'':' gap-40']"
          >
            <span class="text-secondaryText text-xs font-normal">
              <TranslationComp
                class="text-colormix text-xs font-normal"
                text="Total Area"
              />
            </span>
            <span class="font-bold text-secondaryText  text-xs ">{{ thousandSeparator(totalArea) }} {{ measurementType === 'sqmt' ? 'Sq mt' : 'Sq ft' }}</span>
          </div>
          <div
            v-if="status==='available' || unitplanCta"
            class="flex justify-around items-center gap-5 w-full mt-3"
          >
            <fwb-button
              v-if="isonsalestool && (Store.projectCardData[route.params.projectId]?.projectSettings.ale?.is_cta_enabled && (Store.projectCardData[route.params.projectId]?.projectSettings.ale?.cta_type==='custom' ? ctaLink !=='' : true ) && !switchs)"
              class="w-full flex bg-primary text-primaryText font-medium justify-center buttonClass hover:bg-primary  cardshadow focus:ring-0"
              size="lg"
              @click="handleCta()"
            >
              <TranslationComp
                :text="Store.projectCardData[route.params.projectId].projectSettings.ale.cta_name"
              />
            </fwb-button>
          </div>
        </div>
      </div>
    </div>

    <div
      v-if="!Store.isMobile && !Store.isLandscape"
      class="flex justify-center w-[95%] absolute -bottom-5 cursor-pointer"
      @click="minizier()"
    >
      <div class="w-[6.25rem] h-5 flex justify-center bg-secondary rounded-b-lg ">
        <svg
          :class="showContent?'arrow-close ':'arrow-open '"
          class="w-5 h-5"
          viewBox="0 0 18 10"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M15 2C15 2 10.581 7.99999 8.99995 8C7.41885 8.00001 3 2 3 2"
            stroke="#9CA3AF"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>
    <!-- close here  -->
    <div
      v-if="(status === 'available' || unitplanCta) && (Store.isMobile || Store.isLandscape)"
      class=""
      :class="[switchs ? 'block' :'hidden', 'relative flex justify-around items-center h-full ml-2', Store.isMobile ? 'w-[95%]' : '',
               Store.isMobile
                 ? unitplanCta
                   ? (Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0
                     ? ' bottom-24 -mb-2'
                     : 'bottom-5 w-full')
                   : (Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0
                     ? 'bottom-24 right-8'
                     : 'bottom-6 right-8')
                 : unitplanCta ? 'w-[95%] right-8 bottom-3' : 'w-[95%] right-8 bottom-2', Store.isMobile && route.query['type'] === 'interior' ? 'ml-2 bottom-24 right-10' : '']"
    >
      <fwb-button
        v-if="Store.projectCardData[route.params.projectId]?.projectSettings.ale?.is_cta_enabled && (Store.projectCardData[route.params.projectId]?.projectSettings.ale?.cta_type==='custom' ? ctaLink !=='' : true )"
        :class="[(Store.isMobile && route.query['type'] === 'unitplan' && unitplanCta) ? 'w-[90%]' : 'w-[80%]', 'h-full flex bg-primary text-xs text-primaryText font-medium justify-center buttonClass hover:bg-primary focus:ring-0 items-center', 'whitespace-nowrap px-9', Store.isMobile && route.query['type'] === 'interior' && unitplanCta ? '!ml-2' : '']"
        size="lg"
        @click="handleCta()"
      >
        <TranslationComp
          :text="Store.projectCardData[route.params.projectId].projectSettings.ale.cta_name"
        />
      </fwb-button>
    </div>
    <div
      v-if="Store.isMobile || Store.isLandscape"
      class=" bg-primary w-8 h-8   p-1 flex !items-center !justify-center backdrop-blur-[3.125rem] ml-auto absolute"
      :class="[switchs ? 'block' :'hidden', Store.isLandscape ? 'right-2 w-[32px] h-[32px] !rounded-2xl z-0' : 'sm:rounded-lg rounded-2xl',
               Store.isMobile
                 ? (unitplanCta
                   ? (Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0
                     ? (Store.projectCardData[route.params.projectId]?.projectSettings.ale?.is_cta_enabled ? ( route.query['type'] === 'unitplan' ? 'bottom-[8.5rem] right-4' : 'bottom-[5.7rem] right-6 -mb-2.5'): 'bottom-[5.25rem] right-4')
                     : (Store.projectCardData[route.params.projectId]?.projectSettings.ale?.is_cta_enabled ? 'bottom-[3.5rem] right-4' : 'bottom-3 right-4'))
                   : (Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0
                     ? '!bottom-20 right-4'
                     : 'bottom-3 right-4'))
                 : '', Store.isLandscape ? unitplanCta ? 'bottom-0' : 'bottom-3' : '', Store.isMobile && route.query['type'] === 'interior' ? (Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0 ? 'bottom-[1rem]' : 'bottom-[0.85rem]') : '']"
      @click="closeModal()"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
      >
        <path
          d="M10 17.5C8.51664 17.5 7.06659 17.0601 5.83323 16.236C4.59986 15.4119 3.63856 14.2406 3.07091 12.8701C2.50325 11.4997 2.35472 9.99168 2.64411 8.53683C2.9335 7.08197 3.64781 5.7456 4.6967 4.6967C5.7456 3.64781 7.08197 2.9335 8.53683 2.64411C9.99168 2.35472 11.4997 2.50325 12.8701 3.07091C14.2406 3.63856 15.4119 4.59986 16.236 5.83323C17.0601 7.06659 17.5 8.51664 17.5 10C17.4978 11.9885 16.7069 13.8948 15.3009 15.3009C13.8948 16.7069 11.9885 17.4978 10 17.5ZM10 4C8.81331 4 7.65328 4.3519 6.66658 5.01119C5.67989 5.67047 4.91085 6.60755 4.45673 7.7039C4.0026 8.80026 3.88378 10.0067 4.11529 11.1705C4.3468 12.3344 4.91825 13.4035 5.75736 14.2426C6.59648 15.0818 7.66558 15.6532 8.82946 15.8847C9.99335 16.1162 11.1997 15.9974 12.2961 15.5433C13.3925 15.0892 14.3295 14.3201 14.9888 13.3334C15.6481 12.3467 16 11.1867 16 10C15.9982 8.40925 15.3655 6.88417 14.2407 5.75934C13.1158 4.6345 11.5908 4.00179 10 4Z"
          fill="var(--primaryText)"
        />
        <path
          d="M10 11.5C9.80109 11.5 9.61032 11.421 9.46967 11.2803C9.32902 11.1397 9.25 10.9489 9.25 10.75V7C9.25 6.80109 9.32902 6.61032 9.46967 6.46967C9.61032 6.32902 9.80109 6.25 10 6.25C10.1989 6.25 10.3897 6.32902 10.5303 6.46967C10.671 6.61032 10.75 6.80109 10.75 7V10.75C10.75 10.9489 10.671 11.1397 10.5303 11.2803C10.3897 11.421 10.1989 11.5 10 11.5Z"
          fill="var(--primaryText)"
        />
        <path
          d="M10 13.75C10.4142 13.75 10.75 13.4142 10.75 13C10.75 12.5858 10.4142 12.25 10 12.25C9.58579 12.25 9.25 12.5858 9.25 13C9.25 13.4142 9.58579 13.75 10 13.75Z"
          fill="var(--primaryText)"
        />
      </svg>
    </div>
  </div>
</template>

<style scoped>
/* You can add additional styles here if needed */
.arrow-open {
  transform: rotate(0deg);
  transition: 0.5s
}

.arrow-close {
  transform: rotate(180deg);
  transition: 0.5s
}
.cardshadow:hover{
  box-shadow: var(--primary) 0px 0px 15px;
}

</style>
