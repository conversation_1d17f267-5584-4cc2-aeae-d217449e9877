<script setup>
import { ref } from "vue";
import { useRoute } from "vue-router";
import { creationToolStore } from "../../../store";
import { Form } from "vee-validate";

import TranslationComp from '../TranslationComp/TranslationComp.vue';
import ButtonComponent from '../ButtonComponent/ButtonComponent.vue';
import FormInput from '../FormInput/FormInput.vue';
import StatusPopup from '../StatusPopup/StatusPopup.vue';
import { registerinterestSchema } from "../../../helpers/validationSchema";

const formData = ref({
  fullName: '',
  phone: '',
  email: '',
});
const showSuccessPopup = ref(false);
const showErrorPopup = ref(false);
const errorMessage = ref('');
const isSubmitting = ref(false);
const Store = creationToolStore();
const route = useRoute();
const emit = defineEmits(["handleClose", "handleClick"]);

const props = defineProps({
  unitId: { type: String, default: '' },
  unitName: { type: String, default: '' },
});

const goBack = () => {
  Store.showRegisterInterest = false;
  emit("handleClose");
};

const onFormSubmit = async (values) => {
  formData.value = { ...values };
  isSubmitting.value = true;

  try {
    const DTO = {
      name: values.fullName,
      email: values.email,
      phonenumber: values.phone,
    };

    const project_id = route.params.projectId;
    const unit_id = props.unitId || 'default_unit';

    await Store.CreateLead(DTO, project_id, unit_id).then(() => {
      Store.showRegisterInterest = false;
      isSubmitting.value = false;
      showSuccessPopup.value = true;
    });
  } catch (error) {
    isSubmitting.value = false;
    errorMessage.value = 'Whoops! Unable to submit';
    Store.showRegisterInterest = false;
    showErrorPopup.value = true;
  }
};

const handleSuccessClose = () => {
  showSuccessPopup.value = false;
  emit("handleClose");
};

const handleErrorClose = () => {
  showErrorPopup.value = false;
  errorMessage.value = '';
  emit("handleClose");
};
</script>

<template>
  <div class="fixed inset-0 !z-[55] flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-[30px]">
    <div
      class="bg-secondary lg:min-w-[35vw] rounded-lg flex-col justify-start items-start inline-flex gap-1 w-full max-w-md mx-2 overflow-y-auto max-h-[90vh] h-fit"
      :class="Store.isMobile ? 'p-4' : 'p-6'"
    >
      <div class="flex justify-between items-center w-full">
        <p class="font-bold text-left text-xl leading-none text-secondaryText">
          <TranslationComp text="Register Your Interest" />
        </p>
        <div
          class="h-5 w-5 cursor-pointer"
          @click="goBack()"
        >
          <svg
            class="w-5 h-5"
            viewBox="0 0 27 27"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M20.25 20.25L6.75 6.75"
              stroke="var(--secondaryText)"
              stroke-width="1.575"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M20.25 6.75L6.75 20.25"
              stroke="var(--secondaryText)"
              stroke-width="1.575"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>

      <p class="font-normal text-left text-base leading-none text-secondaryText mt-1">
        <TranslationComp text="Tell us a few details and our team will reach out." />
      </p>

      <div class="text-sm my-3 font-medium font-semibold text-secondaryText capitalize">
        <TranslationComp text="Interested Units : " />
        <span class="text-xs font-medium text-secondaryText capitalize">
          <TranslationComp
            :key="props.unitName"
            :text="props.unitName"
          />
        </span>
      </div>

      <Form
        v-slot="{ errorBag }"
        :validation-schema="registerinterestSchema"
        class="w-full flex flex-col gap-5 !m-0"
        @submit="onFormSubmit"
      >
        <FormInput
          v-model="formData.fullName"
          label="Full Name"
          name="fullName"
          type="text"
          placeholder="Enter your full name"
          :error-bag="errorBag"
        />

        <FormInput
          v-model="formData.phone"
          label="Phone"
          name="phone"
          type="tel"
          placeholder="Enter your phone number"
          :error-bag="errorBag"
        />

        <FormInput
          v-model="formData.email"
          label="Email"
          name="email"
          type="email"
          placeholder="<EMAIL>"
          :error-bag="errorBag"
        />

        <div class="w-full button-full-width focus:ring-0">
          <ButtonComponent
            :message="isSubmitting ? 'Submitting...' : 'Submit'"
            :disabled="isSubmitting"
          />
        </div>
      </Form>
    </div>
  </div>

  <StatusPopup
    :show="showSuccessPopup"
    type="success"
    title="Successfully Registered Your Interest"
    message="Our team will reach out."
    button-text="Continue"
    :is-mobile="Store.isMobile"
    @close="handleSuccessClose"
  />

  <StatusPopup
    :show="showErrorPopup"
    type="error"
    :message="errorMessage"
    button-text="Try Again"
    :is-mobile="Store.isMobile"
    @close="handleErrorClose"
  />
</template>

<style scoped>
::-webkit-scrollbar {
  display: none;
}

.button-full-width :deep(.buttons) {
  width: 100% !important;
}
</style>
