<script setup>
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { computed, onMounted, ref } from 'vue';
import DropDown from '../DropDown/DropDown.vue';
import NearByFloatingButton from '../NearByFloatingButton/NearByFloatingButton.vue';
import { creationToolStore } from '../../../store';
import { useRoute } from 'vue-router';

const leftButton = `<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M18.375 10.5L2.625 10.5" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M7 14.875L2.625 10.5L7 6.125" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

const rightButton = `<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M2.625 10.5L18.375 10.5" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M14 14.875L18.375 10.5L14 6.125" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

const route = useRoute();
const Store = creationToolStore();
const props = defineProps({
  tourData: {
    type: Object,
    required: true,
  },
});
const storagePath = `https://${import.meta.env.VITE_APP_BUCKET_CDN}/CreationtoolAssets/${route.params.organizationId}/projects/${route.params.projectId}/tours/${route.params.tourId}`;

const containerVal = ref(null);
const updateTextureRef = ref(true);
var updateTexture = updateTextureRef.value;
const imagenameRef = ref(null);
var imagename = imagenameRef.value;
const listOfImages = ref(null);
const currentImageId = ref('');
const currentGroupId = ref('');

// Define LOD configurations
const lods = [
  { level: 2, rows: 2, columns: 2 },
  { level: 1, rows: 4, columns: 4 },
  { level: 0, rows: 8, columns: 8 },
];

var loadedTiles = lods.map(() => []); // Array for each LOD
let lastUpdateTime = 0;
const updateInterval = 50; // Update every 250ms
const batchSize = 8;
const lodLoadDelay = 500; // Delay between LOD loads in ms
let currentLODIndex = 0;
let currentTexture = null;
let nextTexture = null;

/* Methods */
// HasGroups
const hasGroups = computed(() => props.tourData?.groups && Object.keys(props.tourData.groups).length > 0);
// GetImages
const getImages = () => {
  if (!props.tourData?.images) {
    return [];
  }
  const images = Object.entries(props.tourData.images);

  const baseMap = ([id, image]) => ({
    icon: `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
          <path d="M17.0309 10.9218C19.5182 6.33548 18.3845 3.22238 14.129 3C11.036 3.05905 7.84555 4.72552 5.29889 7.30361C3.16169 9.46717 1.05739 12.5658 1.82988 15.515C2.00045 16.1662 2.33304 16.6098 2.85842 17.0273C4.2703 18.1493 5.65613 18.248 8.32522 17.5927C11.0282 16.7714 12.704 15.534 14.117 14.2681M14.117 14.2681C14.1184 14.2668 14.1199 14.2654 14.1214 14.2641C14.124 14.2617 14.1223 14.2573 14.1187 14.2573C14.116 14.2573 14.1141 14.26 14.115 14.2625C14.1157 14.2644 14.1163 14.2663 14.117 14.2681ZM14.117 14.2681C14.4256 15.1808 14.2301 15.9953 13.6987 17.5927" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>`,
    id,
    value: image.id,
    name: image.name,
  });

  if (hasGroups.value) {
    if (!currentGroupId.value) {
      return [];
    }
    listOfImages.value =  images
      .filter(([_, image]) => image.groupId === currentGroupId.value)
      // Add sorting by order
      .sort((a, b) => (a[1].order || 0) - (b[1].order || 0))
      .map(baseMap);
  } else {
    // If no groups, show all images sorted by order
    listOfImages.value = images
      .sort((a, b) => (a[1].order || 0) - (b[1].order || 0))
      .map(baseMap);
  }

  return listOfImages.value;
};
// CurrentDropDownValue
const currentDropdownValue = computed(() => {
  if (!currentGroupId.value || !props.tourData?.groups?.[currentGroupId.value]) {
    return null;
  }
  return {
    name: props.tourData.groups[currentGroupId.value].name,
    category: currentGroupId.value,
  };
});
// CurrentIndex
const currentIndex = computed(() => {
  const images = getImages();
  return images.findIndex((img) => img.id === currentImageId.value);
});
// GetGroupsList
const getGroupsList = () => {
  if (!hasGroups.value) {
    return [];
  }

  // Filter out groups that have no images
  return Object.entries(props.tourData.groups)
    .filter(([groupId]) => {
      // Check if any images exist for this group
      return Object.values(props.tourData.images).some(
        (image) => image.groupId === groupId,
      );
    })
    .sort((a, b) => a[1].order - b[1].order)
    .map(([id, group]) => ({
      value: id,
      name: group.name,
      category: id,
    }));
};

// Initial
if (hasGroups.value && props.tourData?.groups) { // If groups,
  const firstGroupId = Object.keys(props.tourData.groups)[0];
  const groupImage = Object.entries(props.tourData.images)
    .find(([_, image]) => image.groupId === firstGroupId);

  if (groupImage) {
    const getTheImageObj = props.tourData.images[groupImage[0]];
    console.log(getTheImageObj, "getTheImageObj");

    // Extract image name
    let fileName = getTheImageObj.name.split('.').slice(0, -1).join('.'); // Remove extension
    // Remove the last underscore + numbers
    fileName = fileName.replace(/_\d+$/, '');
    imagename = fileName;
    // Image Id
    currentImageId.value = getTheImageObj.id;
    // Grp Id
    currentGroupId.value = getTheImageObj.groupId;
    // groupImage[0]; // Return the image ID
  }
} else { // No groups Others
  if (props.tourData?.images) {
    const firstImage = Object.values(props.tourData.images)[0];
    if (firstImage) {
      // Extract image name
      let fileName = firstImage.name.split('.').slice(0, -1).join('.'); // Remove extension
      // Remove the last underscore + numbers
      fileName = fileName.replace(/_\d+$/, '');
      imagename = fileName;
      // Image Id
      currentImageId.value = firstImage.id;
      // Grp Id
      currentGroupId.value = firstImage.groupId;
    }
  }
}

onMounted(() => {

  const container = containerVal.value;
  document.body.appendChild(container);
  // Setup the scene, camera, and renderer
  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(90, window.innerWidth / window.innerHeight, 0.1, 1000);
  const renderer = new THREE.WebGLRenderer();
  renderer.setSize(window.innerWidth, window.innerHeight);
  renderer.setPixelRatio(window.devicePixelRatio);
  container.appendChild(renderer.domElement);
  const maxAnisotropy = renderer.capabilities.getMaxAnisotropy();
  // Add OrbitControls
  const controls = new OrbitControls(camera, renderer.domElement);
  camera.position.set(0, 0, 1);
  controls.update();

  // Create a sphere for the 360 panorama
  const geometry = new THREE.SphereGeometry(90, 60, 40);
  geometry.scale(-1, 1, 1); // Invert the sphere to view from inside

  // ShaderMaterial for blending LODs
  const material = new THREE.ShaderMaterial({
    uniforms: {
      currentTexture: { value: null },
      nextTexture: { value: null },
      blendFactor: { value: 0.0 },
    },
    vertexShader: `
        varying vec2 vUv;
        void main() {
            vUv = uv;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
    `,
    fragmentShader: `
        uniform sampler2D currentTexture;
        uniform sampler2D nextTexture;
        uniform float blendFactor;
        varying vec2 vUv;
        void main() {
            vec4 currentColor = texture2D(currentTexture, vUv);
            vec4 nextColor = texture2D(nextTexture, vUv);
            gl_FragColor = mix(currentColor, nextColor, blendFactor);
        }
    `,
    transparent: false,
  });

  const sphere = new THREE.Mesh(geometry, material);
  scene.add(sphere);

  // Helper function to combine tiles into a single texture
  function combineAndApplyTiles (tiles, rows, columns) {
    if (!updateTexture) {
      return;
    }
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    const tileSize = 512; // Adjust to your tile size
    canvas.width = tileSize * columns;
    canvas.height = tileSize * rows;

    tiles.forEach((tile, i) => {
      if (!updateTexture) {
        return;
      }
      const x = (i % columns) * tileSize;
      const y = Math.floor(i / columns) * tileSize;
      if (tile) {
        context.drawImage(tile.image, x, y, tileSize, tileSize);
      }
    });

    const texture = new THREE.Texture(canvas);
    // texture.encoding = THREE.sRGBEncoding;
    texture.anisotropy = maxAnisotropy;
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.generateMipmaps = false;
    texture.minFilter = THREE.LinearFilter;
    // texture.magFilter = THREE.LinearFilter;
    texture.needsUpdate = true;
    if (!updateTexture) {
      return;
    }
    return texture;

  }

  // Function to load tiles for an LOD in batches
  function loadTilesForLOD (lod) {
    if (!updateTexture) {
      return;
    }
    // const tileSize = 512; // Replace with actual size if different
    const tilesToLoad = [];
    const loader = new THREE.TextureLoader();
    if (!lod) {
      return;
    }
    // Collect tiles to load
    for (let row = 0; row < lod.rows; row++) {
      if (!updateTexture) {
        return;
      }
      for (let col = 0; col < lod.columns; col++) {
        if (!updateTexture) {
          return;
        }
        const tileIndex = (row * lod.columns) + col;
        // console.log(lod)
        if (!loadedTiles[lod.level][tileIndex]) {
          const url = `${storagePath}/${imagename}/lod_${lod.level}/tile_${row}_${col}.jpg`;
          console.log(url);
          tilesToLoad.push({ url, index: tileIndex });
        }
      }
      // if (lod.level == 2 && row == 1) {
      //     startTransitionRef.value = true
      // }
    }

    // Helper to load a batch of tiles
    function loadBatch (batch) {
      if (!updateTexture) {
        return;
      }
      return Promise.all(
        batch.map((tile) =>
          new Promise((resolve, reject) => {
            if ((tile.url).includes(`${imagename}/`)) {
              loader.load(
                tile.url,
                (texture) => {
                  // console.log(tile.url)
                  if (!updateTexture) {
                    return;
                  }
                  loadedTiles[lod.level][tile.index] = texture;
                  // if (lod.level == 2 && tile.index == 1) {
                  //     startTransitionRef.value = true
                  // }
                  resolve(texture);
                },
                undefined,
                (err) => {
                  console.error(`Error loading tile ${tile.url}:`, err);
                  reject(err);
                },
              );
            }
          }),
        ),
      );
    }

    // Process tiles in batches
    return new Promise((resolve) => {
      if (!updateTexture) {
        return;
      }
      const batches = [];
      // console.log(tilesToLoad)
      for (let i = 0; i < tilesToLoad.length; i += batchSize) {
        if (!updateTexture) {
          return;
        }
        batches.push(tilesToLoad.slice(i, i + batchSize));
      }

      let currentBatchIndex = 0;

      function processNextBatch () {
        if (!updateTexture) {
          loadedTiles = lods.map(() => []);
          return;
        }
        if (currentBatchIndex >= batches.length) {
          // console.log(batches.length)
          // Combine and resolve once all batches are loaded
          resolve(combineAndApplyTiles(loadedTiles[lod.level], lod.rows, lod.columns));
          if (currentLODIndex >= lods.length) {
            updateTexture = false;
            // currentLODIndex = 0;
            // lastUpdateTime = 0;
            // loadedTiles = lods.map(() => []);
            // loadedTiles = lods.map(() => []);
            return;
          }

          currentLODIndex++;

          return;
        }

        loadBatch(batches[currentBatchIndex]).then(() => {
          // console.log(currentBatchIndex)
          if (!updateTexture) {
            return;
          }
          currentBatchIndex++;
          setTimeout(processNextBatch, updateInterval); // Adjust delay if needed
        });
      }

      processNextBatch();
    });
  }

  // Update logic with blending
  function updateTileLoading () {
    const currentTime = Date.now();
    if (!updateTexture) {
      return;
    }
    if (currentLODIndex >= lods.length) {
      currentLODIndex = 2;
      // updateTexture = false;
      // currentLODIndex = 0;
      // lastUpdateTime = 0;
      // material.uniforms.uTexture1.value = material.uniforms.uTexture2.value
      // material.uniforms.uBlendFactor.value=0;
      // console.log(loadedTiles)
      // loadedTiles = lods.map(() => []);
      // return
    }
    // console.log(currentLODIndex)

    const lod = lods[currentLODIndex];
    // console.log(lods)
    if (!lod) {
      return;
    }
    if (currentTime - lastUpdateTime > lodLoadDelay) {
      if (!updateTexture) {
        return;
      }
      loadTilesForLOD(lod).then((texture) => {
        if (!updateTexture) {
          return;
        }
        // console.log(texture)
        nextTexture = texture;
        // blendFactor = 0;
        currentTexture = nextTexture;
        material.uniforms.currentTexture.value = currentTexture;
        // if (!animating) {
        //     if (!updateTexture) return;
        //     material.uniforms.uTexture1.value = currentTexture

        // }

        material.uniforms.nextTexture.value = nextTexture;

        //  material.needsUpdate = true;

      });

      lastUpdateTime = currentTime;

    }

  }

  function onWindowResize () {

    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();

    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);

  }
  window.addEventListener('resize', onWindowResize);
  // Animation loop
  function animate () {
    requestAnimationFrame(animate);
    controls.update();
    if (updateTexture) {
      updateTileLoading();
    }
    renderer.render(scene, camera);
  }

  animate();
});

// UpdateMaterial
function updateMaterial (name) {
  currentLODIndex = 0;
  lastUpdateTime = 0;
  loadedTiles = lods.map(() => []);
  imagename = name;
  updateTexture = true;
}

// Switch Image
const handleImageChange = (item) => {
  console.log(item, "handleImageChange");
  if (item && item.id) {
    //  if(imageId === currentImageId.value){
    //    return
    // }
    // switchImage(item.id);
    let fileName = item.name.split('.').slice(0, -1).join('.'); // Remove extension
    // Remove the last underscore + numbers
    fileName = fileName.replace(/_\d+$/, '');
    console.log(fileName);
    currentImageId.value = item.id;
    updateMaterial(fileName);
  }
};

// Group Change
const handleGroupChange = (group) => {
  console.log("handleGroupChange", group);
  currentGroupId.value =  group.category || group.value;
  const groupImages = getImages();
  if (groupImages.length > 0) {
    console.log(groupImages);

    const getTheImageObj = groupImages[0];
    console.log("getTheImageObj", getTheImageObj);

    // Extract image name
    let fileName = getTheImageObj.name.split('.').slice(0, -1).join('.'); // Remove extension
    // Remove the last underscore + numbers
    fileName = fileName.replace(/_\d+$/, '');
    // Image Id
    currentImageId.value = getTheImageObj.id;
    updateMaterial(fileName);

  }
};

</script>

<template>
  <div class="vr-container relative w-full h-screen">
    <div
      id="container"
      ref="containerVal"
    />

    <!-- Navigation controls remain the same -->
    <div class="fixed w-full flex justify-center bottom-10 z-[3]">
      <NearByFloatingButton
        class="flex"
        :class="Store.isMobile ? 'w-full' : 'sm:w-[38%] md:w-[48%] lg:w-[60%] xl:[75%]'"
        :itemsList="listOfImages"
        :sliderButton="true"
        :active="currentIndex"
        :leftButton="leftButton"
        :rightButton="rightButton"
        :objectIconKey="'icon'"
        :objectNameKey="'name'"
        @button-clicked="handleImageChange"
      />
    </div>

    <div
      v-if="hasGroups"
      class="fixed z-[6]"
      :class="[
        Store.isMobile ? 'right-3' : 'left-9',
        Store.isMobile ? 'bottom-[9rem]' : 'bottom-10'
      ]"
    >
      <DropDown
        v-if="currentDropdownValue"
        placement="top"
        :list="getGroupsList()"
        :defaultValue="currentDropdownValue"
        :align-to-end="Store.isMobile ? true : false"
        type="object"
        objectKey="name"
        @select-option="handleGroupChange"
      />
    </div>
  </div>
</template>

<style scoped>
.vr-container {
  position: relative;
  width: 100%;
  height: 100vh;
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
#container {
    position: absolute;
    top: 0;
    left: 0;
    /* width: 100%;
    height: 100%; */
    /* image-rendering: pixelated; */
}

#ui ul {
    list-style-type: none;
    flex-direction: row;
    display: flex;
}

#ui {
    z-index: 999;
    position: absolute;
    top: 0;
    left: 0;
    margin: 0 auto;
}
</style>
