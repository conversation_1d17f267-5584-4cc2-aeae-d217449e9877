<template>
  <div class="flex-col justify-start items-start gap-2 flex">
    <label class="text-secondaryText text-sm font-medium leading-[21px]">
      <TranslationComp :text="label" />
    </label>
    <Field
      :id="name"
      v-model="localValue"
      as="input"
      :type="type"
      :name="name"
      :class="errorBag?.[name] ? '!border-2 !border-red-600' : 'border border-tertiary50opacity'"
      class="rounded-lg w-full !bg-tertiary50opacity text-sm font-normal text-start placeholder:secondaryText text-secondaryText p-3"
      :placeholder="placeholder"
      @input="$emit('update:modelValue', $event.target.value)"
    />
    <div
      v-if="errorBag?.[name]"
      class="text-red-500 text-xs mt-1"
    >
      <TranslationComp :text="getErrorMessage(errorBag[name])" />
    </div>
  </div>
</template>

<script setup>
import { Field } from "vee-validate";
import TranslationComp from '../TranslationComp/TranslationComp.vue';

const props = defineProps({
  label: { type: String, required: true },
  name: { type: String, required: true },
  type: { type: String, default: 'text' },
  placeholder: { type: String, default: '' },
  modelValue: { type: String, default: '' },
  errorBag: { type: Object, default: () => ({}) },
});

const emit = defineEmits(['update:modelValue']);

import { computed } from 'vue';
const localValue = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
});

const getErrorMessage = (error) => {
  if (!error) {
    return '';
  }
  if (Array.isArray(error)) {
    return error.length > 0 ? error[0] : '';
  }
  return error;
};
</script>
<style scoped>
/* Override autofill background color */
input[type="text"]:-webkit-autofill,
input[type="email"]:-webkit-autofill,
input[type="tel"]:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px rgba(0, 0, 0, 0.1) inset !important;
  -webkit-text-fill-color: var(--secondaryText) !important;
  background-color: rgba(0, 0, 0, 0.1) !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

/* For Firefox */
input[type="text"]:-moz-autofill,
input[type="email"]:-moz-autofill,
input[type="tel"]:-moz-autofill {
  background-color: rgba(0, 0, 0, 0.1) !important;
}

/* For other browsers */
input[type="text"]:autofill,
input[type="email"]:autofill,
input[type="tel"]:autofill {
  background-color: rgba(0, 0, 0, 0.1) !important;
}

/* Additional fallback for Chrome/Safari */
input[type="text"]:autofill,
input[type="email"]:autofill,
input[type="tel"]:autofill {
  -webkit-box-shadow: 0 0 0 1000px rgba(0, 0, 0, 0.1) inset !important;
  background-color: rgba(0, 0, 0, 0.1) !important;
}
</style>
