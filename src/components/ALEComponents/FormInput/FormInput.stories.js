import FormInput from './FormInput.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/FormInput',
  component: FormInput,
  tags: ['autodocs'],
  argTypes: {
    label: {
      control: 'text',
      description: 'Label text for the input field',
    },
    name: {
      control: 'text',
      description: 'Name attribute for the input field',
    },
    type: {
      control: 'select',
      options: ['text', 'email', 'tel', 'password', 'number'],
      description: 'Input type',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text',
    },
    modelValue: {
      control: 'text',
      description: 'Input value',
    },
  },
};

export const Primary = {
  args: {
    label: 'Full Name',
    name: 'fullName',
    type: 'text',
    placeholder: 'Enter your full name',
    modelValue: '',
  },
};

export const Email = {
  args: {
    label: 'Email Address',
    name: 'email',
    type: 'email',
    placeholder: '<EMAIL>',
    modelValue: '',
  },
};

export const Phone = {
  args: {
    label: 'Phone Number',
    name: 'phone',
    type: 'tel',
    placeholder: 'Enter your phone number',
    modelValue: '',
  },
};

export const WithError = {
  args: {
    label: 'Email Address',
    name: 'email',
    type: 'email',
    placeholder: '<EMAIL>',
    modelValue: 'invalid-email',
    errorBag: {
      email: 'Please enter a valid email address',
    },
  },
};
