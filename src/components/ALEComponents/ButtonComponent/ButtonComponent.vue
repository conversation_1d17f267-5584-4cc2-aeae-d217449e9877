<template>
  <div class="relative flex flex-col gap-4 group">
<div class="butons">
    <fwb-button
      class="cursor-pointer pa-2 group buttons z-[1] relative !bg-primary justify-center items-center gap-2 hover:bg-primary hover:textfont focus:ring-0"
      :class="Store.isMobile? 'w-full': Store.isLandscape ? 'w-[155px]' : 'flex w-[17.875rem]'"
      @click="handleClick()"
    >
      <span class="text-base not-italic text-primaryText font-medium ">
        <TranslationComp :text="message" />
      </span>
      <template #suffix>
        <div class="buttonsvg" v-html="svg" />
      </template>
    </fwb-button>
    </div>
  </div>
</template>
<script setup>
import { FwbButton } from 'flowbite-vue';
import { defineEmits, defineProps } from 'vue';
import { creationToolStore } from '../../../store';
import TranslationComp from '../TranslationComp/TranslationComp.vue';

const Store = creationToolStore();
const emit = defineEmits(['handleButtonClick']);

defineProps({
  message: {
    type: String,
    default: "",
  },
  svg: {
    type: String,
    default: "",
  },
});

const handleClick = () => {
  emit('handleButtonClick');
};

</script>

<style  scoped>
.buttons{
   cursor: pointer;
padding: var(--35, 12px) var(--6, 24px);
}

</style>
<style scoped>
.butons{
  position: relative;

  border: none;
  outline: none;
  border-radius: 5px;
  font-size: 18px;
  font-family: 'Raleway', sans-serif;
}
.butons:before{
  position: absolute;
  content: '';
  top: -2px;
  left: -2px;
  height: calc(100% + 4px);
  width: calc(100% + 4px);
  border-radius: 5px;
  z-index: -1;

opacity: 1;
  filter: blur(5px);
  background: linear-gradient( 45deg, #0a0f3c, #0d1a5f, #1e3a8a, #2563eb, #3b82f6, #60a5fa, #93c5fd, #bfdbfe, #0a0f3c);
   /* background: linear-gradient(45deg, #ff0000, #ff7300, #fffb00, #48ff00, #00ffd5, #002bff, #7a00ff, #ff00c8, #ff0000); */
  background-size: 400%;
  transition: opacity .3s ease-in-out;
  animation: animate 20s linear infinite;
}
.butons:hover:before{
  opacity: 0;
}
.butons:hover{
   transform: translateY(-4px);
}
.butons:hover:active{
  opacity: 0 !important;
   transform: translateY(-4px);
  background: none;
}
.butons:hover:active:before{
  filter: blur(2px);
}
@keyframes animate {
  0% { background-position: 0 0; }
  50% { background-position: 400% 0; }
  100% { background-position: 0 0; }
}

</style>
