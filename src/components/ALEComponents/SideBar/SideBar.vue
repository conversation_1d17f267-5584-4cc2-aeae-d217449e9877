<template>
  <div
    ref="dropDownWrapperRef"
    class="card-change"
    :class="[right ? 'right-0 flex-row' : 'left-0 flex-row-reverse']"
  >
    <div class="buttonm">
      <button
        ref="dropdownRef"
        class="button w-12  h-40 my-auto rounded text-black bg-[#ffffff99] text-center focus:outline-none  transition-color duration-300"
        @click.prevent="toggle"
      >
        <span
          :class="[right ? '-rotate-90' : 'rotate-90']"
          class="block transform origin-center font-bold"
        >
          <div class="proj-btn">
            Project

            <svg
              class="h-4 w-4"
              :class="[open ? 'ro270' : 'ro90']"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g id="Group 238012">
                <path
                  id="Vector"
                  d="M15.4385 16L9.16982 8L15.4385 0L13.2295 -2.89669e-07L6.96086 8L13.2295 16L15.4385 16Z"
                  fill="#6B7280"
                />
                <path
                  id="Vector_2"
                  d="M9.03857 16L2.76992 8L9.03857 0L6.82962 -2.89669e-07L0.560963 8L6.82962 16L9.03857 16Z"
                  fill="#25272C"
                />
              </g>
            </svg>
          </div>
        </span>
      </button>
    </div>

    <div
      class="drawer-proj-list"
      :class="[open ? 'dopen' : 'dclose']"
    >
      <div class="proj-title">
        <p>Projects</p>
        <button @click.prevent="toggle">
          <svg
            width="27"
            height="27"
            viewBox="0 0 27 27"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M20.25 20.25L6.75 6.75"
              stroke="white"
              stroke-width="1.575"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M20.25 6.75L6.75 20.25"
              stroke="white"
              stroke-width="1.575"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
      <slot name="content" />
    </div>
  </div>

<!--     <transition name="fade">

    <div v-if="dimmer && open" @click="toggle" class="flex-1 bg-gray-400 bg-opacity-75 active:outline-none z-10">
    </div>
  </transition> -->
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const open = ref(false);
const right = ref(true);

const dropdownRef = ref(null); // Button
const dropDownWrapperRef = ref(null); // Parent  wrapper

const toggle = () => {
  open.value = !open.value;
};

const closeDropdownOnOutsideClick = (event) => {

  if (open.value && !dropDownWrapperRef.value.contains(event.target) ) {
    open.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', closeDropdownOnOutsideClick);
});

onUnmounted(() => {
  document.removeEventListener('click', closeDropdownOnOutsideClick);
});

</script>

<style scoped>

.card-change {
@apply absolute flex top-0 h-screen z-20
}

.proj-title {
@apply text-2xl font-medium leading-7 tracking-normal text-left flex items-center justify-between m-[1em];

}
.buttonm
{
position: relative;
}

.button {
@apply absolute translate-x-[-48.5px] translate-y-[-31px] m-0 bottom-0 rounded-tl-[1em] rounded-bl-[1em] flex justify-center items-center;
}

@media screen and (max-width: 480px) {
.button {
rotate: 90deg;
/* transform: translate(0, -286px); */
border-radius: 2em;
background: white;
position: absolute;
right: 96px;
max-height: fit-content;
bottom: -85px;
}
}

.proj-btn {
@apply flex gap-[1em] items-center;
}

.ro90 {
rotate: 90deg;

}

.ro270 {
rotate: 270deg;
}

.ro90,
.ro270 {
transition-duration: 0.5s;
}

.drawer-proj-list {
@apply transition-all text-white h-full duration-700 flex-col-reverse overflow-y-auto overflow-x-hidden items-center justify-center;
}

.drawer-proj-list{
background: rgba(0, 0, 0, 0.25);
backdrop-filter: blur(19px);
-webkit-backdrop-filter: blur(19px);
}

.drawer-proj-list::-webkit-scrollbar {
width: 7px;
}

.drawer-proj-list::-webkit-scrollbar-track {
background: rgb(189, 189, 189);
}

.drawer-proj-list::-webkit-scrollbar-thumb {
background: rgba(54, 54, 54, 0.411);
border-radius: 20px;

}

@media screen and (max-width: 480px) {
.proj-title {
display: none;
}

.card-change {
bottom: 0;
width: 100vw;
height: -moz-fit-content;
height: fit-content;
top: revert;
flex-direction: column;
}

.drawer-proj-list {
background-color: transparent;
overflow: hidden;
transition: max-height 0.3s ease-out;
justify-content: normal;
}

.dopen {
max-height:13em;
display: flex;
flex-direction: row;
overflow: scroll;
bottom: 0;

}

.dclose {
max-height: 0;
}
}

.fade-enter-active,
.fade-leave-active {
transition: opacity 1s ease-out;
}

.fade-enter,
.fade-leave-to {
opacity: 0;
}

.dopen {
@apply max-w-lg;
}

.dclose {
@apply max-w-0;
}
</style>
