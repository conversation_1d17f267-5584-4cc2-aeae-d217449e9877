import SideDrawer from './SideDrawer.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/SideBar',
  component: SideDrawer,
  tags: ['autodocs'],
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=1181-3940&mode=design&t=MJoHYvcCnJNPKlHr-4',
    },
  },
};

export const Primary = {
  args: {
    title: "Amenities",

  },
};
