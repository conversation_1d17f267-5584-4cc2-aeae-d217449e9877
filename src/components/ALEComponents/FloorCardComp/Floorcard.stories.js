import FloorCardComp from './FloorCardComp.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/FloorCardComp',
  component: FloorCardComp,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    floor: 42,
    availableUnits: 8,
    availabilityStatus: 'Available',
    unit: 34,
    minArea: 2500,
    maxArea: 3500,
    measurementType: 'sqft',
    isMobile: false,
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=1127-9917&mode=dev",
    },
  },
};
