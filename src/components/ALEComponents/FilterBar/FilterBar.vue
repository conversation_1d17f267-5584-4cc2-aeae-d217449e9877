<script setup>
import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { FwbButton, FwbRadio } from 'flowbite-vue';
import { creationToolStore } from '../../../store';
import MultiSelectButtons from '../MultiSelectButtons/MultiSelectButtons.vue';
import { formatBedrooms, getCookie, Googleanalytics, removeQueryParams } from "../../../helpers/helper";
import SliderInput from '../SliderInput/SliderInput.vue';
import Modal from '../../Overall/Modal/CustomModal.vue';
import router from '../../../router';
import TranslationComp from '../TranslationComp/TranslationComp.vue';

const store = creationToolStore();
const route = useRoute();
const picked = ref();
const selectedUnits = ref([]);
const selectedStyleType = ref([]);
const selectedBedroom = ref([]);
const initialAreaValue = ref([]);
const initialPriceValue = ref([]);
const floorRange = ref([]);
const Units = ref([]);
const styleTypes = ref([]);
const bedroomTypes = ref([]);
const minMeasurement = ref('');
const maxMeasurement = ref('');
const measurementType = ref(null);
const currencyType = ref(null);
const minPrice = ref(0);
const maxPrice = ref(0);
const floorValues = ref([]);
const areaRangeSlider = ref(null);
const floorRangeSlider = ref(null);
const priceRangeSlider = ref(null);
const unitsButton = ref(null);
const styleButton = ref(null);
const bedroomButton = ref(null);
const disableClear = ref(true);
const selectedCurrency = ref(store.currencyData.currency || getCookie('selectedCurrency'));
const selectedExchangeRatio = ref(store.currencyData.exchangeRatio);
const from_price = ref(false), to_price = ref(false), from_area= ref(false), to_area = ref(false), bedroom_numbers = ref(false), availability_status = ref(false);

// Props & Emits
const props = defineProps({
  openModal: {
    type: Boolean,
    required: true,
  },
  showFloors: {
    type: Boolean,
    required: true,
  },
  showStatusFilter: {
    type: Boolean,
    required: true,
  },
});

const emit = defineEmits(['close-modal', 'show-results', 'clear-filter', "close-onError", "empty-filter"]);

// Methods

function getSequenceNumbers (start, end) {
  const numbers = [];
  for (let i = start; i <= end; i++) {
    numbers.push(i);
  }
  return numbers;
}
function convertCurrency (price) {
  if (selectedCurrency.value && price && selectedExchangeRatio.value) {

    if (selectedCurrency.value === store.organizationDetails.baseCurrency) {
      return price * 1;
    }
    const rateData = selectedExchangeRatio.value.find((item) => item.currency === selectedCurrency.value);
    return rateData ? Math.round(price * rateData.rate) : null;
  }
  return price;
}
function convertCurrencyToOriginal (price) {
  if (selectedCurrency.value && price && selectedExchangeRatio.value) {

    if (selectedCurrency.value === store.organizationDetails.baseCurrency) {
      return price * 1;
    }
    const rateData = selectedExchangeRatio.value.find((item) => item.currency === selectedCurrency.value);
    return rateData ? Math.round(price / rateData.rate) : null;
  }
  return price;
}

const handleFilters = (val, type) => {
  const queryObject = {
    ...((type === 'price' && val && val.length !== 0) && { 'min_price': convertCurrencyToOriginal(val.value[0]), 'max_price': convertCurrencyToOriginal(val.value[1]) }),
    ...((val && val.length !== 0 && type === 'area') && { 'min_area': val.value[0], 'max_area': val.value[1] }),
    ...((val && val.length !== 0 && type === 'floor') && { 'floor': getSequenceNumbers(val[0], val[1]) }),
    ...((type === 'unit' && val) && { 'unitplan_type': val }),
    ...((type === 'style' && val) && { 'style_types': val }),
    ...((type === 'bedroom' && val) && { 'bedrooms': val }),
    ...((type === 'status') && { 'status': val ? val : '' }),
  };
  if (Object.keys(queryObject).length !== 0) {
    disableClear.value = false;
    emit("show-results", queryObject);
  }

  // for google analytics
  if (type === 'price' && val && val.length !== 0) {
    from_price.value = val.value[0];
    to_price.value = val.value[1];
  } else if (val && val.length !== 0 && type === 'area') {
    from_area.value = val.value[0];
    to_area.value = val.value[1];
  } else if (type === 'bedroom' && val) {
    bedroom_numbers.value = formatBedrooms(val);
  } else if (type === 'status') {
    availability_status.value = val || '';
  }

  Googleanalytics('filter_clicked', {
    bedroom_number: bedroom_numbers.value,
    from_price: from_price.value,
    to_price: to_price.value,
    from_area: from_area.value,
    to_area: to_area.value,
    availability_tag: availability_status.value,
    organization_id: route.params.organizationId,
    organization_name: store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: store.projectCardData?.[route.params.projectId]?.name,
  });
};

function resetSlider () {
  if (areaRangeSlider.value) {
    areaRangeSlider.value.clear();
  }
  if (floorRangeSlider.value) {
    floorRange.value = [];
    floorRangeSlider.value.resetSlider();
  }
  if (priceRangeSlider.value) {
    priceRangeSlider.value.clear();
  }
}

function handleButtonReset () {
  // Console.log('Buttons Reset to New Value', newValue);
}
function resetButtons () {
  if (unitsButton.value) {
    selectedUnits.value = [];
    unitsButton.value.resetButtons();
  }

  if (styleButton.value) {
    selectedStyleType.value = [];
    styleButton.value.resetButtons();
  }

  if (bedroomButton.value) {
    selectedBedroom.value = [];
    bedroomButton.value.resetButtons();
  }
}

function handleClear () {
  picked.value = '';
  resetButtons();
  resetSlider();
  const queryObject = removeQueryParams(route.query, route.params.organizationId);
  router.replace({ name: route.name, query: queryObject });
  disableClear.value = true;
  emit('clear-filter');
}

function closeModal () {
  emit("close-modal");
}

function emptyFilterChecker () {
  if (props.showStatusFilter && !(Units.value.length > 1) && !(styleTypes.value.length > 1) && !(bedroomTypes.value && bedroomTypes.value.length > 1) && !(minMeasurement.value < maxMeasurement.value) && !(minPrice.value < maxPrice.value)) {
    emit('empty-filter', true);
  } else {
    emit('empty-filter', false);
  }
}

// Initial Values
function initialValueSetter () {
  Units.value = store.filteredDataPoints.unitData?.uniqueUnitPlanTypes ?? [];
  styleTypes.value = store.filteredDataPoints.unitplanData?.uniqueStyleTypes ?? [];
  bedroomTypes.value = store.filteredDataPoints.unitData?.uniqueBedroomTypes ?? [];
  minPrice.value = convertCurrency(store.filteredDataPoints.unitData?.minPrice) ?? 0;
  maxPrice.value = convertCurrency(store.filteredDataPoints.unitData?.maxPrice) ?? 0;
  minMeasurement.value = parseInt(store.filteredDataPoints.unitData?.minMeasurement ?? 0);
  maxMeasurement.value = Math.ceil(store.filteredDataPoints.unitData?.maxMeasurement ?? 0);
  floorValues.value = [store.filteredDataPoints.unitData?.minFloor, store.filteredDataPoints.unitData?.maxFloor] ?? [];
  measurementType.value =
    store.filteredDataPoints.unitplanData?.measurementType === "sqft"
      ? "sq.ft"
      : store.filteredDataPoints.unitplanData?.measurementType === "sqmt"
        ? "sq.mt"
        : store.filteredDataPoints.unitData?.measurementType || "";
  currencyType.value = selectedCurrency.value ?? '';
  emptyFilterChecker();
}

function queryValueSetter () {
  emptyFilterChecker();
  picked.value = route.query.status ? route.query.status : '';
  selectedUnits.value = route.query.unitplan_type
    ? Array.isArray(route.query.unitplan_type)
      ? [...route.query.unitplan_type]
      : [route.query.unitplan_type]
    : [];
  selectedStyleType.value = route.query.style_types
    ? Array.isArray(route.query.style_types)
      ? [...route.query.style_types]
      : [route.query.style_types]
    : [];
  selectedBedroom.value = route.query.bedrooms
    ? Array.isArray(route.query.bedrooms)
      ? [...route.query.bedrooms]
      : [route.query.bedrooms]
    : [];
  initialAreaValue.value = [route.query.min_area, route.query.max_area];
  initialPriceValue.value = [convertCurrency(route.query.min_price), convertCurrency(route.query.max_price)];
}

watch(() => store.filteredDataPoints, () => {
  initialValueSetter();
});

watch(() => store.currencyData, (newVal) => {
  selectedCurrency.value = newVal;
  initialValueSetter();
});

watch(
  () => route.query,
  () => {
    queryValueSetter();
  },
  { deep: true },
);

onMounted(() => {
  initialValueSetter();
  // Route Value Setter
  if (Object.keys(route.query).length > 0) {
    queryValueSetter();
  }
});
</script>

<template>
  <modal
    :isOpen="props.openModal"
    title="FILTERS"
    :showOverlay="false"
    :size="store.isMobile || store.isLandscape ? '2xl' : 'xs'"
    modalPlacement="bottom-right"
    class="modal-class"
    :class="store.isMobile || store.isLandscape? 'h-full w-full overflow-y-auto' : 'mb-24 mr-3'"
    @update:is-open="closeModal"
  >
    <template #content>
      <div :class="store.isMobile? 'h-[40vh] overflow-y-auto p-4' : store.isLandscape ? 'h-[50vh] overflow-y-auto p-4' :'p-3 overflow-y-auto h-[55vh]'">
        <div v-if="!showStatusFilter">
          <label
            for="status"
            class="label-primary"
          >
            <TranslationComp
              text="Status"
            /> </label>
          <ul
            class="flex border border-gray-600 rounded-lg dark:bg-tertiary50opacity dark:border-gray-600 mb-4 radio-item w-fill"
          >
            <li class="w-full flex items-center justify-center p-3 border-r border-gray-600 dark:border-gray-600 ">
              <fwb-radio
                v-model="picked"

                name="radio-horizontal"
                value="all"
                @change="handleFilters('', 'status')"
              >
                <TranslationComp
                  text="All"
                  class="text-sm text-secondaryText"
                />
              </fwb-radio>
            </li>
            <li class="w-full flex items-center justify-center p-3 border-r border-gray-600 dark:border-gray-600">
              <fwb-radio
                v-model="picked"
                name="radio-horizontal"
                value="available"
                @change="handleFilters('available', 'status')"
              >
                <TranslationComp
                  text="Available"
                  class="text-sm text-secondaryText"
                />
              </fwb-radio>
            </li>
            <li class="w-full flex items-center justify-center p-3 mr-1 dark:border-gray-600">
              <fwb-radio
                v-model="picked"
                name="radio-horizontal"
                value="unavailable"
                @change="handleFilters('unavailable', 'status')"
              >
                <TranslationComp
                  text="Unavailable"
                  class="text-sm text-secondaryText"
                />
              </fwb-radio>
            </li>
          </ul>
        </div>
        <div
          v-if="Units && Units.length > 1"
          class="mb-4"
        >
          <label
            for="UnitType"
            class="label-primary"
          >
            <TranslationComp
              text="Unit Type"
            /></label>
          <MultiSelectButtons
            ref="unitsButton"
            :arrayValues="Units"
            :selectedArrayItems="selectedUnits"
            @update:selected-items="(val) => { selectedUnits = val; handleFilters(val, 'unit'); }"
            @reset="handleButtonReset"
          />
        </div>
        <div
          v-if="styleTypes && styleTypes.length > 1"
          class="mb-4"
        >
          <label
            for="Style"
            class="label-primary"
          >
            Style </label>
          <MultiSelectButtons
            ref="styleButton"
            :arrayValues="styleTypes"
            :selectedArrayItems="selectedStyleType"
            @update:selected-items="(val) => { selectedStyleType = val; handleFilters(val, 'style'); }"
            @reset="handleButtonReset"
          />
        </div>

        <div
          v-if="bedroomTypes && bedroomTypes.length > 1"
          class="mb-4"
        >
          <label
            for="Style"
            class="label-primary"
          >
            <TranslationComp
              text="Bedroom"
            />
          </label>
          <MultiSelectButtons
            ref="bedroomButton"
            :arrayValues="bedroomTypes"
            :selectedArrayItems="selectedBedroom"
            @update:selected-items="(val) => { selectedBedroom = val; handleFilters(val, 'bedroom'); }"
            @reset="handleButtonReset"
          />
        </div>
        <div
          v-if="parseInt(minMeasurement) < parseInt(maxMeasurement)"
          class="mb-4"
        >
          <SliderInput
            ref="areaRangeSlider"
            labelType="Area"
            :type="measurementType"
            :minValue="minMeasurement"
            :maxValue="maxMeasurement"
            :initialMin="initialAreaValue[0]"
            :initialMax="initialAreaValue[1]"
            @update:range="(val) => handleFilters(val, 'area')"
          />
        </div>
        <div
          v-if="parseInt(minPrice) < parseInt(maxPrice)"
          class="mb-4"
        >
          <SliderInput
            ref="priceRangeSlider"
            labelType="Price Range"
            :type="currencyType ? currencyType : store.organizationDetails.baseCurrency"
            :minValue="minPrice"
            :maxValue="maxPrice"
            :initialMin="initialPriceValue[0]"
            :initialMax="initialPriceValue[1]"
            @update:range="(val) => handleFilters(val, 'price')"
          />
        </div>

        <!-- Floors needs to be updated when floors is added to the API -->
        <div v-if="floorValues && floorValues.length !== 0 && showFloors">
          <SliderInput
            ref="floorRangeSlider"
            label="Floor"
            :minValue="parseInt(floorValues[0])"
            :maxValue="parseInt(floorValues[1])"
            :initialMin="parseInt(floorValues[0])"
            :initialMax="parseInt(floorValues[1])"
            @update:range="(val) => handleFilters(val, 'floor')"
          />
        </div>
      </div>
    </template>
    <template #footer>
      <div
        :class="[store.isMobile || store.isLandscape ? 'flex justify-start items-center p-3 gap-5' : 'flex justify-start items-center button-group']"
      >
        <fwb-button
          v-if="store.isMobile || store.isLandscape"
          color="default"
          class="bg-primary text-sm font-medium text-primaryText"
          @click="closeModal"
        >
          <TranslationComp
            text=" Show Results"
          />
        </fwb-button>
        <fwb-button
          class="bg-secondary text-sm text-secondaryText hover:bg-secondary hover:text-secondaryText modal-class border border-gray-600"
          :disabled="disableClear"
          @click="handleClear"
        >
          <TranslationComp
            text="Clear All"
          />
        </fwb-button>
      </div>
    </template>
  </modal>
</template>

<style>
.modal-class .label-primary {
  @apply block text-secondaryText text-sm font-medium p-2;
}

li label span {
  margin-left: 10px !important;
  color: var(--secondaryText) !important;
}

.radio-item li label input:checked {
  color: #0c7aff;
}
.radio-item li label input:focus {
  box-shadow: none;
}
.button-group {
  gap: 20px;
  padding: 4px;
  padding-top: 1rem;
}

::-webkit-scrollbar {
  width: 0;
}

/* Track */
::-webkit-scrollbar-track {
  background: var(--secondaryText);
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--secondaryText);
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: var(--secondaryText);
}

input[type="radio"]:checked {
  background-color: var(--colormix) !important; /* Red */
  border-color: var(--colormix) !important;
}

.filterbar-landscape {
  height: calc(100vh - var(--navbar-height, 45px));
}
</style>
