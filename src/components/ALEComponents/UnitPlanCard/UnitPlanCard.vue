<script setup>

import SvgButton from "../svgButton/svgButton.vue";
import { thousandSeparator } from "../../../helpers/helper.js";
import { ref } from "vue";

const { selectItem, thumbnail, name, minMeasurement, maxMeasurement, measurement, measurementType, bedrooms } = defineProps({
  selectItem: {
    type: Boolean, default: false,
  },
  name: {
    type: String, default: "",
  },
  thumbnail: {
    type: String, default: "",
  },
  measurementType: {
    type: String, default: "",
  },
  measurement: {
    type: Number, default: 0,
  },
  minMeasurement: {
    type: Number, default: 0,
  },
  maxMeasurement: {
    type: Number, default: 0,
  },

  bedrooms: {
    type: Number, default: 0,
  },
  allowEnterVr: {
    type: Boolean, default: true,
  },
  favoritesStatus: {
    type: Boolean, default: false,
  },
});

const circularloader = ref(true);
const emits = defineEmits(['handleSelection', 'enterVR', 'handleClose', 'handleFavorite']);

</script>
<template>
  <div class="w-full sm:w-fit h-fit">
    <div
      class="hidden sm:flex w-[377px] h-[401px] justify-start items-start flex-col  relative bg-opacity-60 border rounded-lg backdrop-blur-2xl"
      :class="[(selectItem ? 'bg-black border-white' : 'bg-neutral-800 border-transparent')]"
      @click="() => emits('handleSelection')"
    >
      <div :class="[(selectItem ? 'border-white': 'border-gray-400'),'w-full py-3 px-16 border-b relative']">
        <div class="h-[250px] flex justify-center items-center ">
          <div
            v-if="circularloader"
            class="z-10 absolute top-0 left-0 h-full w-full bg-black bg-opacity-40 backdrop-blur-[20px] flex"
          >
            <div class="m-auto loader border-4 border-gray-200 border-t-4 border-t-black rounded-full w-12 h-12 animate-spin" />
          </div>

          <div class="z-[1] flex absolute md:top-[50%] top-[40%] left-[50%] -translate-x-[50%] -translate-y-[50%]">
            <img
              class="object-contain"
              :src="thumbnail"
              alt=""
              loading="lazy"
              @load="()=> circularloader = false "
            >
          </div>
        </div>
      </div>
      <div class="px-5 py-[22px] flex flex-col justify-start items-start gap-4 w-full">
        <div class="flex justify-between items-center w-full gap-1 flex-nowrap relative">
          <h6 class="text-white text-xl font-semibold uppercase inline text-ellipsis whitespace-nowrap w-full overflow-hidden">
            {{ name.replace(/[^a-zA-Z0-9]/g,' ') }}
          </h6>

          <button
            class="absolute right-0 h-8 w-8 p-2 bg-InventoryFloorPlateCardIconBg bg-opacity-60 backdrop-blur-sm rounded-full flex justify-start items-center gap-2 "
            @click="(event) => { event.stopPropagation(); emits('handleFavorite'); }"
          >
            <svg
              id="goToFavotiteSvg"
              class="w-8 h-8"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10 16.6668C10 16.6668 2.5 12.4897 2.5 7.47711C2.5 2.46452 8.33333 2.0468 10 5.96867C11.6667 2.0468 17.5 2.46452 17.5 7.47711C17.5 12.4897 10 16.6668 10 16.6668Z"
                :stroke="favoritesStatus?'#FF4747':'#FFFFFF'"
                stroke-width="1.4"
                stroke-linecap="round"
                stroke-linejoin="round"
                :fill="favoritesStatus?'#FF4747':'none'"
              />
            </svg>
          </button>
        </div>

        <div class="w-full flex justify-start items-center gap-5">
          <div
            v-if="(minMeasurement && maxMeasurement ) || measurement"
            class="flex justify-start items-center gap-2"
          >
            <svg
              class="w-6 h-6"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M5.45491 7.06704C5.16158 7.34066 4.70198 7.32468 4.42837 7.03135L2.95192 5.44853C2.69104 5.16885 2.69175 4.73478 2.95354 4.45595L4.42999 2.88346C4.70456 2.59102 5.16421 2.57654 5.45664 2.85112C5.74908 3.12569 5.76356 3.58534 5.48898 3.87777L5.12474 4.26571H13.3346L12.973 3.87808C12.6994 3.58475 12.7154 3.12515 13.0087 2.85154C13.302 2.57792 13.7616 2.5939 14.0353 2.88723L15.5117 4.47004C15.7726 4.74972 15.7719 5.1838 15.5101 5.46263L14.0336 7.03512C13.7591 7.32755 13.2994 7.34203 13.007 7.06746C12.7145 6.79289 12.7001 6.33324 12.9746 6.04081L13.3367 5.65519H5.13119L5.4906 6.04049C5.76422 6.33382 5.74824 6.79342 5.45491 7.06704ZM14.078 9.74978H3.85742C3.78839 9.74978 3.73242 9.80574 3.73242 9.87478V20.0785C3.73242 20.1476 3.78839 20.2035 3.85742 20.2035H14.078C14.1471 20.2035 14.203 20.1476 14.203 20.0785V9.87478C14.203 9.80574 14.1471 9.74978 14.078 9.74978ZM3.85742 8.74978C3.2361 8.74978 2.73242 9.25346 2.73242 9.87478V20.0785C2.73242 20.6998 3.2361 21.2035 3.85742 21.2035H14.078C14.6993 21.2035 15.203 20.6998 15.203 20.0785V9.87478C15.203 9.25346 14.6993 8.74978 14.078 8.74978H3.85742ZM16.9164 9.95788C16.6231 10.2315 16.6071 10.6911 16.8807 10.9844C17.1544 11.2778 17.614 11.2937 17.9073 11.0201L18.2961 10.6575V19.2898L17.9104 18.9278C17.618 18.6532 17.1584 18.6677 16.8838 18.9601C16.6092 19.2525 16.6237 19.7122 16.9161 19.9868L18.4886 21.4632C18.7675 21.725 19.2015 21.7257 19.4812 21.4648L21.064 19.9884C21.3574 19.7148 21.3733 19.2552 21.0997 18.9618C20.8261 18.6685 20.3665 18.6525 20.0732 18.9261L19.6855 19.2877V10.6575L20.07 11.0185C20.3624 11.2931 20.8221 11.2786 21.0967 10.9862C21.3712 10.6937 21.3568 10.2341 21.0643 9.9595L19.4918 8.48306C19.213 8.22126 18.7789 8.22055 18.4992 8.48144L16.9164 9.95788Z"
                fill="white"
              />
            </svg>
            <p
              v-if="minMeasurement && maxMeasurement"
              class="text-white text-base font-normal capitalize"
            >
              {{ `${thousandSeparator(minMeasurement)} - ${thousandSeparator(maxMeasurement)}` }} {{ measurementType }}
            </p>
            <p
              v-else
              class="text-white text-base font-normal capitalize"
            >
              {{ thousandSeparator(measurement) }} {{ measurementType }}
            </p>
          </div>
          <div
            v-if="bedrooms"
            class="flex justify-start items-center gap-2"
          >
            <svg
              class="w-6 h-6"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19.5 6.75H2.25V4.5C2.25 4.30109 2.17098 4.11032 2.03033 3.96967C1.88968 3.82902 1.69891 3.75 1.5 3.75C1.30109 3.75 1.11032 3.82902 0.96967 3.96967C0.829018 4.11032 0.75 4.30109 0.75 4.5V19.5C0.75 19.6989 0.829018 19.8897 0.96967 20.0303C1.11032 20.171 1.30109 20.25 1.5 20.25C1.69891 20.25 1.88968 20.171 2.03033 20.0303C2.17098 19.8897 2.25 19.6989 2.25 19.5V16.5H21.75V19.5C21.75 19.6989 21.829 19.8897 21.9697 20.0303C22.1103 20.171 22.3011 20.25 22.5 20.25C22.6989 20.25 22.8897 20.171 23.0303 20.0303C23.171 19.8897 23.25 19.6989 23.25 19.5V10.5C23.25 9.50544 22.8549 8.55161 22.1516 7.84835C21.4484 7.14509 20.4946 6.75 19.5 6.75ZM2.25 8.25H9V15H2.25V8.25ZM10.5 15V8.25H19.5C20.0967 8.25 20.669 8.48705 21.091 8.90901C21.5129 9.33097 21.75 9.90326 21.75 10.5V15H10.5Z"
                fill="white"
              />
            </svg>
            <p class=" text-white text-base font-normal capitalize">
              {{ bedrooms }}
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="flex sm:hidden flex-col justify-start items-start w-full bg-neutral-900 bg-opacity-60 rounded-tl-[20px] rounded-tr-[20px] backdrop-blur-[100px] p-5">
      <div class="flex justify-between items-center w-full mb-5 flex-nowrap">
        <div class="text-left text-white text-xl font-semibold leading-tight tracking-tight inline text-ellipsis whitespace-nowrap w-full overflow-hidden">
          {{ name.replace(/[^a-zA-Z0-9]/g,' ') }}
        </div>
        <svg
          class="w-7 h-7"
          viewBox="0 0 27 27"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          @click="emits('handleClose')"
        >
          <path
            d="M20.25 20.25L6.75 6.75"
            stroke="white"
            stroke-width="1.575"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M20.25 6.75L6.75 20.25"
            stroke="white"
            stroke-width="1.575"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div class="w-full flex justify-start items-center gap-5 mb-5">
        <div
          v-if="(minMeasurement && maxMeasurement ) || measurement"
          class="flex justify-start items-center gap-2"
        >
          <svg
            class="w-6 h-6"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M5.45491 7.06704C5.16158 7.34066 4.70198 7.32468 4.42837 7.03135L2.95192 5.44853C2.69104 5.16885 2.69175 4.73478 2.95354 4.45595L4.42999 2.88346C4.70456 2.59102 5.16421 2.57654 5.45664 2.85112C5.74908 3.12569 5.76356 3.58534 5.48898 3.87777L5.12474 4.26571H13.3346L12.973 3.87808C12.6994 3.58475 12.7154 3.12515 13.0087 2.85154C13.302 2.57792 13.7616 2.5939 14.0353 2.88723L15.5117 4.47004C15.7726 4.74972 15.7719 5.1838 15.5101 5.46263L14.0336 7.03512C13.7591 7.32755 13.2994 7.34203 13.007 7.06746C12.7145 6.79289 12.7001 6.33324 12.9746 6.04081L13.3367 5.65519H5.13119L5.4906 6.04049C5.76422 6.33382 5.74824 6.79342 5.45491 7.06704ZM14.078 9.74978H3.85742C3.78839 9.74978 3.73242 9.80574 3.73242 9.87478V20.0785C3.73242 20.1476 3.78839 20.2035 3.85742 20.2035H14.078C14.1471 20.2035 14.203 20.1476 14.203 20.0785V9.87478C14.203 9.80574 14.1471 9.74978 14.078 9.74978ZM3.85742 8.74978C3.2361 8.74978 2.73242 9.25346 2.73242 9.87478V20.0785C2.73242 20.6998 3.2361 21.2035 3.85742 21.2035H14.078C14.6993 21.2035 15.203 20.6998 15.203 20.0785V9.87478C15.203 9.25346 14.6993 8.74978 14.078 8.74978H3.85742ZM16.9164 9.95788C16.6231 10.2315 16.6071 10.6911 16.8807 10.9844C17.1544 11.2778 17.614 11.2937 17.9073 11.0201L18.2961 10.6575V19.2898L17.9104 18.9278C17.618 18.6532 17.1584 18.6677 16.8838 18.9601C16.6092 19.2525 16.6237 19.7122 16.9161 19.9868L18.4886 21.4632C18.7675 21.725 19.2015 21.7257 19.4812 21.4648L21.064 19.9884C21.3574 19.7148 21.3733 19.2552 21.0997 18.9618C20.8261 18.6685 20.3665 18.6525 20.0732 18.9261L19.6855 19.2877V10.6575L20.07 11.0185C20.3624 11.2931 20.8221 11.2786 21.0967 10.9862C21.3712 10.6937 21.3568 10.2341 21.0643 9.9595L19.4918 8.48306C19.213 8.22126 18.7789 8.22055 18.4992 8.48144L16.9164 9.95788Z"
              fill="white"
            />
          </svg>
          <p
            v-if="minMeasurement && maxMeasurement"
            class="text-white text-base font-normal capitalize"
          >
            {{ `${thousandSeparator(minMeasurement)} - ${thousandSeparator(maxMeasurement)}` }} {{ measurementType }}
          </p>
          <p
            v-else
            class="text-white text-base font-normal capitalize"
          >
            {{ thousandSeparator(measurement) }} {{ measurementType }}
          </p>
        </div>
        <div
          v-if="bedrooms"
          class="flex justify-start items-center gap-2"
        >
          <svg
            class="w-6 h-6"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.5 6.75H2.25V4.5C2.25 4.30109 2.17098 4.11032 2.03033 3.96967C1.88968 3.82902 1.69891 3.75 1.5 3.75C1.30109 3.75 1.11032 3.82902 0.96967 3.96967C0.829018 4.11032 0.75 4.30109 0.75 4.5V19.5C0.75 19.6989 0.829018 19.8897 0.96967 20.0303C1.11032 20.171 1.30109 20.25 1.5 20.25C1.69891 20.25 1.88968 20.171 2.03033 20.0303C2.17098 19.8897 2.25 19.6989 2.25 19.5V16.5H21.75V19.5C21.75 19.6989 21.829 19.8897 21.9697 20.0303C22.1103 20.171 22.3011 20.25 22.5 20.25C22.6989 20.25 22.8897 20.171 23.0303 20.0303C23.171 19.8897 23.25 19.6989 23.25 19.5V10.5C23.25 9.50544 22.8549 8.55161 22.1516 7.84835C21.4484 7.14509 20.4946 6.75 19.5 6.75ZM2.25 8.25H9V15H2.25V8.25ZM10.5 15V8.25H19.5C20.0967 8.25 20.669 8.48705 21.091 8.90901C21.5129 9.33097 21.75 9.90326 21.75 10.5V15H10.5Z"
              fill="white"
            />
          </svg>
          <p class=" text-white text-base font-normal capitalize">
            {{ bedrooms }}
          </p>
        </div>
      </div>

      <div class="w-full flex gap-6 justify-between items-center mb-3">
        <SvgButton
          v-if="allowEnterVr !== 'false' && allowEnterVr"
          theme="light"
          title="Enter Room"
          class="button flex h-12 justify-center items-center gap-3 backdrop-blur px-6 py-3.5 rounded-[50px] whitespace-nowrap w-full"
          @go-to="() => emits('enterVR')"
        >
          <template #svg>
            <svg
              class="w-5 h-5"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clip-path="url(#clip0_135_11284)">
                <path
                  d="M8.54172 9.375H0.625C0.279999 9.375 0 9.095 0 8.75C0 8.405 0.279999 8.125 0.625 8.125H8.54172C8.88672 8.125 9.16672 8.405 9.16672 8.75C9.16672 9.095 8.88672 9.375 8.54172 9.375Z"
                  fill="black"
                />
                <path
                  d="M5.41684 12.5C5.25677 12.5 5.09686 12.4391 4.9751 12.3167C4.73096 12.0724 4.73096 11.6766 4.9751 11.4325L7.65851 8.74924L4.9751 6.06674C4.73096 5.82245 4.73096 5.42664 4.9751 5.1825C5.21924 4.93835 5.61505 4.93835 5.85934 5.1825L8.98434 8.3075C9.22849 8.55164 9.22849 8.94745 8.98434 9.19174L5.85934 12.3167C5.73682 12.4391 5.57675 12.5 5.41684 12.5Z"
                  fill="black"
                />
                <path
                  d="M13.3333 19.9999C12.4141 19.9999 11.6667 19.2524 11.6667 18.3332V3.33322C11.6667 2.62079 12.1201 1.98495 12.795 1.75073L17.8017 0.0815712C18.9217 -0.261752 20 0.569089 20 1.66665V16.6667C20 17.3783 19.5467 18.0132 18.8726 18.2482L13.8642 19.9183C13.6817 19.9749 13.5126 19.9999 13.3333 19.9999ZM18.3333 1.24994C18.2791 1.24994 18.2334 1.25665 18.1842 1.2716L13.1984 2.93405C13.035 2.99081 12.9167 3.15576 12.9167 3.33322V18.3332C12.9167 18.6175 13.225 18.8091 13.4825 18.7283L18.4684 17.0658C18.6309 17.0091 18.75 16.8441 18.75 16.6667V1.66665C18.75 1.43747 18.5634 1.24994 18.3333 1.24994Z"
                  fill="black"
                />
                <path
                  d="M7.29175 4.16672C6.94675 4.16672 6.66675 3.88672 6.66675 3.54172V2.29172C6.66675 1.02753 7.69412 0 8.95831 0H18.3333C18.6783 0 18.9583 0.279999 18.9583 0.625C18.9583 0.970001 18.6783 1.25 18.3333 1.25H8.95831C8.38412 1.25 7.91675 1.71753 7.91675 2.29172V3.54172C7.91675 3.88672 7.63675 4.16672 7.29175 4.16672Z"
                  fill="black"
                />
                <path
                  d="M12.2917 17.5H8.95831C7.69412 17.5 6.66675 16.4724 6.66675 15.2083V13.9583C6.66675 13.6133 6.94675 13.3333 7.29175 13.3333C7.63675 13.3333 7.91675 13.6133 7.91675 13.9583V15.2083C7.91675 15.7824 8.38412 16.25 8.95831 16.25H12.2917C12.6367 16.25 12.9167 16.53 12.9167 16.875C12.9167 17.22 12.6367 17.5 12.2917 17.5Z"
                  fill="black"
                />
              </g>
              <defs>
                <clipPath id="clip0_135_11284">
                  <rect
                    width="20"
                    height="20"
                    fill="white"
                  />
                </clipPath>
              </defs>
            </svg>
          </template>
        </SvgButton>

        <button
          class=" h-8 w-8 p-2 bg-InventoryFloorPlateCardIconBg bg-opacity-60 backdrop-blur-sm rounded-full flex justify-start items-center gap-2 "
          @click="() => emits('handleFavorite')"
        >
          <svg
            id="goToFavotiteSvg"
            class="w-8 h-8"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10 16.6668C10 16.6668 2.5 12.4897 2.5 7.47711C2.5 2.46452 8.33333 2.0468 10 5.96867C11.6667 2.0468 17.5 2.46452 17.5 7.47711C17.5 12.4897 10 16.6668 10 16.6668Z"
              :stroke="favoritesStatus?'#FF4747':'#FFFFFF'"
              stroke-width="1.4"
              stroke-linecap="round"
              stroke-linejoin="round"
              :fill="favoritesStatus?'#FF4747':'none'"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>
