<script setup>
import { onMounted, onUnmounted, ref } from "vue";
import { FwbButton } from "flowbite-vue";

const props = defineProps({
  url: { type: String, default: "" },
});

function createUniqueRandomGenerator (min, max) {
  const available = [];

  // Fill the array with all possible numbers
  for (let i = min; i <= max; i++) {
    available.push(i);
  }

  return function getNextRandom () {
    if (available.length === 0) {
      throw new Error("All unique numbers have been used.");
    }
    const index = Math.floor(Math.random() * available.length);
    return available.splice(index, 1)[0]; // remove and return the number
  };
}

const id = createUniqueRandomGenerator(1, 10000)();
const pdfContainerId = ref(`auto_pdf_container_${id}`);
const showControls = ref(false);
const currentPage = ref(1);
const totalPages = ref(0);

let dFlipScript;
let dFlipStyleTag;

onMounted(() => {
  // Replace with your own nonce value that matches the CSP header
  const nonce = "hu2832y1he22y82121u89";

  window.addEventListener("message", function () {
    if (window?.[`dFlipInstance_${id}`]?.target?.pageCount) {
      showControls.value = true;
      totalPages.value = window[`dFlipInstance_${id}`].target.pageCount;
    }
  });

  // Create and append the style tag
  dFlipStyleTag = document.createElement("style");
  dFlipStyleTag.textContent = `
    .ti-angle-right:before, .ti-angle-left:before {
      color: white;
      font-weight: bolder;
    }

    /* Hide default previous and next buttons */
    .df-ui-prev, .df-ui-next {
      display: none !important;
    }

    /* Custom styles for scrollable container */
    #${pdfContainerId.value} {
      height: 100vh;
      overflow-y: auto;
    }
  `;
  document.head.appendChild(dFlipStyleTag);

  // Create and append the script tag with nonce
  dFlipScript = document.createElement("script");
  dFlipScript.setAttribute("nonce", nonce);
  dFlipScript.textContent = `
    var option_pdf = {
      hideControls: "share,thumbnail,download,more,pageMode,startPage,endPage,sound,pageNumber,play,outline,zoomIn,zoomOut,fullScreen",
      webgl: true,
      backgroundColor: "black",
      height: "80%",
      autoEnableOutline: false,
      autoEnableThumbnail: false,
      overwritePDFOutline: false,
      enableDownload: false,
      pageMode: DFLIP.PAGE_MODE.SINGLE,
      singlePageMode: true,
      scrollWheel: true,
      direction: DFLIP.DIRECTION.VERTICAL,
      autoPlay: false,
      autoPlayDuration: 3000,
      mouseScrollTimeSpan: 500,
      mouseScrollDistance: 400,
      onReady:function (flipbook) {
                window.parent.postMessage(JSON.stringify({type:'dFlip',status:1}));
              },
    };

    // Initialize dFlip
    $(document).ready(function() {
      var flipInstance = $("#${pdfContainerId.value}").flipBook("${props.url.replace(/\\/g, "\\\\").replace(/"/g, '\\"')}", option_pdf);
      
      // Assign dFlipInstance only when initialization is complete
      if (flipInstance) {
        window.dFlipInstance_${id} = flipInstance;
        console.log(window.dFlipInstance_${id})
      }
    });
  `;
  document.body.appendChild(dFlipScript);
});

const goToNextPage = () => {
  if (window?.[`dFlipInstance_${id}`] && window?.[`dFlipInstance_${id}`]?.next) {
    window?.[`dFlipInstance_${id}`].next();
    currentPage.value = window?.[`dFlipInstance_${id}`]?.target?._activePage;
  }
};

const goToPreviousPage = () => {
  if (window?.[`dFlipInstance_${id}`] && window?.[`dFlipInstance_${id}`]?.prev) {
    window?.[`dFlipInstance_${id}`].prev();
    currentPage.value = window?.[`dFlipInstance_${id}`]?.target?._activePage;
  }
};

onUnmounted(() => {
  if (dFlipScript) {
    document.body.removeChild(dFlipScript);
  }
  if (dFlipStyleTag) {
    document.head.removeChild(dFlipStyleTag);
  }
  window?.[`dFlipInstance_${id}`].dispose();
  // Clear the global dFlipInstance when the component is destroyed
  delete window?.[`dFlipInstance_${id}`];
  // window.currentPage = null;
  // window.totalPages = null;
});
</script>

<template>
  <div class="w-full h-full flex flex-col items-center justify-center">
    <div
      :id="pdfContainerId"
      class="w-full h-full m-auto"
    />
    <div
      v-if="showControls"
      class="absolute bottom-[6rem] controls flex justify-center gap-4 w-full items-center z-[1]"
    >
      <div>
        <FwbButton
          class="bg-gray-800 !border-none hover:bg-gray-800 [&>div]:mr-0 h-10 w-10 rounded-[80px] flex justify-center items-center dark:focus:ring-0 focus:ring-0"
          :disabled="currentPage === 1"
          @click="goToPreviousPage"
        >
          <template #prefix>
            <slot class="h-5 w-5 m-0">
              <svg
                class="h-4 w-4"
                viewBox="0 0 21 21"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18.375 10.5L2.625 10.5"
                  stroke="white"
                  stroke-width="1.4"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M7 14.875L2.625 10.5L7 6.125"
                  stroke="white"
                  stroke-width="1.4"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </slot>
          </template>
        </FwbButton>
      </div>
      <div>
        <FwbButton
          class="bg-gray-800 !border-none hover:bg-gray-800 [&>div]:mr-0 h-10 w-10 rounded-[80px] flex justify-center items-center dark:focus:ring-0 focus:ring-0"
          :disabled="currentPage === totalPages"
          @click="goToNextPage"
        >
          <template #prefix>
            <slot class="h-5 w-5 m-0">
              <svg
                class="h-4 w-4"
                viewBox="0 0 21 21"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M2.625 10.5L18.375 10.5"
                  stroke="white"
                  stroke-width="1.4"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 14.875L18.375 10.5L14 6.125"
                  stroke="white"
                  stroke-width="1.4"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </slot>
          </template>
        </FwbButton>
      </div>
      <div style="height: 17px; border: 1px white solid" />
      <div class="flex items-center gap-1.5">
        <div
          class="h-8 w-8 bg-[#374151] flex justify-center items-center rounded"
        >
          <fwb-P class="text-white">
            {{ currentPage }}
          </fwb-P>
        </div>
        <div>
          <fwb-P class="text-white">
            of {{ totalPages }}
          </fwb-P>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// You can add any additional styles here
</style>
