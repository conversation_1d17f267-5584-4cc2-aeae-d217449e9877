import FloorPlateCard from './FloorPlateCard.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories

export default {
  title: 'Design System/ALE/FloorPlateCard',
  component: FloorPlateCard,
  tags: ['autodocs'],
  args: {

  },
};

export const Primary = {
  args: {
    title: 'Unit -456',
    numberOfUnits: 100,
    measurementMin: 2500,
    measurementMax: 2500,
    measurementType: 'Sqft',
    bedroomsMin: 3,
    bedroomsMax: 5,
    availableStatus: 'available',
    availableUnits: 8,
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=918-6562&mode=dev",
    },
  },
};
