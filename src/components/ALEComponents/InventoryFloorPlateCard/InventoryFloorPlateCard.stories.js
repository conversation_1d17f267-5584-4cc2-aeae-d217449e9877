import InventoryFloorPlateCard from './InventoryFloorPlateCard.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/InventoryFloorPlateCard',
  component: InventoryFloorPlateCard,
  tags: ['autodocs'],
  args: {

  },
};

export const Primary = {
  args: {
    name: 'A456',
    status: 'available',
    tower: 'A',
    floor: '2',
    type: 'A1',
    price: 5000000,
    currency: 'INR',
    measurement: 2500,
    measurementType: 'Sqft',
    bedrooms: 2,
  },
  parameters: {
    design:
    {
      type: "figma",
      url: "https://www.figma.com/file/3ScwDHZPtk2FRiRLsSU2T2/ALE-V.2?node-id=93%3A8587&mode=dev",
    },
  },
};
