<script setup>
import { ref, defineProps, defineEmits, onMounted, watch} from 'vue';

const isClicked = ref(false);
const clicked = ref(false);
const svgFill = ref('white');
const svgFiller = ref('white');
// Const isDropdownOpen = ref(true);
const isMobDropdownOpen = ref(true);
const showListView = ref(false);
const list=ref();
const  mobList = ref();
const lastElements=ref();
const restElements =ref();
const mobLastElements=ref();
const showMobileListView = ref();
import { useRoute } from 'vue-router';
const route = useRoute();
const isDropdownOpen = ref(route.query.landmarkId?false:true);

const emit = defineEmits(['moveToScene']);

watch(() => route.query.landmarkId, (new_landmarkId) => {
  if (new_landmarkId){
    isDropdownOpen.value =false;
  }
});

const props =defineProps({
  logo: {type: String, default: ""},
  breadCrumbData: {type: Array, default () {
    return [];
  }},
});

function moveToThatScene (id, type) {
  emit('moveToScene', id, type);
}

const handleBg= ()  => {
  isClicked.value =!isClicked.value;
  svgFill.value = isClicked.value ? 'black' : 'white';

  clicked.value =!clicked.value;
  svgFiller.value = clicked.value ? 'black' : 'white';
};

function toggleListView (){

  showListView.value = !showListView.value;
  svgFill.value = 'white';
}
function toggleDropdown () {
  isDropdownOpen.value = !isDropdownOpen.value;
}

function mobToggleDropdown () {
  isMobDropdownOpen.value = !isMobDropdownOpen.value;
}

const copyOfBreadCrumb = props.breadCrumbData;

lastElements.value = copyOfBreadCrumb.slice(-2);
const lastOfArray = Object.values(lastElements.value);

restElements.value = copyOfBreadCrumb.slice(0, copyOfBreadCrumb.length - 2);
const restOfArray = Object.values(restElements.value);

mobLastElements.value = copyOfBreadCrumb.slice(0, copyOfBreadCrumb.length - 1);
const mobLastOfArray = Object.values(mobLastElements.value);
const mobileList = ref();
mobileList.value = mobLastOfArray.reverse();

const rest = ref();
rest.value = restOfArray.reverse();

function outsideClickHandler (event) {
  const div = list.value;
  if (div && !div.contains(event.target)) {
    showListView.value = false;
    isClicked.value =false;
    svgFill.value = 'white';
  }

  const block = mobList.value;
  if (block && !block.contains(event.target)) {
    showMobileListView.value = false;
    clicked.value =false;
    svgFiller.value = 'white';
  }
}

const mobileListView = () => {
  showMobileListView.value = !showMobileListView.value;
  svgFiller.value = 'white';
};

onMounted(() => {
  document.addEventListener('click', outsideClickHandler);
  // Set isDropdownOpen to false after 2 seconds
  setTimeout(() => {
    isMobDropdownOpen.value = false;
  }, 2000);
});

</script>

<!-- breadcrumb without dropdown -->
<!-- <template>
  <div class="w-full items-center justify-center relative   md:flex ">
    <div class="hidebutton-color w-full  flex items-center justify-between  gap-3 bg-breadCrumbBg bg-opacity-30  backdrop-blur-[0.75rem] px-6 p-2 ">
      <div class="w-fit flex-auto">
        <div class="flex flex-row items-center w-fit">
          <ul
            class=" flex flex-row gap-8 hidebutton-color overflow-hidden  justify-start items-center bg-opacity-75   hidescroll"
            :class="isDropdownOpen ? 'w-full' : 'w-0'"
            style="transition: 0.5s"
          >
            <div class="flex">
              <div class="flex items-center justify-center flex-row cursor-pointer">
                <div
                  v-for="(option,index) in breadCrumbData"
                  :key="index"
                  class=" flex items-center justify-center  flex-row gap-3 cursor-pointer bg-opacity-40"
                  :class="breadCrumbData.length === index ? '' : 'gap-3 pr-4'"
                  @click="moveToThatScene(option._id,option.type)"
                >
                  <div
                    class="cursor-pointer text-center text-white text-sm font-normal non-italic whitespace-nowrap hover:underline hover:underline-offset-4"
                    :class="breadCrumbData.length-1 == index ? '!text-breadCrumbLastScene':''"
                  >
                    {{ option.name }}
                  </div>
                  <svg
                    v-show="breadCrumbData.length-1 != index"
                    class="w-5 h-5 relative z-5"
                    viewBox="0 0 20 21"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clip-path="url(#clip0_918_3583)">
                      <path
                        d="M17 8.76795C18.3333 9.53775 18.3333 11.4623 17 12.2321L8 17.4282C6.66667 18.198 5 17.2358 5 15.6962L5 5.30385C5 3.76425 6.66667 2.802 8 3.5718L17 8.76795Z"
                        fill="#D8DBDF"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_918_3583">
                        <rect
                          width="20"
                          height="20"
                          fill="white"
                          transform="translate(0 0.5)"
                        />
                      </clipPath>
                    </defs>
                  </svg>
                </div>
              </div>
            </div>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template> -->

<template class="flex flex-col gap-6">
  <!-- mobile -->
  <div class="flex items-center justify-start absolute top-[1rem] left-[1rem] rounded-full h-10 md:hidden">
    <div class="hidebutton-color rounded-bl-full rounded-tl-full px-4 py-5 pl-[1.5rem] h-10 flex items-center justify-center gap-3 bg-breadCrumbBg bg-opacity-30 backdrop-blur-lg">
      <img
        class="w-16 aspect-video"
        :src="logo"
      >
    </div>

    <div class="flex flex-row items-center ">
      <ul
        class="h-10 flex flex-row gap-8 hidebutton-color overflow-hidden  justify-start items-center bg-breadCrumbBg bg-opacity-75 backdrop-blur-lg hidescroll"
        :class="isMobDropdownOpen? 'w-full' : 'w-0'"
        style="transition: 1s"
      >
        <div class="flex">
          <div class="w-0.5 h-8 bg-gray-500 mr-4 place-self-center" />
          <div class="flex items-center justify-center flex-row cursor-pointer">
            {{ console.log(breadCrumbData.length)
            }}
            <div v-if="breadCrumbData.length <= 2">
              <div
                v-for="(option,index) in breadCrumbData"
                v-show="breadCrumbData[1]"
                :key="index"
                class="flex items-center justify-center  flex-row gap-3 cursor-pointer bg-opacity-40"
                :class="breadCrumbData.length === index ? '' : 'gap-3 pr-4'"
                @click="moveToThatScene(option._id,option.type)"
              >
                <div
                  v-if="option.name"
                  class="cursor-pointer text-center text-white text-base font-medium whitespace-nowrap flex gap-3  items-center justify-center hover:underline hover:underline-offset-4"
                  :class="breadCrumbData.length-1 == index ? 'text-breadCrumbLastScene':''"
                >
                  {{ option.name }}
                </div>
              </div>
            </div>

            <div
              v-if="breadCrumbData.length > 2"
              class="flex items-center justify-center flex-row gap-3 cursor-pointer"
            >
              <div
                ref="mobList"
                class="w-5 h-5 relative  rounded"
                :class="{ 'bg-breadCrumbDropDownSvgBg': clicked }"
                @click="handleBg()"
              >
                <svg
                  class="w-5 h-5"
                  viewBox="0 0 20 21"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  @click="mobileListView()"
                >
                  <path
                    d="M11.5238 11C11.5238 11.2967 11.4344 11.5867 11.267 11.8334C11.0996 12.08 10.8616 12.2723 10.5831 12.3858C10.3047 12.4994 9.99831 12.5291 9.70272 12.4712C9.40713 12.4133 9.13561 12.2704 8.9225 12.0607C8.7094 11.8509 8.56427 11.5836 8.50547 11.2926C8.44667 11.0017 8.47685 10.7001 8.59218 10.426C8.70752 10.1519 8.90283 9.91762 9.15342 9.7528C9.40401 9.58797 9.69862 9.5 10 9.5C10.4041 9.5 10.7917 9.65804 11.0775 9.93934C11.3633 10.2206 11.5238 10.6022 11.5238 11ZM3.52381 9.5C3.22243 9.5 2.92782 9.58797 2.67723 9.7528C2.42664 9.91762 2.23133 10.1519 2.11599 10.426C2.00066 10.7001 1.97048 11.0017 2.02928 11.2926C2.08808 11.5836 2.23321 11.8509 2.44631 12.0607C2.65942 12.2704 2.93094 12.4133 3.22653 12.4712C3.52212 12.5291 3.82851 12.4994 4.10695 12.3858C4.38539 12.2723 4.62337 12.08 4.79081 11.8334C4.95825 11.5867 5.04762 11.2967 5.04762 11C5.04762 10.6022 4.88708 10.2206 4.60131 9.93934C4.31554 9.65804 3.92795 9.5 3.52381 9.5ZM16.4762 9.5C16.1748 9.5 15.8802 9.58797 15.6296 9.7528C15.379 9.91762 15.1837 10.1519 15.0684 10.426C14.953 10.7001 14.9229 11.0017 14.9817 11.2926C15.0405 11.5836 15.1856 11.8509 15.3987 12.0607C15.6118 12.2704 15.8833 12.4133 16.1789 12.4712C16.4745 12.5291 16.7809 12.4994 17.0593 12.3858C17.3378 12.2723 17.5758 12.08 17.7432 11.8334C17.9106 11.5867 18 11.2967 18 11C18 10.6022 17.8395 10.2206 17.5537 9.93934C17.2679 9.65804 16.8803 9.5 16.4762 9.5Z"
                    :fill="svgFiller"
                  />
                </svg>
              </div>

              <svg
                class="w-4 h-4"
                viewBox="0 0 13 15"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 5.76795C13.3333 6.53775 13.3333 8.46225 12 9.23205L3 14.4282C1.66667 15.198 0 14.2358 0 12.6962L0 2.30385C0 0.764249 1.66667 -0.198004 3 0.571796L12 5.76795Z"
                  fill="#EDEEF1"
                />
              </svg>

              <div class="flex flex-row justify-center items-center">
                <div
                  class="flex justify-center items-center gap-2 cursor-pointer text-center text-white text-base font-medium whitespace-nowrap hover:underline hover:underline-offset-4"
                >
                  {{ breadCrumbData[breadCrumbData.length - 1].name }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </ul>

      <button
        ref="dropdownButton"
        :class="isMobDropdownOpen ? '' : 'open'"
        class=" z-10 h-10 px-3 py-3 rounded-tr-full rounded-br-full gap-3 bg-breadCrumbBg bg-opacity-30 backdrop-blur-lg text-white focus:outline-none flex items-center justify-center border-r-none"
        @click="mobToggleDropdown()"
      >
        <svg
          :class="isMobDropdownOpen ? 'arrow-open' : 'arrow-close'"
          width="21"
          height="20"
          viewBox="0 0 21 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.0959 3.25726L6.66045 9.01979C6.59594 9.08839 6.55034 9.16271 6.52368 9.24274C6.49658 9.32278 6.48303 9.40853 6.48303 9.5C6.48303 9.59147 6.49658 9.67722 6.52368 9.75726C6.55034
                                9.83729 6.59594 9.91161 6.66045 9.98021L12.0959 15.7599C12.2465 15.92 12.4346 16 12.6605 16C12.8863 16 13.0798 15.9142 13.2411 15.7427C13.4024 15.5712 13.483 15.3712 13.483 15.1425C13.483
                                  14.9138 13.4024 14.7137 13.2411 14.5422L8.49916 9.5L13.2411 4.45778C13.3916 4.29771 13.4669 4.1006 13.4669 3.86644C13.4669 3.63182 13.3863 3.42876 13.225 3.25726C13.0637 3.08575 12.8755
                                  3 12.6605 3C12.4454 3 12.2572 3.08575 12.0959 3.25726Z"
            fill="none"
            stroke="white"
            stroke-width="45px"
          />
        </svg>
      </button>
    </div>

    <div
      v-if="showMobileListView && isMobDropdownOpen"
      class="w-[14rem] mt-2 mr-6 bg-breadCrumbDropDownBg bg-opacity-40 rounded-lg border border-breadCrumbBroder backdrop-blur-2xl  flex items-center justify-start flex-col  cursor-pointer absolute top-[3rem] left-[7.5rem] "
      style="align-items: baseline;"
    >
      <div
        v-for="(element, index) in mobileList"
        :key="index"
        class="breadcrumb-dropdown-list flex justify-start items-center w-full hover:bg-white hover:bg-opacity-[0.1] cursor-pointer"
        @click="moveToThatScene(element._id,element.type)"
      >
        <div
          v-if="element.name"
          class="text-white  text-base font-medium p-2 cursor-pointer overflow-hidden "
        >
          {{ element.name }}
        </div>
      </div>
    </div>
  </div>

  <!-- desktop -->
  <div class="hidden items-center justify-start absolute top-[1rem] left-[1rem] rounded-full h-10 md:flex">
    <div class="hidebutton-color rounded-bl-full rounded-tl-full pl-[1.5rem] py-5 px-4 h-10 flex items-center justify-center gap-3 bg-breadCrumbBg bg-opacity-30 backdrop-blur-lg bg-app-gradient">
      <img
        class="w-16 aspect-video"
        :src="logo"
      >
    </div>
    <div class="flex flex-row items-center">
      <ul
        class="h-10 flex flex-row gap-8 hidebutton-color overflow-hidden justify-start items-center bg-breadCrumbBg bg-opacity-75 backdrop-blur-lg hidescroll bg-app-gradient"
        :class="isDropdownOpen ? 'w-full' : 'w-0'"
        style="transition: 0.5s"
      >
        <div class="flex">
          <div class="w-0.5 h-8 bg-gray-500 mr-4 place-self-center" />
          <div class="flex items-center justify-center flex-row cursor-pointer">
            <!-- if the breadcrumb length is more then 5 -->
            <div
              v-if="breadCrumbData.length > 5"
              class="flex items-center justify-center flex-row gap-3 cursor-pointer"
            >
              <div
                ref="list"
                class="w-5 h-5 relative  rounded"
                :class="{ 'bg-breadCrumbDropDownSvgBg': isClicked }"
                @click="handleBg()"
              >
                <svg
                  class="w-5 h-5"
                  viewBox="0 0 20 21"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  @click="toggleListView()"
                >
                  <path
                    d="M11.5238 11C11.5238 11.2967 11.4344 11.5867 11.267 11.8334C11.0996 12.08 10.8616 12.2723 10.5831 12.3858C10.3047 12.4994 9.99831 12.5291 9.70272 12.4712C9.40713 12.4133 9.13561 12.2704 8.9225 12.0607C8.7094 11.8509 8.56427 11.5836 8.50547 11.2926C8.44667 11.0017 8.47685 10.7001 8.59218 10.426C8.70752 10.1519 8.90283 9.91762 9.15342 9.7528C9.40401 9.58797 9.69862 9.5 10 9.5C10.4041 9.5 10.7917 9.65804 11.0775 9.93934C11.3633 10.2206 11.5238 10.6022 11.5238 11ZM3.52381 9.5C3.22243 9.5 2.92782 9.58797 2.67723 9.7528C2.42664 9.91762 2.23133 10.1519 2.11599 10.426C2.00066 10.7001 1.97048 11.0017 2.02928 11.2926C2.08808 11.5836 2.23321 11.8509 2.44631 12.0607C2.65942 12.2704 2.93094 12.4133 3.22653 12.4712C3.52212 12.5291 3.82851 12.4994 4.10695 12.3858C4.38539 12.2723 4.62337 12.08 4.79081 11.8334C4.95825 11.5867 5.04762 11.2967 5.04762 11C5.04762 10.6022 4.88708 10.2206 4.60131 9.93934C4.31554 9.65804 3.92795 9.5 3.52381 9.5ZM16.4762 9.5C16.1748 9.5 15.8802 9.58797 15.6296 9.7528C15.379 9.91762 15.1837 10.1519 15.0684 10.426C14.953 10.7001 14.9229 11.0017 14.9817 11.2926C15.0405 11.5836 15.1856 11.8509 15.3987 12.0607C15.6118 12.2704 15.8833 12.4133 16.1789 12.4712C16.4745 12.5291 16.7809 12.4994 17.0593 12.3858C17.3378 12.2723 17.5758 12.08 17.7432 11.8334C17.9106 11.5867 18 11.2967 18 11C18 10.6022 17.8395 10.2206 17.5537 9.93934C17.2679 9.65804 16.8803 9.5 16.4762 9.5Z"
                    :fill="svgFill"
                  />
                </svg>
              </div>

              <svg
                class="w-4 h-4"
                viewBox="0 0 13 15"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 5.76795C13.3333 6.53775 13.3333 8.46225 12 9.23205L3 14.4282C1.66667 15.198 0 14.2358 0 12.6962L0 2.30385C0 0.764249 1.66667 -0.198004 3 0.571796L12 5.76795Z"
                  fill="#EDEEF1"
                />
              </svg>

              <div class="flex flex-row justify-center items-center">
                <div
                  v-for="(option,index) in lastOfArray"
                  :key="index"
                  class="flex items-center justify-center  flex-row gap-3 cursor-pointer bg-opacity-40"
                  :class="lastOfArray.length === index ? '' : 'gap-3 pr-4'"
                  @click="moveToThatScene(option._id,option.type)"
                >
                  <div
                    class="cursor-pointer text-center text-white text-base font-medium whitespace-nowrap hover:underline hover:underline-offset-4"
                    :class="lastOfArray.length-1 == index ? 'text-breadCrumbLastScene':''"
                  >
                    {{ option.name }}
                  </div>
                  <svg
                    v-show="lastOfArray.length-1 != index"
                    class="w-4 h-4"
                    viewBox="0 0 13 15"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 5.76795C13.3333 6.53775 13.3333 8.46225 12 9.23205L3 14.4282C1.66667 15.198 0 14.2358 0 12.6962L0 2.30385C0 0.764249 1.66667 -0.198004 3 0.571796L12 5.76795Z"
                      fill="#EDEEF1"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <!-- if the breadcrumb length is less then 5 -->
            <div
              v-else-if="breadCrumbData.length <= 5"
              class="flex flex-row"
            >
              <div
                v-for="(option,index) in breadCrumbData"
                :key="index"
                class="flex items-center justify-center  flex-row gap-3 cursor-pointer bg-opacity-40"
                :class="breadCrumbData.length === index ? '' : 'gap-3 pr-4'"
                @click="moveToThatScene(option._id,option.type)"
              >
                <div
                  v-if="option.name"
                  class="cursor-pointer text-center text-white text-base font-medium whitespace-nowrap flex gap-3  items-center justify-center hover:underline hover:underline-offset-4"
                  :class="breadCrumbData.length-1 == index ? 'text-breadCrumbLastScene':''"
                >
                  {{ option.name }}
                  <svg
                    v-show="breadCrumbData.length-1 != index"
                    class="w-4 h-4"
                    viewBox="0 0 13 15"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 5.76795C13.3333 6.53775 13.3333 8.46225 12 9.23205L3 14.4282C1.66667 15.198 0 14.2358 0 12.6962L0 2.30385C0 0.764249 1.66667 -0.198004 3 0.571796L12 5.76795Z"
                      fill="#EDEEF1"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ul>

      <button
        ref="dropdownButton"
        :class="isDropdownOpen ? '' : 'open'"
        class="z-10 h-10 px-3 py-3 rounded-tr-full rounded-br-full gap-3   bg-breadCrumbBg  bg-opacity-30 backdrop-blur-lg text-white focus:outline-none flex items-center justify-center border-r-none"
        @click="toggleDropdown()"
      >
        <svg
          :class="isDropdownOpen ? 'arrow-open' : 'arrow-close'"
          width="21"
          height="20"
          viewBox="0 0 21 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.0959 3.25726L6.66045 9.01979C6.59594 9.08839 6.55034 9.16271 6.52368 9.24274C6.49658 9.32278 6.48303 9.40853 6.48303 9.5C6.48303 9.59147 6.49658 9.67722 6.52368 9.75726C6.55034
                                9.83729 6.59594 9.91161 6.66045 9.98021L12.0959 15.7599C12.2465 15.92 12.4346 16 12.6605 16C12.8863 16 13.0798 15.9142 13.2411 15.7427C13.4024 15.5712 13.483 15.3712 13.483 15.1425C13.483
                                  14.9138 13.4024 14.7137 13.2411 14.5422L8.49916 9.5L13.2411 4.45778C13.3916 4.29771 13.4669 4.1006 13.4669 3.86644C13.4669 3.63182 13.3863 3.42876 13.225 3.25726C13.0637 3.08575 12.8755
                                  3 12.6605 3C12.4454 3 12.2572 3.08575 12.0959 3.25726Z"
            fill="none"
            stroke="white"
            stroke-width="45px"
          />
        </svg>
      </button>
    </div>
  </div>

  <!-- the list of breadcrumb while minimized -->
  <div
    v-if="showListView && breadCrumbData.length>5"
    class="w-64 mt-2 mr-6 bg-breadCrumbDropDownBg bg-opacity-40 rounded-lg border border-breadCrumbBroder backdrop-blur-2xl  flex items-center justify-start flex-col gap-2  cursor-pointer absolute top-[4rem] left-[13rem] "
    style="align-items: baseline;"
  >
    <div
      v-for="(element, index) in rest"
      :key="index"
      class="breadcrumb-dropdown-list flex justify-start items-center w-full hover:bg-white hover:bg-opacity-[0.1] cursor-pointer"
      @click="moveToThatScene(element._id,element.type)"
    >
      <div class="text-white  text-base font-medium p-2 cursor-pointer overflow-hidden ">
        {{ element.name }}
      </div>
    </div>
  </div>
</template>

<style scoped>

.drawer-option-active:hover .drawer-optionIcon::v-deep>svg path {
  stroke: white;
}

@keyframes slideIn {

  0% {
    opacity: 0;
    transform: translateX(-10%);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOut {
  0% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
    transform: translateX(-17%);
  }
}

.arrow-open {
  transform: rotate(0deg);
  transition: 0.5s
}

.arrow-close {
  transform: rotate(180deg);
  transition: 0.5s
}
</style>
