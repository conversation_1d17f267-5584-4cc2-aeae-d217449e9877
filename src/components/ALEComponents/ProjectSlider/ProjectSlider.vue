<script setup>

</script>

<template>
  <div class="w-full flex justify-center">
    <div className="h-12 justify-start items-center gap-5 inline-flex">
      <div className="w-10 h-10 bg-[#1f2a37] rounded-[999px] justify-center items-center flex cursor-pointer">
        <div className="w-5 h-5 relative">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16 5C16 5 9.00001 10.1554 9 12C8.99999 13.8447 16 19 16 19"
              stroke="white"
              stroke-width="4"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>
      <div className="justify-start items-start gap-4 flex">
        <div className="w-3 h-3 bg-white rounded-xl" />
        <div className="w-3 h-3 bg-gray-400 rounded-xl" />
        <div className="w-3 h-3 bg-gray-400 rounded-xl" />
        <div className="w-3 h-3 bg-gray-400 rounded-xl" />
        <div className="w-3 h-3 bg-gray-400 rounded-xl" />
      </div>
      <div
        className="w-10 h-10 bg-[#1f2a37] rounded-[999px] justify-center items-center flex cursor-pointer"
        @click="handleNext"
      >
        <div className="w-5 h-5 relative">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8 5C8 5 15 10.1554 15 12C15 13.8447 8 19 8 19"
              stroke="white"
              stroke-width="4"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<style  scoped>

</style>
