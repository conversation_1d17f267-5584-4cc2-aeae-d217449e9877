<script setup>
import { ref, defineProps, watch } from "vue";
import { creationToolStore } from '../../../store/index';
const props = defineProps({
  logo: { type: String, default: "" },
  progress: { type: Number, default: 0 },
});
const progressWidth = ref(0);
const Store = creationToolStore();
// Function startProgress() {
//   SetInterval(() => {
//     ProgressWidth.value += 1;
//     If (progressWidth.value >= 100) {
//       ProgressWidth.value = 0;
//     }
//   }, 10);
// }
// StartProgress();
watch(
  () => props.progress,
  (progress, oldProgress) => {
    if (progress === 0 && oldProgress > 0){
      const element = document.getElementById("progressBar");
      element.classList.remove("progress-bar");
    }
    if (progress > 0 && oldProgress >= 0){
      const element = document.getElementById("progressBar");
      element.classList.add("progress-bar");
    }
    progressWidth.value = progress;
  },
);
</script>
<template>
  <div
    class="absolute top-0 left-0 w-full h-full bg-black opacity-50"
    :class="!Store.currentSceneVideo && !Store.currentSceneVideoThumb?'z-10':'z-8'"
  >
    <div
      class="fixed top-0 left-0 flex items-center justify-center w-full"
    >
      <div
        class="w-full h-1 bg-white bg-opacity-30 justify-start items-center inline-flex"
      >
        <div
          id="progressBar"
          :style="{ width: progressWidth + '%' }"
          class="h-1 relative bg-white progress-bar"
        />
      </div>
    </div>
  </div>
</template>
<style>
.progress-bar{
  transition: width .6s ease;
}
</style>
