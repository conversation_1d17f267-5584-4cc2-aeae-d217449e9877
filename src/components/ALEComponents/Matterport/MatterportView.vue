<template>
  <div class="h-full">
    <iframe
      id="showcase_frame"
      class="w-full h-full"
      :src="src"
      frameborder="0"
      allowfullscreen
      allow="xr-spatial-tracking"
    />
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';

const properties = defineProps({
  spaceId: {type: String, default: ""},
});
const src = ref("");

onMounted(() => {
  // const src = `https://showcase.propvr.tech/?m=${properties.spaceId}&play=1`;
  if (properties.spaceId){
    src.value = `https://showcase.propvr.tech/?m=${properties.spaceId}&play=1`;
    document.querySelector('iframe').src = src.value;
  }
});

</script>
