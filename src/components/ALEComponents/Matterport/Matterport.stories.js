import MatterportView from './MatterportView.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/MatterportView',
  component: MatterportView,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    modelSID: "zJ9Mimkmanb",
  },
  parameters:
  {
    design:
    {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=629-13416&mode=design&t=MJoHYvcCnJNPKlHr-4",
    },
  },
};
