<script setup>
import { ref, defineProps, watch } from "vue";
import { getCookie, thousandSeparator } from "../../../helpers/helper.js";
import StatusPill from "../../../components/ALEComponents/StatusPill/StatusPill.vue";
import { creationToolStore } from "../../../store";

import { FwbButton } from "flowbite-vue";
import TranslationComp from "../TranslationComp/TranslationComp.vue";
import CurrencyComp from "../CurrencyComp/CurrencyComp.vue";

/* Props and Emits */
const props = defineProps({
  property: {
    type: Object,
    default () {
      return {};
    },
  },
});

const Store = creationToolStore(); // Store

const emit = defineEmits(["handleClose", "handleClick"]);
const units = ref(Object.keys(props.property)); // Property keys
const value = ref(Object.values(props.property)); // Property values
const selectedCurrency = ref(Store.currencyData.currency || getCookie('selectedCurrency'));
const enumKeys = ["measurement", "price", "status", "is_furnished"]; // Enum keys
// Ui lables and mapKeys
const keys = [
  {
    label: "unit",
    mapKey: "name",
  },
  {
    label: "status",
    mapKey: "status",
  },
  {
    label: "Price",
    mapKey: "price",
  },
  {
    label: "Tower ",
    mapKey: "Tower Name",
  },
  {
    label: "Floor",
    mapKey: "floor",
  },
  {
    label: "Unit Type",
    mapKey: "type",
  },
  {
    label: "Area",
    mapKey: "measurement",
  },
  {
    label: "Bedroom",
    mapKey: "bedrooms",
  },
  {
    label: "Bathroom",
    mapKey: "bathroom",
  },
  // {
  //   label: "facing",
  //   mapKey: "facing",
  // },
  // {
  //   Label: "Furnished",
  //   MapKey: "is_furnished",
  // },
  // {
  //   Label: "Measurement Type",
  //   MapKey: "measurement_type",
  // },
];

/* Methods */
watch(() => Store.currencyData, (newCurrency) => {
  selectedCurrency.value = newCurrency.currency;
},
{ deep: true });

const deleteUnit = (unitname) => {
  delete Store.favoritesData[unitname]; // Delete from store.
  units.value = units.value.filter((item) => item !== unitname); // Remove unit from units array.
  value.value = value.value.filter((item) => item.unit_id !== unitname); // Remove unit from value array
};

function getVal (propertyKey, currencyType, measurementType, val) {
  if (propertyKey.toLowerCase() === enumKeys[0].toLowerCase()) {
    return `${thousandSeparator(val)} ${measurementType}`; // Measurement
  }

  if (propertyKey.toLowerCase() === enumKeys[1].toLowerCase()) {
    return selectedCurrency.value ? selectedCurrency.value : currencyType; // Price
  }

  if (propertyKey.toLowerCase() === enumKeys[3].toLowerCase()) {
    return val ? "Furnished" : "Unfurnished";
  }

  return val;
}
const handleSelectionIdEmit = (unit_id) => {
  emit("handleClick", unit_id);
};

const backSvg = ref(`<svg xmlns="http://www.w3.org/2000/svg" width="14" height="15" viewBox="0 0 14 15" fill="none">
<path d="M1.75 7.50008H12.25Z" fill="white"/>
<path d="M5.83333 11.5834L1.75 7.50008M1.75 7.50008L5.83333 3.41675M1.75 7.50008H12.25" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`);

const goBack = () => {
  emit("handleClose");
  // Emit('handleClose');
};

// OnMounted(async () => {
// Console.log("🚀 ~ prop̥erty:", prop̥erty)
//   Store.getListOfUnitplan(
//     Route.params.projectId,
//     Route.params.organizationId
//   ).then((data) => {
//     Const dataArray = Object.values(data);
//     // console.log("data", dataArray);
//     DataArray.map((items) => {
//       Value.value.map((obj) => {
//         If (obj._id === items._id) {
//           If (items.floor_unitplans && items.unit_type == "villa") {
//             // console.log("11111", obj); ///////////////////////////////////////////////////////
//             FloorPLans.value = items.floor_unitplans;
//           }
//         }
//       });
//     });
//   });

//   Console.log("flooe planss", floorPLans);

//   Store.getListOfUnitplan(
//     Route.params.projectId,
//     Route.params.organizationId
//   ).then((data) => {
//     Const dataArray = Object.values(data);
//     FloorPLans.value.map((keys) => {
//       DataArray.map((obj) => {
//         // console.log("second func", obj);
//         // console.log("keyss", keys);
//         If (obj._id == keys) {
//           Console.log("pushing data", obj);
//           Object.assign(props.property, { [obj._id]: obj });

//           Console.log("finallyyyyy", properties.value);
//         } else {
//           Console.log("wrong obj");
//         }
//       });
//     });
//   });

//   Console.log("properties Object", await properties.value);
// });

/* Hooks */
</script>

<template>
  <div
    :class="[
      (Store.isLandscape || Store.isMobile)
        ? 'fixed left-0 right-0 bottom-0 z-[4] overflow-hidden'
        : 'absolute top-0 left-0 w-full h-full z-[4] overflow-hidden',
      Store.isMobile ? 'w-[100vw]' : 'w-full'
    ]"
    :style="(Store.isLandscape || Store.isMobile)
      ? { top: 'var(--navbar-height, 56px)', height: 'calc(100vh - var(--navbar-height, 56px))' }
      : {}"
  >
    <div
      class="flex  justify-center items-center w-full h-[100vh] bg-opacity-50 backdrop-blur-[30px] overflow-hidden"
      :class="Store.isMobile?'h-full':''"
    >
      <!-- <DamacHeader class="flex-shrink-0 w-full" /> -->

      <!-- Favorite -->
      <div
        v-if="units.length > 0 && keys.length > 0"
        class="flex flex-col gap-2  w-full h-full overflow-hidden items-center"
        :class="!Store.isMobile ? 'justify-start':'justify-center'"
      >
        <!-- Close Button -->
        <!-- <span class="absolute text-end w-full top-[15%] right-[5%]">
            <span
              class="inline-block cursor-pointer"
              @click="$emit('handleClose')"
            >
              <svg
                class="w-6 h-6"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M20.5 20L3.5 3"
                  stroke="white"
                  stroke-width="1.98333"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M20.5 3L3.5 20"
                  stroke="white"
                  stroke-width="1.98333"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </span>
          </span> -->

        <!-- Table View -->

        <div
          class="relative table-div  sm:rounded-lg z-50 "
          :class="
            Store.isMobile
              ? 'overflow-x-auto w-[100vw] h-[90%] top-[25px] '
              : Store.isLandscape ? 'w-[95vw] overflow-auto !-right-5 top-[12%]' : 'overflow-x-auto w-[90vw] top-[12%] h-[85vh] '
          "
        >
          <table
            class="text-sm text-left bg-secondary text-secondaryText min-w-full rounded-lg"
            :class="Store.isMobile ? 'flex h-fit w-max-full  ' : ''"
          >
            <thead
              class="text-xs uppercase bg-tertiary50opacity "
              :class="!Store.isMobile ? 'sticky':''"
            >
              <tr :class="Store.isMobile ? 'flex flex-col  w-[120px]' : ''">
                <th
                  v-for="propertyKey in keys"
                  :key="propertyKey"
                  scope="col"
                  class="px-2 py-2 w-[120px] text-xs font-medium text-secondaryText whitespace-nowrap"
                  :class="
                    Store.isMobile
                      ? 'h-14 flex items-center justify-center'
                      : ''
                  "
                >
                  <TranslationComp
                    :text="propertyKey.mapKey.toLowerCase() ===enumKeys[0].toLowerCase()
                      ? `${propertyKey.label} (${value[0].measurement_type})`
                      : propertyKey.label"
                  />
                </th>
                <th
                  scope="col"
                  class="px-2 py-2  w-[120px]"
                  :class="
                    Store.isMobile
                      ? 'h-14 flex items-center justify-center'
                      : ''
                  "
                />
                <th
                  scope="col"
                  class="px-2 py-2  w-[120px]"
                  :class="
                    Store.isMobile
                      ? 'h-14 flex items-center justify-center'
                      : ''
                  "
                />
                <th
                  v-if="!Store.isMobile"
                  scope="col"
                  class="px-2 py-2  w-[120px]"
                  :class="
                    Store.isMobile
                      ? 'h-14 flex items-center justify-center'
                      : ''
                  "
                />
              </tr>
            </thead>
            <tbody :class="Store.isMobile ? 'h-full flex flex-row w-[70vw] overflow-x-auto text-base font-medium  text-secondaryText' : 'text-sm font-medium text-left text-secondaryText whitespace-nowrap overflow-y-auto rounded-b-lg'">
              <tr
                v-for="items in props.property"
                :key="items"
                :class="Store.isMobile ? 'h-full flex flex-col w-[150px]  ' : ''"
              >
                <td
                  v-for="propertyKey in keys"
                  :key="propertyKey"
                  scope="col"
                  class="px-2 py-2 data-fields whitespace-nowrap"
                  :class="Store.isMobile ? 'h-14  text-center  ' : ''"
                >
                  <span
                    v-show="Store.isMobile"
                    v-if="propertyKey.label == 'unit'"
                    class="svg_property rounded-2xl cursor-pointer text-red-500 gap-1 absolute top-1 right-1 flex w-[32px] h-[32px] p-[11.2px] justify-center items-center flex-shrink-0 bg-secondary  backdrop-brightness-50"
                    @click="deleteUnit(items.unit_id)"
                  >
                    <svg
                      class="!w-3 !h-3 inline"
                      viewBox="0 0 12 12"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g clip-path="url(#clip0_320_6498)">
                        <path
                          d="M10.8889 2.52632H8.44444V1.26316C8.44444 0.928148 8.31567 0.606858 8.08646 0.36997C7.85725 0.133082 7.54638 0 7.22222 0H4.77778C4.45362 0 4.14275 0.133082 3.91354 0.36997C3.68433 0.606858 3.55556 0.928148 3.55556 1.26316V2.52632H1.11111C0.949034 2.52632 0.793596 2.59286 0.67899 2.7113C0.564385 2.82975 0.5 2.99039 0.5 3.15789C0.5 3.3254 0.564385 3.48604 0.67899 3.60449C0.793596 3.72293 0.949034 3.78947 1.11111 3.78947H1.72222V10.7368C1.72222 11.0719 1.85099 11.3931 2.0802 11.63C2.30941 11.8669 2.62029 12 2.94444 12H9.05556C9.37971 12 9.69059 11.8669 9.9198 11.63C10.149 11.3931 10.2778 11.0719 10.2778 10.7368V3.78947H10.8889C11.051 3.78947 11.2064 3.72293 11.321 3.60449C11.4356 3.48604 11.5 3.3254 11.5 3.15789C11.5 2.99039 11.4356 2.82975 11.321 2.7113C11.2064 2.59286 11.051 2.52632 10.8889 2.52632ZM4.77778 1.26316H7.22222V2.52632H4.77778V1.26316ZM5.38889 9.47368C5.38889 9.64119 5.3245 9.80183 5.2099 9.92028C5.09529 10.0387 4.93985 10.1053 4.77778 10.1053C4.6157 10.1053 4.46026 10.0387 4.34566 9.92028C4.23105 9.80183 4.16667 9.64119 4.16667 9.47368V5.05263C4.16667 4.88513 4.23105 4.72448 4.34566 4.60604C4.46026 4.48759 4.6157 4.42105 4.77778 4.42105C4.93985 4.42105 5.09529 4.48759 5.2099 4.60604C5.3245 4.72448 5.38889 4.88513 5.38889 5.05263V9.47368ZM7.83333 9.47368C7.83333 9.64119 7.76895 9.80183 7.65434 9.92028C7.53974 10.0387 7.3843 10.1053 7.22222 10.1053C7.06015 10.1053 6.90471 10.0387 6.7901 9.92028C6.6755 9.80183 6.61111 9.64119 6.61111 9.47368V5.05263C6.61111 4.88513 6.6755 4.72448 6.7901 4.60604C6.90471 4.48759 7.06015 4.42105 7.22222 4.42105C7.3843 4.42105 7.53974 4.48759 7.65434 4.60604C7.76895 4.72448 7.83333 4.88513 7.83333 5.05263V9.47368Z"
                          fill="#F05252"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_320_6498">
                          <rect
                            width="12"
                            height="12"
                            fill="white"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                  </span>
                  <StatusPill
                    v-if="propertyKey.mapKey === enumKeys[2]"
                    :availability-status="items[propertyKey.mapKey]"
                  />

                  <span
                    v-else-if="items[propertyKey.mapKey]"
                    class="capitalize"
                  >
                    <TranslationComp
                      :text=" getVal(
                        propertyKey.mapKey,
                        items.currency,
                        items.measurement_type,
                        items[propertyKey.mapKey]
                      )"
                    />
                    <CurrencyComp
                      v-if="propertyKey.mapKey === enumKeys[1]"
                      :price="items[propertyKey.mapKey]"
                      :unitCurrencyType="items.currency"
                      class="ml-1"
                    />
                  </span>
                  <span v-else> - </span>
                </td>

                <td :class="Store.isMobile ? 'h-14  text-center  ' : ''">
                  <FwbButton
                    size="xs"
                    class="bg-tertiary50opacity hover:bg-primary hover:text-secondary text-secondaryText py-2 px-4 text-xs/font-medium locate-btn"
                    color="dark"
                    pill
                    @click="handleSelectionIdEmit(items.unit_id)"
                  >
                    <TranslationComp
                      text="Locate"
                    />
                  </FwbButton>
                </td>

                <td
                  v-if="!Store.isMobile"
                  :class="Store.isMobile ? 'h-14  text-center  ' : ''"
                >
                  <span

                    class="svg_property rounded-full cursor-pointer text-red-500 flex gap-1 items-center w-full justify-center"
                    @click="deleteUnit(items.unit_id)"
                  >
                    <svg
                      class="!w-3 !h-3 inline"
                      viewBox="0 0 12 12"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g clip-path="url(#clip0_320_6498)">
                        <path
                          d="M10.8889 2.52632H8.44444V1.26316C8.44444 0.928148 8.31567 0.606858 8.08646 0.36997C7.85725 0.133082 7.54638 0 7.22222 0H4.77778C4.45362 0 4.14275 0.133082 3.91354 0.36997C3.68433 0.606858 3.55556 0.928148 3.55556 1.26316V2.52632H1.11111C0.949034 2.52632 0.793596 2.59286 0.67899 2.7113C0.564385 2.82975 0.5 2.99039 0.5 3.15789C0.5 3.3254 0.564385 3.48604 0.67899 3.60449C0.793596 3.72293 0.949034 3.78947 1.11111 3.78947H1.72222V10.7368C1.72222 11.0719 1.85099 11.3931 2.0802 11.63C2.30941 11.8669 2.62029 12 2.94444 12H9.05556C9.37971 12 9.69059 11.8669 9.9198 11.63C10.149 11.3931 10.2778 11.0719 10.2778 10.7368V3.78947H10.8889C11.051 3.78947 11.2064 3.72293 11.321 3.60449C11.4356 3.48604 11.5 3.3254 11.5 3.15789C11.5 2.99039 11.4356 2.82975 11.321 2.7113C11.2064 2.59286 11.051 2.52632 10.8889 2.52632ZM4.77778 1.26316H7.22222V2.52632H4.77778V1.26316ZM5.38889 9.47368C5.38889 9.64119 5.3245 9.80183 5.2099 9.92028C5.09529 10.0387 4.93985 10.1053 4.77778 10.1053C4.6157 10.1053 4.46026 10.0387 4.34566 9.92028C4.23105 9.80183 4.16667 9.64119 4.16667 9.47368V5.05263C4.16667 4.88513 4.23105 4.72448 4.34566 4.60604C4.46026 4.48759 4.6157 4.42105 4.77778 4.42105C4.93985 4.42105 5.09529 4.48759 5.2099 4.60604C5.3245 4.72448 5.38889 4.88513 5.38889 5.05263V9.47368ZM7.83333 9.47368C7.83333 9.64119 7.76895 9.80183 7.65434 9.92028C7.53974 10.0387 7.3843 10.1053 7.22222 10.1053C7.06015 10.1053 6.90471 10.0387 6.7901 9.92028C6.6755 9.80183 6.61111 9.64119 6.61111 9.47368V5.05263C6.61111 4.88513 6.6755 4.72448 6.7901 4.60604C6.90471 4.48759 7.06015 4.42105 7.22222 4.42105C7.3843 4.42105 7.53974 4.48759 7.65434 4.60604C7.76895 4.72448 7.83333 4.88513 7.83333 5.05263V9.47368Z"
                          fill="#F05252"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_320_6498">
                          <rect
                            width="12"
                            height="12"
                            fill="white"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                    <span>
                      <TranslationComp
                        text="Remove"
                      />
                    </span>
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- No Favorite found -->

      <div
        v-else
        class="p-5 bg-secondary rounded-lg  flex-col justify-start items-center inline-flex gap-4"
      >
        <svg
          class="w-[15px] h-[15px]"
          viewBox="0 0 15 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g id="heart">
            <path
              id="Vector"
              d="M2.44982 4.53722C2.58174 4.21874 2.7751 3.92936 3.01886 3.6856C3.26261 3.44185 3.55199 3.24849 3.87047 3.11657C4.18895 2.98465 4.5303 2.91675 4.87502 2.91675C5.21975 2.91675 5.56109 2.98465 5.87957 3.11657C6.19806 3.24849 6.48744 3.44185 6.73119 3.6856L7.50002 4.45444L8.26886 3.6856C8.76114 3.19332 9.42883 2.91675 10.125 2.91675C10.8212 2.91675 11.4889 3.19332 11.9812 3.6856C12.4735 4.17789 12.75 4.84557 12.75 5.54177C12.75 6.23797 12.4735 6.90565 11.9812 7.39794L7.50002 11.8791L3.01886 7.39794C2.7751 7.15418 2.58174 6.8648 2.44982 6.54632C2.3179 6.22784 2.25 5.88649 2.25 5.54177C2.25 5.19705 2.3179 4.8557 2.44982 4.53722Z"
              fill="#9CA3AF"
              stroke="#9CA3AF"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </g>
        </svg>

        <p class="text-center text-secondaryText font-medium text-sm">
          <TranslationComp
            text="No Favorites."
          />
          <br>
          <TranslationComp
            text="Add units to Favorites for compare."
          />
        </p>

        <div
          className="h-[41px] px-5 py-2.5 bg-primary rounded-lg justify-center items-center gap-2 inline-flex cursor-pointer"
          @click="goBack()"
        >
          <div
            className="w-3.5 h-3.5 relative backsvgfiller"
            v-html="backSvg"
          />
          <div className="text-secondary text-sm font-medium  leading-[21px]">
            <TranslationComp
              text="Back"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* WrapperContainer is just a testing class */
/*  .wrapperContainer {
  background-image: url("https://images.unsplash.com/photo-1682687982167-d7fb3ed8541d?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D");
  background-position: center;
  background-repeat: no-repeat;
}   */

/* ----- Table ----- */

::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

.table-head{
    background-color: rgba(var(--secondary), 0.5);
}

table {
  table-layout: fixed;
  border-collapse: separate; /* Don't collapse */
  border-spacing: 0;
}

th {
  padding: 10px;
  text-align: center;
  border-left: none;
  border-bottom: none;
  border-top: none;
}

td {
  padding: 1.2rem;
  text-align: center;
  position: relative;
  border-left: none;
  border-bottom: none;
}

th:first-of-type {
  padding: 2.5rem;
  @media screen {
    padding: 0;
  }
}

td:first-of-type {
  text-align: left;
  @media screen {
    text-align: center;
  }
}

/* Property Labels or keys */
.property-name {
  position: sticky;
  left: 0;
  z-index: 2;
}
.table-div::-webkit-scrollbar{
   display: none;
}
/* Headers */
.property-header {
  top: 0;
  left: 0;
  z-index: 3;
  background: var(--gray-700, #374151);
  color: #ffffff;
}
.property-table {
  /* border-radius: var(--rounded-lg, 8px); */
  /* border: 1px solid var(--gray-700, #374151); */
  background: var(--gray-800, #1f2a37);

  /* shadow-sm */
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.08);
}
.book_unit_property {
  white-space: nowrap;
}
.svg_property {
  white-space: nowrap;
}
.unit-header {
  position: sticky;
  top: 0;
  z-index: 1;
}

/* Actions Buttons */
.actionButtonsSticky {
  position: sticky;
  bottom: 0;
  z-index: 1;
}

.backButtonSticky {
  position: sticky;
  left: 0;
  bottom: 0;
  z-index: 3;
}

.lightTheme {
  background: linear-gradient(
    90deg,
    rgba(228, 213, 180, 1) 0%,
    rgba(224, 186, 103, 1) 100%
  );
}
.lightTheme:hover {
  background: #ffca55;
}
.no-fav{
  border-radius: var(--rounded-md, 6px);

background: var(--gray-800, #1F2A37);
/* shadow */
box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10);
}
.locate-btn:focus{
  box-shadow: none;
}
.backsvgfiller::v-deep> svg >path{
  stroke:var(--secondary);
}
</style>
