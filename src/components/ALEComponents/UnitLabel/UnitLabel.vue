<template>
  <!-- :class="computedThemeClass" -->
  <span
    class="font-medium rounded text-[10px] p-[3px] text-secondaryText bg-secondary"
  >
    {{ label }}
  </span>
</template>

<script setup>
import { ref } from 'vue';
import { creationToolStore } from '../../../store';
import { useRoute } from 'vue-router';

const Store = creationToolStore();
const route = useRoute();
const projectTheme = ref(null);

if (Store.projectCardData[route.params.projectId].projectSettings.theme) {
  projectTheme.value = Store.projectCardData[route.params.projectId].projectSettings.theme.theme;
} else {
  projectTheme.value = Store.organizationDetails.theme;
}

// Const computedThemeClass = computed(() => {
//   Switch (projectTheme.value) {
//     Case 'light':
//       Return 'theme-light';
//     Case 'dark':
//       Return 'theme-dark';
//     Case 'custom':
//       Return 'theme-custom';
//     Default:
//       Return 'theme-dark';
//   }
// });

defineProps({
  label: { type: String, default: "" },
});
</script>

<style>
/* .theme-light {
  background-color: #fff;
  color:#000;
  font-size: 10px;
  padding: 3px;
}

.theme-dark {
  background-color: #1f2a37;
  color:#fff;
  font-size: 10px;
  padding: 3px;
}
.theme-custom {
  background-color: #fff;
  color:#1565C0;
} */
</style>
