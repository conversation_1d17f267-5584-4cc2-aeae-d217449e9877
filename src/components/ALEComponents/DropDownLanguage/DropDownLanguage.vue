<template>
  <div class="dropdown">
    <button
      :class="[Store.isMobile?'min-w-24':'min-w-[12rem] ', 'dropdown-btn', isOpen ? 'bg-primary text-primaryText' : 'bg-secondary text-secondaryText']"
      @click="toggleDropdown"
    >
      <div class="flex gap-2 items-center">
        <span class="icon w-5 h-5 m-0 fill-primary"><svg
          width="14"
          height="15"
          viewBox="0 0 14 15"
          fill="primary"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            :class="[isOpen ? 'stroke-secondary' : 'stroke-black']"
            d="M2.36549 6.71651L2.26681 7.2999H2.85849H4.22069H4.69273L4.71986 6.82864C4.77974 5.78852 4.97707 4.84582 5.27155 4.10118L5.82231 2.70851L4.54536 3.49097C3.97279 3.84182 3.48199 4.31121 3.10599 4.86759C2.72998 5.42396 2.47749 6.0544 2.36549 6.71651ZM5.69955 4.11345L5.69947 4.11362C5.39987 4.81217 5.19177 5.73473 5.12387 6.76709L5.08883 7.2999H5.62279H8.37799H8.91196L8.87691 6.76709C8.80902 5.73479 8.60093 4.81228 8.30136 4.11374C8.11732 3.6843 7.90354 3.35099 7.67719 3.1275L7.67715 3.12747C7.44922 2.90248 7.2206 2.7999 7.00039 2.7999C6.7803 2.7999 6.55181 2.90237 6.32401 3.12709C6.09593 3.35147 5.88296 3.68624 5.69955 4.11345ZM9.28092 6.82864L9.30805 7.2999H9.78009H11.1423H11.734L11.6353 6.71651C11.5233 6.0544 11.2708 5.42396 10.8948 4.86759C10.5188 4.31121 10.028 3.84182 9.45543 3.49097L8.17847 2.70851L8.72923 4.10118C9.02371 4.84582 9.22104 5.78852 9.28092 6.82864ZM8.87691 8.23272L8.91196 7.6999H8.37799H5.62279H5.08883L5.12387 8.23272C5.19177 9.26508 5.39987 10.1876 5.69947 10.8862C5.88351 11.3156 6.09727 11.6488 6.3236 11.8723L6.32364 11.8723C6.55156 12.0973 6.78018 12.1999 7.00039 12.1999C7.22049 12.1999 7.44899 12.0974 7.67679 11.8727C7.90454 11.6486 8.11834 11.3141 8.30146 10.8858C8.60098 10.1873 8.80903 9.2649 8.87691 8.23272ZM8.72928 10.8985L8.17784 12.2917L9.45543 11.5088C10.028 11.158 10.5188 10.6886 10.8948 10.1322C11.2708 9.57585 11.5233 8.9454 11.6353 8.28329L11.734 7.6999H11.1423H9.78009H9.30805L9.28092 8.17117C9.22103 9.21137 9.02369 10.1547 8.72928 10.8985ZM4.54536 11.5088L5.82231 12.2913L5.27155 10.8986C4.97708 10.154 4.77978 9.21139 4.72058 8.17149L4.69374 7.6999H4.22139H2.85849H2.26681L2.36549 8.28329C2.47749 8.9454 2.72998 9.57585 3.10599 10.1322C3.48199 10.6886 3.97279 11.158 4.54536 11.5088ZM3.39415 3.89366C4.35058 2.93722 5.64779 2.3999 7.00039 2.3999C8.35299 2.3999 9.6502 2.93722 10.6066 3.89366C11.5631 4.85009 12.1004 6.1473 12.1004 7.4999C12.1004 8.85251 11.5631 10.1497 10.6066 11.1061C9.6502 12.0626 8.35299 12.5999 7.00039 12.5999C5.64779 12.5999 4.35058 12.0626 3.39415 11.1061C2.43771 10.1497 1.90039 8.85251 1.90039 7.4999C1.90039 6.1473 2.43771 4.85009 3.39415 3.89366Z"
          />
        </svg>
        </span>
        <span v-if="!Store.isMobile"> {{ (languageLabels[selectedLanguage].split(',')[1] ) }}</span>
        <span v-else>{{ selectedLanguage }}</span>
      </div>
      <span :class="Store.isMobile ? '' : 'arrow'">
        <svg
          :class="[isToggle ? 'w-3.5 h-3.5 rotate-[-180] transition-transform duration-500' : 'w-3.5 h-3.5 rotate-180 transition-transform duration-500']"
          width="14"
          height="7"
          viewBox="0 0 14 7"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            class="fill-secondaryText"
            d="M7.0147 0C6.68056 6.62804e-05 6.36012 0.122775 6.12388 0.341136L1.08388 4.99826C0.963539 5.10566 0.86755 5.23414 0.801514 5.37618C0.735479 5.51823 0.70072 5.67101 0.699266 5.8256C0.697813 5.98019 0.729692 6.13351 0.793046 6.27659C0.8564 6.41968 0.949959 6.54967 1.06826 6.65899C1.18657 6.76831 1.32725 6.85476 1.4821 6.9133C1.63695 6.97184 1.80287 7.0013 1.97017 6.99996C2.13747 6.99861 2.30281 6.96649 2.45653 6.90548C2.61026 6.84446 2.74929 6.75576 2.86552 6.64456L7.0147 2.81058L11.1639 6.64456C11.4015 6.85664 11.7198 6.97399 12.0502 6.97134C12.3805 6.96869 12.6966 6.84624 12.9302 6.63038C13.1638 6.41451 13.2963 6.12249 13.2992 5.81722C13.302 5.51195 13.175 5.21785 12.9455 4.99826L7.90552 0.341136C7.66928 0.122775 7.34885 6.62804e-05 7.0147 0Z"
          />
        </svg>
      </span>
    </button>
    <ul
      v-if="isOpen"
      ref="dropDownList"
      class="dropdown-menu w-[-webkit-fill-available] bg-secondary opacity-90 text-secondaryText"
    >
      <template
        v-for="language in availableLanguages"
        :key="language.code"
      >
        <li
          v-if="language.code !== selectedLanguage"
          class="dropdown-item hover:bg-primary hover:text-secondary hover:opacity-80"
          @click="selectLanguage(language.code)"
        >
          <span v-if="!Store.isMobile"> {{ (language.label.split(',')[0]) }} ({{ language.label.split(',')[1] }}) </span>
          <span v-else> {{ language.code }}  </span>
        </li>
      </template>
    </ul>
  </div>
</template>
<script setup>

import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { creationToolStore } from '../../../store/index';
import { languageSet } from '../../../config/masterdata';
import { onClickOutside } from '@vueuse/core';

const Store = creationToolStore();
const route = useRoute();
const selectedLanguage = ref(route.params.language || 'en');
const isOpen = ref(false);
const isToggle = ref(false), dropDownList = ref();
const languageLabels = languageSet;

const projectId = route.params.projectId;

const supportedLanguages = Store.projectCardData?.[projectId]?.projectSettings?.ale?.supported_languages || [];
const defaultLanguage = Store.projectCardData?.[route.params.projectId]?.projectSettings?.ale?.default_language || null;

const uniqueSupportedLanguages = Array.from(new Set([
  ...supportedLanguages,
  ...(defaultLanguage ? [defaultLanguage] : []),
]));

const availableLanguages = uniqueSupportedLanguages.map((langCode) => ({
  code: langCode,
  label: languageLabels[langCode] || langCode,
}));

onMounted(() => {
  console.log('Available Languages:', availableLanguages);
  console.log('Language Labels:', languageLabels);
});
function toggleDropdown () {
  isOpen.value = !isOpen.value;
}

function selectLanguage (language) {
  selectedLanguage.value = language;
  isOpen.value = false;

  const currentPath = route.fullPath;
  const languageCodes = availableLanguages.map((lang) => lang.code).join('|');
  const currentLanguage = currentPath.match(new RegExp(`^/(${languageCodes})`));

  let newPath;

  if (currentLanguage) {
    newPath = currentPath.replace(currentLanguage[0], `/${selectedLanguage.value}`);
  } else {
    newPath = `/${selectedLanguage.value}${currentPath}`;
  }

  window.location.href = newPath;
}

onClickOutside(dropDownList, () => isOpen.value = false);

</script>

<style scoped>
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-btn {
    padding: 8px 20px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    gap:1em;
    align-items: center;
}

.arrow {
    margin-left: auto;
}

.dropdown-menu {
    position: absolute;
    border-radius: 8px;
    overflow: hidden;
    margin-top: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 1;
    list-style: none;
    padding: 0;
}

.dropdown-item {
    padding: 10px 20px;
    cursor: pointer;

}

</style>
