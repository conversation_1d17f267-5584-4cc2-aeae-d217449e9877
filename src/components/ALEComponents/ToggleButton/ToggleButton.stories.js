import ToggleButton from './ToggleButton.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/ToggleButton',
  component: ToggleButton,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    label: 'Radius',
    id: 'asdadsad',
    active: true,
  },
  parameters: {
    design:
      {
        type: "figma",
        url: "https://www.figma.com/design/tzsvPTCvtPiJh166XcefIW/PropVR-Lite-with-Flowbite-Design-system?node-id=104-616&m=dev",
      },
  },
};
