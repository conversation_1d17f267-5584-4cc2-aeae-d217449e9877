<script setup>
import { ref, watchEffect } from 'vue';
import { creationToolStore } from '../../../store';
import { useRoute } from 'vue-router';

const props = defineProps({
  visible: { type: Boolean },
});
const emit = defineEmits(['close']);
const Store = creationToolStore();

const route = useRoute();
const shareUrl = ref('');
const copied = ref(false);

function fallbackCopy () {
  const textarea = document.createElement('textarea');
  textarea.value = shareUrl.value;
  textarea.setAttribute('readonly', '');
  textarea.style.position = 'absolute';
  textarea.style.left = '-9999px';
  document.body.appendChild(textarea);
  textarea.select();
  try {
    document.execCommand('copy');
    copied.value = true;
    setTimeout(() => (copied.value = false), 1500);
  } catch (err) {
    console.log(err);
  }
  document.body.removeChild(textarea);
}

function copyToClipboard () {
  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard.writeText(shareUrl.value)
      .then(() => {
        copied.value = true;
        setTimeout(() => (copied.value = false), 1500);
      })
      .catch(fallbackCopy);
  } else {
    fallbackCopy();
  }
}

function closeModal () {
  emit('close', false);
}

function shareOnWhatsApp () {
  const userAgent = navigator.userAgent.toLowerCase();
  const isMobile = /iphone|ipad|android/.test(userAgent);
  if (isMobile) {
    window.location.href = `whatsapp://send?text=${encodeURIComponent(`${shareUrl.value}`)}`;
  } else {
    window.open(`https://web.whatsapp.com/send?text=${encodeURIComponent(`${shareUrl.value}`)}`, '_blank');
  }
}

function shareOnEmail () {
  const subject = encodeURIComponent('Check this out!');
  const body = encodeURIComponent(`I wanted to share this with you: ${shareUrl.value}`);
  const userAgent = navigator.userAgent.toLowerCase();
  const isMobile = /iphone|ipad|android/.test(userAgent);
  if (isMobile) {
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
  } else {
    window.open(`https://mail.google.com/mail/?view=cm&fs=1&to=&su=${subject}&body=${body}`, '_blank');
  }
}

// function shareOnFacebook () {
//   const url = encodeURIComponent(shareUrl.value);
//   // window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank')
//   window.open(`http://www.facebook.com/sharer.php?u=${url}`, '_blank');
//   // window.open(`https://www.facebook.com/dialog/send?link=${url}&app_id=YOUR_APP_ID&redirect_uri=${url}`, '_blank')
// }

function shareOnTwitter () {
  const url = encodeURIComponent(shareUrl.value);
  window.open(`https://twitter.com/intent/tweet?text=&url=${url}`, '_blank');
}

watchEffect(() => {
  shareUrl.value = window.location.origin + route.fullPath;
});
</script>

<template>
  <div
    v-if="props.visible"
    :class="[Store.isMobile || Store.isLandscape ? 'absolute w-full bottom-0' : 'fixed inset-0', 'flex z-50 items-center justify-center']"
  >
    <div :class="['relative bg-secondary px-4 py-4 shadow-lg', Store.isMobile || Store.isLandscape ? 'w-full rounded-t-lg' : 'w-[370px] max-w-[90vw] rounded-lg']">
      <div class="flex items-center justify-between mb-4">
        <span class="text-secondaryText text-base font-semibold items-center">Share Experience</span>
        <button
          class="text-secondaryText"
          aria-label="Close"
          @click="closeModal"
        >
          <svg
            width="20"
            height="20"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
      <!-- Share Icons -->
      <div :class="['flex mb-6', Store.isLandscape ? 'gap-5' : 'justify-between']">
        <div class="flex flex-col items-center">
          <!-- WhatsApp Icon -->
          <div
            class="cursor-pointer w-14 h-14 rounded-full bg-tertiary50opacity flex items-center justify-center mb-2"
            @click="shareOnWhatsApp"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="4"
              height="4"
              viewBox="0 0 24 24"
              fill="none"
              class="w-6 h-6"
            >
              <path
                d="M20.4054 3.4875C18.1607 1.2375 15.1714 0 11.9946 0C5.4375 0 0.101786 5.33571 0.101786 11.8929C0.101786 13.9875 0.648214 16.0339 1.6875 17.8393L0 24L6.30536 22.3446C8.04107 23.2929 9.99643 23.7911 11.9893 23.7911H11.9946C18.5464 23.7911 24 18.4554 24 11.8982C24 8.72143 22.65 5.7375 20.4054 3.4875ZM11.9946 21.7875C10.2161 21.7875 8.475 21.3107 6.95893 20.4107L6.6 20.1964L2.86071 21.1768L3.85714 17.5286L3.62143 17.1536C2.63036 15.5786 2.11071 13.7625 2.11071 11.8929C2.11071 6.44464 6.54643 2.00893 12 2.00893C14.6411 2.00893 17.1214 3.0375 18.9857 4.90714C20.85 6.77679 21.9964 9.25714 21.9911 11.8982C21.9911 17.3518 17.4429 21.7875 11.9946 21.7875ZM17.4161 14.3839C17.1214 14.2339 15.6589 13.5161 15.3857 13.4196C15.1125 13.3179 14.9143 13.2696 14.7161 13.5696C14.5179 13.8696 13.95 14.5339 13.7732 14.7375C13.6018 14.9357 13.425 14.9625 13.1304 14.8125C11.3839 13.9393 10.2375 13.2536 9.08571 11.2768C8.78036 10.7518 9.39107 10.7893 9.95893 9.65357C10.0554 9.45536 10.0071 9.28393 9.93214 9.13393C9.85714 8.98393 9.2625 7.52143 9.01607 6.92679C8.775 6.34821 8.52857 6.42857 8.34643 6.41786C8.175 6.40714 7.97679 6.40714 7.77857 6.40714C7.58036 6.40714 7.25893 6.48214 6.98571 6.77679C6.7125 7.07679 5.94643 7.79464 5.94643 9.25714C5.94643 10.7196 7.0125 12.1339 7.15714 12.3321C7.30714 12.5304 9.25179 15.5304 12.2357 16.8214C14.1214 17.6357 14.8607 17.7054 15.8036 17.5661C16.3768 17.4804 17.5607 16.8482 17.8071 16.1518C18.0536 15.4554 18.0536 14.8607 17.9786 14.7375C17.9089 14.6036 17.7107 14.5286 17.4161 14.3839Z"
                fill="white"
              />
            </svg>
          </div>
          <span class="text-xs text-secondaryText">WhatsApp</span>
        </div>
        <div class="flex flex-col items-center">
          <!-- Email Icon -->
          <div
            class="cursor-pointer w-14 h-14 rounded-full bg-tertiary50opacity flex items-center justify-center mb-2"
            @click="shareOnEmail"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              class="w-6 h-6"
            >
              <path
                d="M12 13.0968L23.13 3.5664C22.7026 3.2033 22.1608 3.00271 21.6 3H2.4C1.83921 3.00271 1.29738 3.2033 0.87 3.5664L12 13.0968Z"
                fill="white"
              />
              <path
                d="M13.5 14.9724C13.0633 15.3065 12.5294 15.4889 11.9796 15.492C11.4672 15.493 10.9684 15.3271 10.5588 15.0192L0 5.9808V18.6C0 19.2365 0.252856 19.847 0.702944 20.2971C1.15303 20.7471 1.76348 21 2.4 21H21.6C22.2365 21 22.847 20.7471 23.2971 20.2971C23.7471 19.847 24 19.2365 24 18.6V5.9808L13.5 14.9724Z"
                fill="white"
              />
            </svg>
          </div>
          <span class="text-xs text-secondaryText">Email</span>
        </div>
        <!-- Facebook Icon -->
        <!-- <div class="flex flex-col items-center">
                    <div @click="shareOnFacebook" class="cursor-pointer w-14 h-14 rounded-full bg-tertiary50opacity flex items-center justify-center mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="13" height="24" viewBox="0 0 13 24" fill="none" class="w-6 h-6">
                        <path d="M12.0117 13.5L12.6783 9.15656H8.51063V6.33797C8.51063 5.14969 9.09281 3.99141 10.9594 3.99141H12.8541V0.293438C12.8541 0.293438 11.1347 0 9.49078 0C6.05859 0 3.81516 2.08031 3.81516 5.84625V9.15656H0V13.5H3.81516V24H8.51063V13.5H12.0117Z" fill="white"/>
                        </svg>
                    </div>
                    <span class="text-xs text-secondaryText">Facebook</span>
                </div> -->
        <div class="flex flex-col items-center">
          <!-- Twitter Icon -->
          <div
            class="cursor-pointer w-14 h-14 rounded-full bg-tertiary50opacity flex items-center justify-center mb-2"
            @click="shareOnTwitter"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="20"
              viewBox="0 0 24 20"
              fill="none"
              class="w-6 h-6"
            >
              <path
                d="M21.5331 4.85786C21.5483 5.07105 21.5483 5.28429 21.5483 5.49747C21.5483 12 16.5991 19.4924 7.55333 19.4924C4.7665 19.4924 2.17768 18.6853 0 17.2843C0.395955 17.33 0.776628 17.3452 1.18782 17.3452C3.48728 17.3452 5.60407 16.5686 7.29444 15.2437C5.13199 15.198 3.31979 13.7818 2.69542 11.8325C3.00001 11.8782 3.30456 11.9086 3.62439 11.9086C4.066 11.9086 4.50766 11.8477 4.9188 11.7412C2.66499 11.2843 0.974582 9.30458 0.974582 6.91371V6.85282C1.62938 7.21831 2.39087 7.44673 3.19792 7.47715C1.87304 6.59388 1.00505 5.08629 1.00505 3.38069C1.00505 2.46699 1.24866 1.62943 1.67508 0.898457C4.09642 3.88323 7.73605 5.83244 11.8172 6.04568C11.7411 5.68019 11.6954 5.29952 11.6954 4.9188C11.6954 2.2081 13.8883 0 16.6142 0C18.0304 0 19.3096 0.593909 20.2081 1.5533C21.3198 1.34011 22.3858 0.928926 23.33 0.365486C22.9644 1.50765 22.1878 2.46704 21.1675 3.07614C22.1574 2.96959 23.1168 2.69542 24 2.31474C23.3301 3.28933 22.4924 4.15731 21.5331 4.85786Z"
                fill="white"
              />
            </svg>
          </div>
          <span class="text-xs text-secondaryText">Twitter</span>
        </div>
      </div>
      <!-- Share Link and Copy Button -->
      <div class="flex items-center bg-tertiary50opacity rounded-lg px-3 py-2">
        <input
          class="flex-1 bg-transparent text-secondaryText text-sm outline-none"
          :value="shareUrl"
          readonly
        >
        <button
          class="ml-2 flex items-center bg-primary text-primaryText px-3 py-1.5 rounded-md hover:bg-opacity-90 transition gap-1"
          @click="copyToClipboard"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 14 14"
            fill="none"
            class="stroke-current"
          >
            <path
              d="M11.7992 5.20001H6.39922C5.73648 5.20001 5.19922 5.73727 5.19922 6.40001V11.8C5.19922 12.4628 5.73648 13 6.39922 13H11.7992C12.462 13 12.9992 12.4628 12.9992 11.8V6.40001C12.9992 5.73727 12.462 5.20001 11.7992 5.20001Z"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M2.8 8.8H2.2C1.88174 8.8 1.57652 8.67357 1.35147 8.44853C1.12643 8.22348 1 7.91826 1 7.6V2.2C1 1.88174 1.12643 1.57652 1.35147 1.35147C1.57652 1.12643 1.88174 1 2.2 1H7.6C7.91826 1 8.22348 1.12643 8.44853 1.35147C8.67357 1.57652 8.8 1.88174 8.8 2.2V2.8"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          <span>{{ copied ? 'Copied!' : 'Copy' }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
