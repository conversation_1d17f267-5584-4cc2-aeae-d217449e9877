import SliderInput from "./SliderInput.vue";

export default {
  title: "Design System/ALE/SliderInput",
  component: SliderInput,
  tags: ["autodocs"],
  argTypes: {
    labelType: {
      control: "text",
      description: "Main label for the slider input",
    },
    type: {
      control: "text",
      description: "Sub-type or additional descriptor",
    },
    minValue: {
      control: "number",
      description: "Minimum possible value",
    },
    maxValue: {
      control: "number",
      description: "Maximum possible value",
    },
    initialMin: {
      control: "number",
      description: "Initial minimum value",
    },
    initialMax: {
      control: "number",
      description: "Initial maximum value",
    },
  },
};

export const PriceRangeSlider = {
  args: {
    labelType: "Price Range",
    type: "USD",
    minValue: 0,
    maxValue: 10000,
    initialMin: 500,
    initialMax: 5000,
  },
};

export const AgeRangeSlider = {
  args: {
    labelType: "Age Range",
    type: "Years",
    minValue: 18,
    maxValue: 100,
    initialMin: 25,
    initialMax: 55,
  },
};

export const DefaultRangeSlider = {
  args: {
    labelType: "Default Range",
    type: "",
    minValue: 0,
    maxValue: 100,
    initialMin: 20,
    initialMax: 80,
  },
};

export const FullRangeSlider = {
  args: {
    labelType: "Full Range Example",
    type: "Complete",
    minValue: 0,
    maxValue: 1000000,
    initialMin: 10000,
    initialMax: 500000,
  },
};
