import ControlButton from './ControlButton.vue';

export default {
  title: 'Design System/ALE/ControlButton',
  component: ControlButton,
  tags: ['autodocs'],
  argTypes: {

  },
};

const Template = (args) => ({
  components: { ControlButton },
  setup () {
    return { args };
  },
  template: `
    <ControlButton :active="args.active" :isHover="args.isHover" :name="args.name" :inactiveSVG="args.inactiveSVG" :activeSvg="args.activeSvg">
      <template v-slot:svg>

      </template>
    </ControlButton>
  `,
});

export const Primary = Template.bind({});
Primary.args = {
  active: true,
  isHover: true,
  name: "Fullscreen",
  activeSVG: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M10.1199 20.9994H4V14.8794H6V18.9823H10.1199V20.9994Z" fill="#5E5E5E"/>
  <path d="M6 10L4 10.1202V4.00024H10.1199V6.00003H6V10Z" fill="#5E5E5E"/>
  <path d="M20.9998 10.12H19V6H14.8799V4H20.9998V10.12Z" fill="#5E5E5E"/>
  <path d="M20.9997 20.9994H14.8799V19.0001H19V14.8794H20.9997V20.9994Z" fill="#5E5E5E"/>
  </svg>`,
  inactiveSVG: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M10.1199 20.9994H4V14.8794H6V18.9823H10.1199V20.9994Z" fill="#5E5E5E"/>
  <path d="M6 10L4 10.1202V4.00024H10.1199V6.00003H6V10Z" fill="#5E5E5E"/>
  <path d="M20.9998 10.12H19V6H14.8799V4H20.9998V10.12Z" fill="#5E5E5E"/>
  <path d="M20.9997 20.9994H14.8799V19.0001H19V14.8794H20.9997V20.9994Z" fill="#5E5E5E"/>
  </svg>
  `,
};

export const Design = {
  args: {
    active: true,
    isHover: true,
    name: "Fullscreen",
    activeSVG: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M10.1199 20.9994H4V14.8794H6V18.9823H10.1199V20.9994Z" fill="#5E5E5E"/>
  <path d="M6 10L4 10.1202V4.00024H10.1199V6.00003H6V10Z" fill="#5E5E5E"/>
  <path d="M20.9998 10.12H19V6H14.8799V4H20.9998V10.12Z" fill="#5E5E5E"/>
  <path d="M20.9997 20.9994H14.8799V19.0001H19V14.8794H20.9997V20.9994Z" fill="#5E5E5E"/>
  </svg>`,
    inactiveSVG: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M10.1199 20.9994H4V14.8794H6V18.9823H10.1199V20.9994Z" fill="#5E5E5E"/>
  <path d="M6 10L4 10.1202V4.00024H10.1199V6.00003H6V10Z" fill="#5E5E5E"/>
  <path d="M20.9998 10.12H19V6H14.8799V4H20.9998V10.12Z" fill="#5E5E5E"/>
  <path d="M20.9997 20.9994H14.8799V19.0001H19V14.8794H20.9997V20.9994Z" fill="#5E5E5E"/>
  </svg>
  `,
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=263-39844&mode=design&t=MJoHYvcCnJNPKlHr-4",
    },
  },
};
