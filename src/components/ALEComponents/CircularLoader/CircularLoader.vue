<script setup>
</script>
<template>
  <div class="relative h-full w-full">
    <div class="absolute h-full w-full flex items-center justify-center">
      <div class="loader" />
    </div>
  </div>
</template>
<style scoped>
.loader {
  border: 8px solid #343435;
  border-radius: 50%;
  border-top: 8px solid #f3f3f3;
  width: 60px;
  height: 60px;
  -webkit-animation: spin 2s linear infinite; /* Safari */
  animation: spin 2s linear infinite;
}

/* Safari */
@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
