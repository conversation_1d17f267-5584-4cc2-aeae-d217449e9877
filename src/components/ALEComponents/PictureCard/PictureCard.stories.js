import PictureCard from './PictureCard.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/PictureCard',
  component: PictureCard,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    imageUrl: "https://external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fpixy.org%2Fsrc%2F477%2F4774988.jpg&f=1&nofb=1&ipt=9c4dfb810ffe9c2a015db0547035e8ce0f209c40394d0b76df073bdbb8e1be0c&ipo=images",
    title: "Palmiera",
    location: " Business Bay",
    bedrooms: "2 to 4 BR",
    price: "75,00,000",
    type: "Apartment",
    communityId: "null",

  },
};

export const Secondary = {
  args: {

  },
};

export const Large = {
  args: {

  },
};

export const Small = {
  args: {

  },
};
