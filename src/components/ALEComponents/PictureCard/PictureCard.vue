<template>
  <AnimatedContent
    :distance="100"
    direction="horizontal"
    :reverse="false"
    :duration="0.8"
    ease="power3.out"
    :initial-opacity="0"
    :animate-opacity="true"
    :scale="1"
    :threshold="0.1"
    :delay="0"
  >
    <div
     class="relative overflow-hidden card-class !max-w-none bg-white rounded-lg border border-gray-200 shadow-md hover:bg-gray-100"
    :class="[Store.isMobile?'!w-full rounded-b-none':'!max-w-sm ',towerLayer?'w-fit':'!max-w-md ']"
    >
      <div
        class="w-full flex-row flex shadow-lg "
        :class="isMasterScene?'!h-[150px]': (isProjectCategory ? 'h-[100px]' : '')"
      >
        <img
          v-if="imageUrl"
          class="sm:w-40 w-36 object-cover"
          :class="[Store.isMobile ? 'h-[10.5rem]' : '', isMasterScene?'!w-32':'', isProjectCategory?'min-h-[85px] max-h-full':'']"
          :src="imageUrl"
          alt="Property image"
        >
        <div
          class="w-full sm:px-4 pl-5 pr-6 sm:pr-[1.5rem] gap-2 bg-secondary flex flex-col justify-between"
          :class="isProjectCategory?'!py-2 items-start h-full':'py-4 '"
        >
          <div class="flex justify-start items-start flex-wrap-reverse gap-2">
            <div
              class="text-secondaryText capitalize leading-5"
              :class="isProjectCategory?'font-medium text-sm':'font-bold  text-base '"
            >
              <TranslationComp
                :key="title"
                :text="title"
              />
            </div>
            <div>
              <StatusPill
                v-if="status!=='' && (communityLayer||towerLayer)"
                :availability-status="status"
                class="text-sm"
              />
            </div>
          </div>

          <p
            v-if="!Store.isMobile && location"
            class="text-secondaryText font-normal -mt-1"
            :class="isProjectCategory || isMasterScene?'text-xs capitalize':'text-sm uppercase'"
          >
            <TranslationComp
              :key="location"
              :text="location"
            />
          </p>
          <div
            class="flex sm:flex-col"
            :class="Store.isMobile?'flex-col gap-3':'sm:gap-2 gap-4'"
          >
            <div
              class="grid grid-cols-2"
              :class="towerLayer?'gap-14':'gap-4'"
            >
              <!-- type -->
              <div
                v-if="type && (cardDetails.type || showDefault)"
                class="flex items-center gap-2"
              >
                <div>
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    class="towersvg"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M4 2.1335V14.6668H12L12 12.0833H9.33333C8.91912 12.0833 8.58333 11.7475 8.58333 11.3333C8.58333 10.9191 8.91912 10.5833 9.33333 10.5833H12V9.41667H9.33333C8.91912 9.41667 8.58333 9.08088 8.58333 8.66667C8.58333 8.25245 8.91912 7.91667 9.33333 7.91667H11.9994C11.997 7.22964 11.9861 6.80798 11.9343 6.4488C11.7975 6.63165 11.5792 6.75 11.3333 6.75H9.33333C8.91912 6.75 8.58333 6.41421 8.58333 6C8.58333 5.58579 8.91912 5.25 9.33333 5.25H11.3333C11.4476 5.25 11.556 5.27557 11.6529 5.3213C10.9269 3.27992 9.13549 1.75532 6.9386 1.40736C6.47221 1.3335 5.91481 1.3335 4.8 1.3335C4.31399 1.3335 4 1.61606 4 2.1335Z"
                      fill="#9CA3AF"
                    />
                  </svg>
                </div>
                <p class="text-colormix text-xs capitalize">
                  <TranslationComp
                    :key="type"
                    :text="type"
                  />
                </p>
              </div>

              <!-- bedroom -->
              <div
                v-if="bedrooms && (cardDetails.bedrooms || showDefault)"
                class="flex items-center gap-2"
              >
                <div>
                  <svg
                    viewBox="0 0 16 17"
                    class=" w-4 h-4 bedroomSVG"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M14.6666 12.1666H1.33325"
                      stroke="#6B7280"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M14.6666 14.5V11.1667C14.6666 9.9096 14.6666 9.28107 14.2761 8.89053C13.8855 8.5 13.257 8.5 11.9999 8.5H3.99992C2.74284 8.5 2.1143 8.5 1.72378 8.89053C1.33325 9.28107 1.33325 9.9096 1.33325 11.1667V14.5"
                      stroke="#6B7280"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M14 8.5V5.40705C14 4.94595 14 4.71541 13.8719 4.49769C13.7438 4.27997 13.5613 4.16727 13.1963 3.94189C11.7246 3.03319 9.93287 2.5 8 2.5C6.06711 2.5 4.27543 3.03319 2.80372 3.94189C2.43869 4.16727 2.25618 4.27997 2.12809 4.49769C2 4.71541 2 4.94595 2 5.40705V8.5H14ZM7.33333 8V6.80893C7.33333 6.55515 7.2952 6.47027 7.0998 6.37025C6.693 6.16195 6.1991 6 5.66667 6C5.13423 6 4.64037 6.16195 4.2335 6.37025C4.03814 6.47027 4 6.55515 4 6.80893V8H7.33333ZM12.0001 6.80893V8H8.66675V6.80893C8.66675 6.55515 8.70488 6.47027 8.90028 6.37025C9.30708 6.16195 9.80095 6 10.3334 6C10.8659 6 11.3597 6.16195 11.7665 6.37025C11.9619 6.47027 12.0001 6.55515 12.0001 6.80893Z"
                      fill="#6B7280"
                    />
                    <rect
                      x="2"
                      y="9"
                      width="12"
                      height="3"
                      fill="#6B7280"
                    />
                  </svg>
                </div>
                <p class="text-colormix text-xs">
                  <TranslationComp
                    :key="bedrooms"
                    :text="bedrooms"
                  />
                </p>
              </div>
            </div>

            <div
              class="grid grid-cols-2"
              :class="towerLayer?'gap-14':'gap-4'"
            >
              <!-- units -->
              <div
                v-if="units && !isMasterScene && !isProjectCategory"
                class="flex items-center gap-2"
              >
                <svg
                  class="w-4 h-4 secondlinesvg"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M7.49695 7.22974V1.06677H2.87473C2.02732 1.06677 1.33398 1.76011 1.33398 2.60751V7.22974H7.49695ZM9.03769 7.22974H15.2007V2.60751C15.2007 1.76011 14.5073 1.06677 13.6599 1.06677H9.03769V7.22974ZM7.49695 8.77048H1.33398V13.3927C1.33398 14.2401 2.02732 14.9334 2.87473 14.9334H7.49695V8.77048ZM9.03769 8.77048V14.9334H13.6599C14.5073 14.9334 15.2007 14.2401 15.2007 13.3927V8.77048H9.03769Z"
                    fill="#9CA3AF"
                  />
                </svg>
                <p class="text-colormix text-xs capitalize whitespace-nowrap">
                  <TranslationComp
                    :key="units"
                    :text="units"
                  />
                </p>
              </div>

              <!-- floor -->
              <div
                v-if="totalFloors"
                class="flex items-center gap-2"
              >
                <svg
                  class="w-4 h-4 secondlinesvg"
                  width="18"
                  height="18"
                  viewBox="0 0 18 18"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M16.1806 0.749878H12.5693C12.388 0.749878 12.241 0.89691 12.241 1.0782C12.241 1.25947 12.388 1.40651 12.5693 1.40651H12.8976V3.37633H9.61446C9.43319 3.37633 9.28614 3.52336 9.28614 3.70465C9.28614 3.88592 9.43317 4.03296 9.61446 4.03296H9.94277V6.33107H6.6596C6.47833 6.33107 6.33128 6.4781 6.33128 6.65939C6.33128 6.84066 6.47831 6.9877 6.6596 6.9877H6.98792V9.61412H4.03318C3.85191 9.61412 3.70486 9.76116 3.70486 9.94244C3.70486 10.1237 3.85189 10.2708 4.03318 10.2708H4.36149V12.5689H1.07832C0.897047 12.5689 0.75 12.7159 0.75 12.8972C0.75 13.0785 0.897032 13.2255 1.07832 13.2255H1.40664V15.8519H1.07832C0.897047 15.8519 0.75 15.999 0.75 16.1802C0.75 16.3615 0.897032 16.5086 1.07832 16.5086C1.08336 16.5086 16.1771 16.5087 16.1801 16.5087C16.3614 16.5087 16.5084 16.3616 16.5084 16.1804V1.07828C16.5084 0.897012 16.3619 0.749878 16.1806 0.749878ZM15.8523 15.8517L16.1801 15.8519L16.1889 13.1999L4.68981 13.2252C4.87108 13.2252 16.1889 13.3812 16.1889 13.1999L16.1801 10.7999L7.31623 10.2705C7.4975 10.2705 16.1801 10.9812 16.1801 10.7999L16.1889 7.19988L10.271 6.98743C10.4522 6.98743 16.1889 7.38116 16.1889 7.19988V4.19988L13.2257 4.03269C13.407 4.03269 16.1889 4.38116 16.1889 4.19988L15.8521 1.79988L15.8521 1.40627L15.8523 15.8517Z"
                    fill="#6B7280"
                  />
                  <path
                    d="M10.5996 13.8819C10.5996 14.0632 10.7466 14.2103 10.9279 14.2103H14.2111C14.3924 14.2103 14.5394 14.0632 14.5394 13.8819V8.95747C14.5394 8.7762 14.3924 8.62915 14.2111 8.62915C14.0298 8.62915 13.8828 8.77618 13.8828 8.95747V13.5536H10.928C10.7468 13.5536 10.5997 13.7006 10.5997 13.8819L10.5996 13.8819Z"
                    fill="#6B7280"
                  />
                </svg>
                <p class="text-colormix text-xs">
                  {{ totalFloors }}
                  <TranslationComp
                    text="Floors"
                  />
                </p>
              </div>
            </div>
          </div>

          <div
            v-if="price && (price !== 0 && price !== '' && price !== '0')"
            class="flex justify-between py-0.5"
            :class="isMasterScene || isProjectCategory?'-mt-3':'pt-2'"
          >
            <span
              v-if="isMasterScene || isProjectCategory"
              class="text-primary text-xs flex items-end"
            >Starting<span
              class="ml-1 font-bold text-primary text-sm uppercase"
              :class="isProjectCategory?'font-semibold text-xs':''"
            >  {{ selectedCurrencyTitle ? selectedCurrencyTitle : currencyTitle }} <CurrencyComp :price="price" /></span> </span>

            <span
              v-else
              class="font-bold text-primary text-sm uppercase"
            >{{ currencyTitle }} {{ thousandSeparator(price) }} </span>
          </div>

          <fwb-button
            v-if="communityLayer ? availableUnitsPresent : showExplore "
            class="flex bg-primary  text-primaryText font-medium justify-center mt-2 buttonClass hover:bg-primary focus:ring-0"
            :class="[isMasterScene || isProjectCategory? '!w-[9rem] -mt-1':'', Store.isMobile?'!w-full text-xs':'!w-auto text-sm']"
            :size="isMasterScene || isProjectCategory?'sm':'lg'"
            @click="emits('explore')"
          >
            <TranslationComp
              text="Explore"
            />
            <template #suffix>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="15"
                height="15"
                viewBox="0 0 15 15"
                fill="none"
                class="svgprimary"
              >
                <g clip-path="url(#clip0_3520_2068)">
                  <path
                    d="M13.8452 7.10432L13.8454 7.10443C13.9172 7.17961 13.9685 7.27815 13.9895 7.38871C14.0106 7.49935 13.9996 7.61381 13.9589 7.71671L14.4239 7.90059L13.9585 7.71782C13.9318 7.78581 13.8931 7.8462 13.8458 7.89611C13.8457 7.89626 13.8456 7.89641 13.8454 7.89656L9.84599 12.085L9.84594 12.0849L9.84006 12.0913C9.79271 12.1426 9.73734 12.1822 9.67787 12.209C9.61846 12.2357 9.55549 12.2494 9.49242 12.25C9.42935 12.2506 9.36623 12.238 9.30651 12.2124C9.24673 12.1868 9.19083 12.1483 9.14275 12.0979C9.09461 12.0475 9.05537 11.9863 9.02841 11.9173C9.00144 11.8483 8.98761 11.7736 8.98824 11.6977C8.98887 11.6219 9.00393 11.5475 9.032 11.4791C9.06006 11.4107 9.10024 11.3503 9.14909 11.3009L9.14914 11.3009L9.15514 11.2947L11.4483 8.89315L12.2554 8.04784H11.0867H1.50006C1.37508 8.04784 1.25028 7.99606 1.15453 7.89579C1.05803 7.79473 1 7.65294 1 7.50052C1 7.34811 1.05803 7.20632 1.15453 7.10526C1.25028 7.00498 1.37508 6.9532 1.50006 6.9532H11.0867H12.2554L11.4483 6.1079L9.15414 3.70535L9.1542 3.70529L9.14809 3.69912C9.09924 3.64971 9.05906 3.58935 9.031 3.52094C9.00293 3.4525 8.98787 3.37811 8.98724 3.30227C8.98661 3.22644 9.00044 3.15172 9.02741 3.08268C9.05437 3.01367 9.09361 2.95246 9.14175 2.90205C9.18983 2.8517 9.24572 2.8132 9.30551 2.78758C9.36523 2.76199 9.42835 2.74944 9.49142 2.75002C9.55449 2.75059 9.61746 2.76429 9.67687 2.79101C9.73633 2.81777 9.79171 2.85736 9.83906 2.90871L9.83901 2.90876L9.845 2.91504L13.8452 7.10432Z"
                    fill="#374151"
                    stroke="#374151"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_3520_2068">
                    <rect
                      width="14"
                      height="14"
                      fill="white"
                      transform="translate(0.5 0.5)"
                    />
                  </clipPath>
                </defs>
              </svg>
            </template>
          </fwb-button>

          <fwb-button
            v-if="unitplanPresent && status !==''"
            class="flex bg-primary  text-primaryText font-medium justify-center mt-2 buttonClass hover:bg-primary focus:ring-0"
            :class="[isMasterScene || isProjectCategory? '!w-[9rem] -mt-1':'', Store.isMobile?'!w-full text-xs':'!w-auto text-sm']"
            :size="isMasterScene || isProjectCategory?'sm':'lg'"
            @click="emits('showunits')"
          >
            <TranslationComp
              text="Show Units"
            />
            <template #suffix>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="15"
                height="15"
                viewBox="0 0 15 15"
                fill="none"
                class="svgprimary"
              >
                <g clip-path="url(#clip0_3520_2068)">
                  <path
                    d="M13.8452 7.10432L13.8454 7.10443C13.9172 7.17961 13.9685 7.27815 13.9895 7.38871C14.0106 7.49935 13.9996 7.61381 13.9589 7.71671L14.4239 7.90059L13.9585 7.71782C13.9318 7.78581 13.8931 7.8462 13.8458 7.89611C13.8457 7.89626 13.8456 7.89641 13.8454 7.89656L9.84599 12.085L9.84594 12.0849L9.84006 12.0913C9.79271 12.1426 9.73734 12.1822 9.67787 12.209C9.61846 12.2357 9.55549 12.2494 9.49242 12.25C9.42935 12.2506 9.36623 12.238 9.30651 12.2124C9.24673 12.1868 9.19083 12.1483 9.14275 12.0979C9.09461 12.0475 9.05537 11.9863 9.02841 11.9173C9.00144 11.8483 8.98761 11.7736 8.98824 11.6977C8.98887 11.6219 9.00393 11.5475 9.032 11.4791C9.06006 11.4107 9.10024 11.3503 9.14909 11.3009L9.14914 11.3009L9.15514 11.2947L11.4483 8.89315L12.2554 8.04784H11.0867H1.50006C1.37508 8.04784 1.25028 7.99606 1.15453 7.89579C1.05803 7.79473 1 7.65294 1 7.50052C1 7.34811 1.05803 7.20632 1.15453 7.10526C1.25028 7.00498 1.37508 6.9532 1.50006 6.9532H11.0867H12.2554L11.4483 6.1079L9.15414 3.70535L9.1542 3.70529L9.14809 3.69912C9.09924 3.64971 9.05906 3.58935 9.031 3.52094C9.00293 3.4525 8.98787 3.37811 8.98724 3.30227C8.98661 3.22644 9.00044 3.15172 9.02741 3.08268C9.05437 3.01367 9.09361 2.95246 9.14175 2.90205C9.18983 2.8517 9.24572 2.8132 9.30551 2.78758C9.36523 2.76199 9.42835 2.74944 9.49142 2.75002C9.55449 2.75059 9.61746 2.76429 9.67687 2.79101C9.73633 2.81777 9.79171 2.85736 9.83906 2.90871L9.83901 2.90876L9.845 2.91504L13.8452 7.10432Z"
                    fill="#374151"
                    stroke="#374151"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_3520_2068">
                    <rect
                      width="14"
                      height="14"
                      fill="white"
                      transform="translate(0.5 0.5)"
                    />
                  </clipPath>
                </defs>
              </svg>
            </template>
          </fwb-button>
        </div>
      </div>
    </div>
  </AnimatedContent>
</template>

<script setup>

import { FwbButton } from 'flowbite-vue';
import { creationToolStore } from '../../../store';
import TranslationComp from '../TranslationComp/TranslationComp.vue';
import { ref, defineProps, defineEmits } from "vue";
import { useRoute } from "vue-router";
import StatusPill from '../StatusPill/StatusPill.vue';
import { getCookie, thousandSeparator } from "../../../helpers/helper";
import CurrencyComp from '../CurrencyComp/CurrencyComp.vue';
import AnimatedContent from '../../../animations/AnimatedContent/AnimatedContent.vue';
const route = useRoute();
const emits = defineEmits(['explore', 'showunits']);
const Store = creationToolStore();
const { imageUrl, title, location, bedrooms, type, units, totalFloors, isMasterScene, currencyTitle, price, towerLayer, showExplore, availableUnitsPresent, status, communityLayer,
  //  Price
} = defineProps({
  imageUrl: { type: String, default: "" },
  title: { type: String, default: "" },
  location: { type: String, default: "" },
  bedrooms: { type: String, default: "" },
  price: { type: Number, default: 0 },
  currencyTitle: { type: Number, default: 0 },
  type: { type: String, default: "" },
  units: { type: String, default: "" },
  totalFloors: { type: String, default: ""},
  isMasterScene: { type: Boolean, default: false},
  showExplore: { type: Boolean, default: true},
  isProjectCategory: { type: Boolean, default: false},
  towerLayer: { type: Boolean, default: false },
  communityLayer: { type: Boolean, default: false },
  availableUnitsPresent: { type: Boolean, default: false },
  status: { type: String, default: "" },
});

const showDefault = ref(), cardDetails = ref([]), unitplanPresent = ref(false);
const selectedCurrencyTitle = ref(Store.currencyData.currency || getCookie('selectedCurrency'));

unitplanPresent.value = Store.sidebarOptions && Store.sidebarOptions[route.params.projectId] && Object.values(Store.sidebarOptions[route.params.projectId]).some((option) => {
  return option.type === 'unitplan';
});

if (route.fullPath.includes('masterscene')) {
  showDefault.value = true;
} else {
  if (Store.projectCardData[route.params.projectId].projectSettings.ale.unit_card_customize_type){
    cardDetails.value = Store.projectCardData[route.params.projectId].projectSettings.ale.unitcard_config;
  } else {
    showDefault.value = true;
  }
}

</script>
<style scoped>
  svg path,rect{
  stroke:var(--colormix);
  fill:var(--colormix) !important;
}
</style>
<style lang="scss">
.card-class{
  border: none;
  --tw-bg-opacity: 0;
}
.card-class button {
  width: 185px;
}

.svgprimary g path{
  stroke: var(--primaryText) !important;
  fill: var(--primaryText) !important;
}

// .secondlinesvg path{
//   fill: var(--colormix);
// }

// .firstlinesvg  path{
//   stroke: var(--colormix);
// }

@media (min-width: 640px) {
  .card-class button {
    width: 13rem;
  }
}

/* Mobile animation for PictureCard */
.mobile-card-animation {
  animation: slideInFromBottom 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes slideInFromBottom {
  0% {
    transform: translateY(100px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
