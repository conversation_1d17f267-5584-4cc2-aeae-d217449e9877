<script setup>
import { ref, defineProps,  onMounted } from 'vue';
import { creationToolStore } from '../../../store';
import { getCookie } from '../../../helpers/helper';
import TranslationComp from '../TranslationComp/TranslationComp.vue';

const Store = creationToolStore();

defineEmits(["enter", 'closeX']);

defineProps({
  message: {
    type: String,
    default: "",
  },
  button:
  {
    type: String,
    default: "",
  },

  hideclose: Boolean,
});

const showPopup = ref(false);
let timeoutId;

function setFullScreenCookie () {
  if (!getCookie('fullscreen')) {
    const expiryTime = new Date(Date.now() + (30 * 60000)); // 30 minutes in milliseconds
    document.cookie = `fullscreen=true; path=/; expires=${expiryTime.toUTCString()};`;
  }
}

const startTimeout = () => {
  timeoutId = setTimeout(() => {
    showPopup.value = false;
    setFullScreenCookie();
  }, 5000);
};

const clearPopupTimeout = () => {
  clearTimeout(timeoutId);
};

const resetPopupTimeout = () => {
  clearPopupTimeout();
  startTimeout();
};

onMounted(() => {
  showPopup.value = true;
  startTimeout();
});

</script>

<template>
  <div
    v-if="showPopup"
    class="z-50 fixed left-[50%] -translate-x-[50%] w-full sm:max-w-[38rem] sm:px-4 sm:py-2 p-2 backdrop-blur-lg sm:bg-secondary bg-secondary rounded-none sm:rounded-2xl flex justify-between sm:justify-around items-center flex-row popup"
    :class="Store.isLandscape?'top-12': Store.isMobile ? 'sm:top-10':'!top-3'"
    @mouseover="clearPopupTimeout"
    @mouseleave="resetPopupTimeout"
  >
    <div class="flex gap-4 sm:gap-0 sm:flex-col sm:items-start sm:mb-3 items-center justify-center md:items-center md:flex-row md:mb-0">
      <a class="flex items-center sm:mb-2 border-gray-200 md:pe-4 md:me-4 md:border-e md:mb-0 dark:border-white">
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12Z"
            fill="#1F2A37"
          />
          <path
            d="M16 7.5H14C13.7235 7.5 13.5 7.724 13.5 8C13.5 8.276 13.7235 8.5 14 8.5H14.9045L13.1465 10.268C12.951 10.4635 12.951 10.7795 13.1465 10.975C13.244 11.0725 13.372 11.1215 13.5 11.1215C13.628 11.1215 13.756 11.0725 13.8535 10.975L15.5 9.3195V10C15.5 10.276 15.7235 10.5 16 10.5C16.2765 10.5 16.5 10.276 16.5 10V8C16.5 7.724 16.2765 7.5 16 7.5Z"
            fill="white"
          />
          <path
            d="M16 13.5C15.7235 13.5 15.5 13.724 15.5 14V14.7985L13.8535 13.1465C13.658 12.951 13.342 12.951 13.1465 13.1465C12.951 13.342 12.951 13.658 13.1465 13.8535L14.7875 15.5H14C13.7235 15.5 13.5 15.724 13.5 16C13.5 16.276 13.7235 16.5 14 16.5H16C16.2765 16.5 16.5 16.276 16.5 16V14C16.5 13.724 16.2765 13.5 16 13.5Z"
            fill="white"
          />
          <path
            d="M9.207 8.5H10C10.2765 8.5 10.5 8.276 10.5 8C10.5 7.724 10.2765 7.5 10 7.5H8C7.935 7.5 7.87 7.5135 7.809 7.5385C7.6865 7.589 7.589 7.6865 7.5385 7.809C7.5135 7.87 7.5 7.935 7.5 8V10C7.5 10.276 7.7235 10.5 8 10.5C8.2765 10.5 8.5 10.276 8.5 10V9.207L10.2675 10.975C10.365 11.0725 10.493 11.1215 10.621 11.1215C10.749 11.1215 10.877 11.0725 10.9745 10.975C11.17 10.7795 11.17 10.4635 10.9745 10.268L9.207 8.5Z"
            fill="white"
          />
          <path
            d="M10.2675 13.1465L8.5 14.914V14C8.5 13.724 8.2765 13.5 8 13.5C7.7235 13.5 7.5 13.724 7.5 14V16C7.5 16.276 7.7235 16.5 8 16.5H10C10.2765 16.5 10.5 16.276 10.5 16C10.5 15.724 10.2765 15.5 10 15.5H9.328L10.9745 13.8535C11.17 13.658 11.17 13.342 10.9745 13.1465C10.779 12.951 10.463 12.951 10.2675 13.1465Z"
            fill="white"
          />
        </svg>
      </a>
      <p
        v-if="!Store.isMobile"
        class="flex text-secondaryText text-base font-normal leading-6 p-0 sm:p-2 w-fit"
      >
        <TranslationComp
          :text="message"
        />
      </p>
      <span
        v-else
        class="flex text-secondaryText text-base font-normal leading-6 p-0 sm:p-2 w-fit"
      >
        <span>
          <TranslationComp
            :text="message"
          />
          <span
            class="underline text-secondaryText"
            @click="$emit('enter')"
          >
            <TranslationComp
              text="Enter the full screen mode"
            />
          </span>
        </span>
      </span>
    </div>

    <div
      v-if="!Store.isMobile"
      class="flex items-center flex-shrink-0"
    >
      <a
        v-if="button"
        href="#"
        class="px-5 py-2 me-2 text-xs font-medium text-primaryText bg-primary rounded-lg hover:text-primaryText focus:ring-0 focus:outline-none"
        @click="$emit('enter')"
      >
        <TranslationComp
          text="Enter Fullscreen"
        />
      </a>
      <button
        type="button"
        class="flex-shrink-0 inline-flex justify-center w-7 h-7 items-center text-secondaryText hover:bg-tertiary50opacity hover:text-secondaryText rounded-lg text-sm p-1.5 dark:hover:bg-tertiary50opacity dark:hover:text-secondaryText"
        @click="showPopup = false; $emit('closeX')"
      >
        <svg
          class="w-3 h-3"
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 14 14"
        >
          <path
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
          />
        </svg>
        <span class="sr-only">Close banner</span>
      </button>
    </div>
    <div v-else>
      <button
        type="button"
        class="flex-shrink-0 inline-flex justify-center w-7 h-7 items-center text-secondaryText hover:bg-tertiary50opacity hover:text-secondaryText rounded-lg text-sm p-1.5 dark:hover:bg-tertiary50opacity dark:hover:text-secondaryText"
        @click="showPopup = false; $emit('closeX')"
      >
        <svg
          class="w-3 h-3"
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 14 14"
        >
          <path
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
          />
        </svg>
        <span class="sr-only">Close banner</span>
      </button>
    </div>
  </div>
</template>

<style scoped>
  .popup {
  animation: dropAnimation 0.5s ease forwards;
}
@keyframes dropAnimation {
  from {
    top: 0%;
  }
  to {
    top: 5.7%;
  }
}
</style>
