import PanoramaCard from './PanoramaCard.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/PanoramaCard',
  component: PanoramaCard,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    name: 'Gym',
    mediaType: "image",
    link: "https://imgs.search.brave.com/xgVU82LRFcUUM106GQ3SYY6dJWOSK1DqCgtWrd1CWPE/rs:fit:500:0:0/g:ce/aHR0cHM6Ly9jNy5h/bGFteS5jb20vMzYw/L1dBNjk1SC9maXRu/ZXNzLWd5bS1jbHVi/LWZ1bGwtMzYwLWRl/Z3JlZS1wYW5vcmFt/YS1zcGhlcmljYWwt/cHJvamVjdGlvbi1X/QTY5NUguanBn",
    icon: "https://blogger.googleusercontent.com/img/a/AVvXsEhM8ocGP9mCzf03EuZtz6BAlc2OeHSGbhVi_OlSi4PyutwWROiHtzP_gy2UxYf78At1BcP3nUD0wqqQenW5dzwcKumKpkwQVnY5GiRFvakL0F6Ala2g8jKZhCoflosvqtmE34AzsV9hkmLlmfrzB4Bl8Agpzm4ZiKcOdk88D8Z75Ns9hprHDOCwZVAhgWJQ",
  },
  parameters:
  {
    design:
    {
      type: 'figma',
      url: 'https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=1140-7757&mode=design&t=W9FInuotqQY0fSA7-4',
    },
  },
  hideclose: true,
};
