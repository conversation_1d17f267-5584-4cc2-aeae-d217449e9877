<template>
  <div
    class="relative aspect-[16/9] !h-40 md:w-80 md:h-44 rounded-xl cursor-pointer md:max-w-[20rem] min-w-[11rem] "
    @click="onOpenProject()"
  >
    <img
      class="clip rounded-xl object-cover w-full h-full absolute border-2 border-r-zinc-100"
      :src="link"
      alt="Thumbnail"
    >

    <div
      class="w-full h-full absolute rounded-xl hiddenBg"
      style="background: linear-gradient(1deg, rgba(0, 0, 0, 0.83) 1.03%, rgba(0, 0, 0, 0.40) 55.79%, rgba(0, 0, 0, 0.00) 69.65%);"
      :class="{ 'hidden': isHovered }"
    />

    <p class="absolute bottom-2 text-center w-full text-lg font-bold text-white">
      {{ name }}
    </p>
    <div class="absolute flex items-center justify-center w-full h-full">
      <img
        class="h-16"
        :src="icon"
      >
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

defineProps({
  name: {type: String, default: ""},
  icon: {type: String, default: ""},
  mediaType: {type: String, default: ""},
  link: {type: String, default: ""},
  hideclose: Boolean,
});

const emit = defineEmits(['onClickClose', 'onOpenProject']);

function onOpenProject () {
  emit('onOpenProject');
}

</script>
