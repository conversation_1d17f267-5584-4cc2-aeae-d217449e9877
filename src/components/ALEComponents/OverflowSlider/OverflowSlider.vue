<script setup>

// *** Default Values as per the library

import { register } from 'swiper/element/bundle';
register(); // Initialize
import { defineEmits } from 'vue';
defineEmits(['swiper-slide-change']);
// Props, State and emits
const { slidesPerGroup, inlineWrapperStyles, inlineArrowStyles, initialSlide, verticalFixedHeight, inlineArrowWrapperStyles, inlineIndicatorsStyles, inlineIndicatorsWrapperStyles, pagination, arrows, spaceBetween, slidesPerView, direction, dynamicBullets, bulletsPerView} = defineProps({
  inlineWrapperStyles: {type: String, default: ""},
  inlineArrowStyles: {type: String, default: ""},
  inlineArrowWrapperStyles: {type: String, default: ""},
  inlineIndicatorsWrapperStyles: {type: String, default: ""},
  inlineIndicatorsStyles: {type: String, default: ""},
  pagination: Boolean,
  dynamicBullets: Boolean,
  bulletsPerView: Number,
  arrows: Boolean,
  spaceBetween: {type: Number, default: 0},
  slidesPerView: {type: String, default: ""},
  direction: {type: String, default: ""},
  slidesPerGroup: {type: String, default: ""},
  verticalFixedHeight: {type: String, default: ""},
  initialSlide: {type: Number, default: 0},
});

// Styles
const injectStyles =  [
  `
        /* --- Horizontal Navigation (Arrows) --- */


        .swiper-horizontal{
            ${inlineWrapperStyles}
        }

        .swiper-horizontal .swiper-button-next ,
        .swiper-horizontal .swiper-button-prev {
            ${inlineArrowWrapperStyles}
        }
        



        /* --- Vertical Navigation (Arrows) --- */

          .swiper-vertical {
                height: ${verticalFixedHeight}px ;
          }

         .swiper-vertical .swiper-button-next,
        .swiper-vertical .swiper-button-prev {
          left: 50%;
          transform: rotate(90deg);
          transform-origin: left center;
              ${inlineArrowWrapperStyles}
         }

        .swiper-vertical .swiper-button-prev {
          top: calc(var(--swiper-navigation-size) / 2);
        }

        .swiper-vertical .swiper-button-next {
          top: auto;
          bottom: calc(var(--swiper-navigation-size) / 2);
        }

         .swiper-button-prev svg , .swiper-button-next svg {
            ${inlineArrowStyles}
        }

        /* --- Pagination  --- */

        .swiper-pagination  {
          ${inlineIndicatorsWrapperStyles}
        }

        @media screen and (max-width: 1024px) {
          .swiper-pagination {
            //  width:138px !important;
            // max-width:10% !important;
            // min-width:10% important;
          }
        }
        @media screen and (max-width: 768px) {
          .swiper-pagination {
            // width:10% !important;
            // max-width:10% !important;
            // min-width:10% important;
          }
        }
        

         /* --- Customizing Pagination Bullets  --- */
        .swiper-pagination span[part="bullet"] , .swiper-pagination span[part="bullet-active"] {
            ${inlineIndicatorsStyles}
        }
            .swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next .swiper-pagination-bullet-active-next-next{
                  transform: scale(1);
            }
            .swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev .swiper-pagination-bullet-active-prev{
                  transform: scale(1);
            }
                  .swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev{
                     transform: scale(1);
                  }
                     .swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev{
                      transform: scale(1);
                     }
                      .swiper-pagination-bullets-dynamic .swiper-pagination-bullet{
                        transform: scale(1);
                      }
        
                        .swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic, .swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic{
                        transform:translateX(0%) !important;
                        left:0 !important;
                        }

      `,
];

</script>

<template>
  <swiper-container
    :ref="ref"
    :navigation="(arrows === true || arrows === 'true' ) && arrows"
    :pagination="(pagination === true || pagination === 'true') && { dynamicBullets: dynamicBullets,dynamicMainBullets:bulletsPerView}"
    :slides-per-view="slidesPerView"
    :space-between="spaceBetween"
    :injectStyles="injectStyles"
    :direction="(direction && direction)"
    :slides-per-group="slidesPerGroup"
    class="documentHeightAdjust"
    :initial-slide="initialSlide"
    @swiperslidechange="$emit('swiper-slide-change', $event)"
  >
    <slot name="options" />
  </swiper-container>
</template>
