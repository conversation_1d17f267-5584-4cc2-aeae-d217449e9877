import UnitPlanFavoriteView from './UnitPlanFavoriteView.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/UnitPlanFavoriteView',
  component: UnitPlanFavoriteView,
  tags: ['autodocs'],
  argTypes: {
  },
};

export const Primary = {
  args: {
    property: {
      'Tower A': {
        'name': 'Tower A',
        'type': '2',
        'measurement_type': 'Sqft',
        'measurement': 0,
        'min_measurement': 1056,
        'max_measurement': 1056,
        'bedrooms': 1,
        'bathrooms': 1,
        'is_furnished': 'true',
      },
      'Tower B': {
        'name': 'Tower B',
        'type': 'Studio',
        'measurement_type': 'Sqft',
        'measurement': 20000,
        'min_measurement': 0,
        'max_measurement': 0,
        'bedrooms': 1,
        'bathrooms': 1,
        'is_furnished': "true",
      },
    },
  },
};
