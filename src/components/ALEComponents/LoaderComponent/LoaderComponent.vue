<script setup>
import { ref, defineProps } from 'vue';

defineProps({logo: {type: String, default: ""}});
const progressWidth = ref(0);

function startProgress () {
  setInterval(() => {
    progressWidth.value += 1;
    if (progressWidth.value >= 100) {
      progressWidth.value = 0;

    }
  }, 10);
}
startProgress();
</script>
<template>
  <div class="fixed top-0 left-0 right-0 bottom-0 background_blur flex items-center justify-center">
    <div class="">
      <div class="w-[20.063rem] sm:w-[20.063rem] h-[3.175rem] loaderbody justify-center items-center inline-flex">
        <img
          v-if="logo"
          :src="logo"
        >
      </div>
      <div class="progress-bar">
        <div class="w-full h-2 bg-white bg-opacity-30 rounded-lg justify-start items-center inline-flex">
          <div
            :style="{ width: progressWidth + '%' }"
            class="h-2 relative bg-white rounded-lg"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<style>
.background_blur {background: rgba(0, 0, 0, 0.20);backdrop-filter: blur(37.5px);}
.loaderbody {height: 100%;}
.loaderIMG {margin-bottom: 28px;}
@media only screen and (max-width: 680px) {
    .loaderIMG {max-width: 300px;margin-bottom: 15px;}
}</style>
