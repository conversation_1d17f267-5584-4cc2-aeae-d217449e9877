import AmenityCard from './AmenityCard.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/AmenityCard',
  component: AmenityCard,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    name: "Club House",
    description: "Ride the tide of the best that life has to offer with the enchanting amenities of DAMAC Lagoons. Ride the tide of the best that life has to offer with the enchanting amenities of DAMAC Lagoons.",
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=105-2814&mode=design&t=MJoHYvcCnJNPKlHr-4",
    },
  },
};
