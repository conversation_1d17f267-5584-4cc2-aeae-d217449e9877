<script setup>
import { defineProps, defineEmits, ref } from 'vue';
import { cdn } from '../../../helpers/helper';
defineProps({
  name: {
    type: String,
    default: "",
  },
  description:
  {
    type: String,
    default: "",
  },
  cardSelectionRef: { type: String, default: "" },
  id: { type: String, default: "" },
  icon: { type: String, default: "" },
  mediaType: { type: String, default: "" },
  link: { type: String, default: "" },
  hideclose: Boolean,
});
const emit = defineEmits(['gotoTour']);
const circularloader = ref(true);
function gotoTour () {
  emit('gotoTour');
}

</script>

<template>
  <div
    v-if="description"
    class="relative cursor-pointer bg-secondary md:backdrop-blur-[14px] backdrop-blur-0 rounded-2xl text-secondaryText flex flex-col justify-start items-start slide-animation w-[250px] h-fit overflow-hidden border"
    :class="(cardSelectionRef === id) ? 'border-white' : 'border-gray-700' "
    @click="gotoTour"
  >
    <div class="w-full h-20 max-h-20 relative flex-0 flex justify-center items-center">
      <img
        class=" object-cover w-full h-full border-[white] border-solid border-[1px] md:border-none"
        :src="cdn(link)"
      >

      <span
        class="absolute flex bg-secondary cursor-pointer justify-start px-4 py-2.5 items-center rounded-lg whitespace-nowrap text-white text-sm font-medium leading-[21px]"
      >Explore</span>
    </div>

    <div class="hidden sm:flex p-4 w-full flex-1 h-fit flex-col justify-start items-start gap-2 ">
      <p
        class="w-full text-left text-base text-secondaryText font-medium whitespace-nowrap text-ellipsis overflow-hidden "
      >
        {{
          name }}
      </p>
      <p class="text-xs text-left w-full text-secondaryText font-normal text-wrap">
        {{ description }}
      </p>
    </div>
  </div>
  <div
    v-else
    class="relative cursor-pointer sm:!w-[12rem] sm:!h-[8rem] !w-[12rem] !h-[7rem] bg-secondary backdrop-blur-0 rounded-2xl text-secondaryText   flex flex-row items-center gap-4 slide-animation"
    :class="(cardSelectionRef === id) ? 'border-2 border-white' : '' "
    @click="gotoTour"
  >
    <div class="relative flex justify-center items-center h-full w-full overflow-hidden">
      <div class="gradient absolute bg-secondary h-full w-full rounded-2xl flex justify-center items-center z-20 ">
        <!-- <img
          v-if="icon"
          class="w-9 h-9"
          :src="icon"
        > -->
        <span class="w-9 h-9">
          <svg
            width="800px"
            height="800px"
            viewBox="0 0 108.782 108.782"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g transform="translate(-37.109 -210.359)">
              <path
                d="M144.391,264.75c0,13.468-23.68,24.386-52.891,24.386S38.609,278.218,38.609,264.75c0-7.3,6.949-13.843,17.962-18.312"
                fill="none"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="3"
              />
              <path
                d="M103.535,312.32c-3.635,3.408-7.719,5.321-12.035,5.321-15.187,0-27.5-23.68-27.5-52.891s12.312-52.891,27.5-52.891c7.372,0,14.066,5.579,19,14.662"
                fill="none"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="3"
              />
              <path
                d="M73.344,256.069h8.681v8.681H73.344"
                fill="none"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="3"
              />
              <path
                d="M73.344,264.75h8.681v8.681H73.344"
                fill="none"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="3"
              />
              <path
                d="M95.841,264.75H87.159v-8.681h8.681"
                fill="none"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="3"
              />
              <rect
                width="8.681"
                height="8.681"
                transform="translate(87.159 264.75)"
                stroke-width="3"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
                fill="none"
              />
              <path
                d="M109.656,264.75v8.681h-8.681V256.069h8.681Z"
                fill="none"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="3"
              />
              <circle
                cx="2.858"
                cy="2.858"
                r="2.858"
                transform="translate(115.249 256.069)"
                fill="none"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="3"
              />
              <path
                d="M49.662,244.042l6.909,2.4-2.4,6.909"
                fill="none"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="3"
              />
              <path
                d="M112.166,219.4l-1.662,7.121-7.121-1.662"
                fill="none"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="3"
              />
            </g>
          </svg>
        </span>

        <span
          class="absolute bottom-2 text-base font-medium text-secondaryText  sm:w-[11vh] w-[10rem] whitespace-nowrap text-ellipsis overflow-hidden text-center items-center"
        >{{
          name }}</span>
      </div>

      <div
        v-if="circularloader"
        class="z-10 absolute top-0 left-0 h-full w-full bg-secondary bg-opacity-40 backdrop-blur-[20px] flex rounded-2xl"
      >
        <div
          class="m-auto loader border-4 border-gray-200 border-t-4 border-t-black rounded-full w-5 h-5 sm:mb-10 mb-8 animate-spin"
        />
      </div>

      <img
        loading="lazy"
        class="bg-cover object-cover rounded-2xl !w-full !sm:h-[8rem] !h-full border-[white] md:border-none"
        :src="cdn(link)"
        alt=""
        @load="()=>circularloader=false"
      >
    </div>
  </div>
</template>

<style scoped>
.bgcolor{
  color: '#28334A'
}

.bgcolor:hover
{
  filter:saturate(1.6)
}

.gradient
{
  background: linear-gradient(180deg, rgba(2,0,36,0) 0%, rgba(28, 28, 28, 0.781) 77%, rgb(0, 0, 0) 100%);
}
</style>
