import SliderOption from './SliderOption.vue';

const sampleTemplate = `

  <template v-slot:icon>

  <svg width="28" height="28" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <rect x="0.25" y="0.25" width="17.5" height="17.5" rx="3.75" stroke="#262626" stroke-width="0.5"/>
  <mask id="mask0_1126_1929" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="3" y="3" width="12" height="12">
  <rect x="3" y="3" width="12" height="12" fill="url(#pattern0)"/>
  </mask>
  <g mask="url(#mask0_1126_1929)">
  <rect x="3" y="3" width="12" height="12" fill="#262626"/>
  </g>
  <defs>
  <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
  <use xlink:href="#image0_1126_1929" transform="scale(0.00195312)"/>
  </pattern>
  <image id="image0_1126_1929" width="512" height="512" xlink:href="data:image/png;base64,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"/>
  </defs>
  </svg>
  

  </template>

`;

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/SliderOption',
  component: SliderOption,
  tags: ['autodocs'],
};

const Template = (args) => ({
  components: { SliderOption },
  setup () {
    return { args };
  },
  template: `
    <SliderOption :title="args.title" :isArrowRotate="args.isArrowRotate" :theme="args.theme" >
        ${sampleTemplate}
    </SliderOption>
  `,
});

export const Primary = Template.bind({});

Primary.args = {
  title: "Sample",
  isArrowRotate: false,
  theme: "dark",
}; // Props
