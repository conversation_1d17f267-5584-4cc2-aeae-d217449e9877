<script setup>

/* State & Props & Emits */
const { title, isArrowRotate, theme} = defineProps({
  title: {type: String, default: ""},
  isArrowRotate: Boolean,
  theme: {type: String, default: ""},
});

const emits = defineEmits(['handleClick']);

</script>

<template>
  <button
    :class="[(theme.toLowerCase() === 'dark' ? 'bg-sliderOptionLightColor' : 'bg-sliderOptionDarkColor' )]"
    class="customSizing p-3 w-auto inline-flex justify-center items-center gap-2 bg-opacity-75 rounded-lg backdrop-blur-lg mb-4"
    @click="() => emits('handleClick',isArrowRotate)"
  >
    <slot
      class="svgCustomSizing"
      name="icon"
    />

    <div class="flex justify-between items-center gap-2 w-auto">
      <p
        :class="[(theme.toLowerCase() === 'dark' ? 'text-sliderOptionDarkColor' : 'text-sliderOptionLightColor' )]"
        class="overflow-hidden whitespace-nowrap max-w-[125px] text-left text-md font-normal text-ellipsis mb-0 select-none capitalize "
      >
        {{ title }}
      </p>

      <div class="svgCustomSizing w-4">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 12 6"
          :class="[(!isArrowRotate ? 'rotate-[180deg]' : 'rotate-[0deg]') , (theme.toLowerCase() === 'dark' ? 'fill-sliderOptionDarkColor' : 'fill-sliderOptionLightColor'), 'transition-all ']"
        >
          <path
            d="M0.837631 1.29554L5.50016 5.73872C5.55567 5.79146 5.6159 5.82884 5.68086 5.85085C5.74582 5.87321 5.81548 5.88459 5.88985 5.88497C5.96422 5.88535 6.03399 5.87469 6.09918 5.85299C6.16436 5.83164 6.22497 5.79488 6.28102 5.74272L11.0027 1.34752C11.1334 1.22579 11.1993 1.07313 11.2002 0.889546C11.2012 0.705958 11.1323 0.548241 10.9935 0.416394C10.8547 0.284546 10.6924 0.218147 10.5065 0.217196C10.3206 0.216246 10.1575 0.280981 10.0174 0.411403L5.89823 4.24579L1.81846 0.369481C1.68894 0.246424 1.52899 0.184408 1.33862 0.183434C1.14786 0.182459 0.982433 0.247182 0.842324 0.377603C0.702216 0.508025 0.631714 0.660658 0.63082 0.835504C0.629926 1.01035 0.698863 1.1637 0.837631 1.29554Z"
            fill=""
          />
        </svg>
      </div>
    </div>
  </button>
</template>

<style>

@media only screen and (max-width:360.98px){
        .customSizing{
             padding: 7px;
             gap: 4px;
        }

        .svgCustomSizing{
            width: 1px;
        }

}

</style>
