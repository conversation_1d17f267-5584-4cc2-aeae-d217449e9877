<script setup>
import { defineEmits, ref, watch, computed } from 'vue';

const props = defineProps({
  modelValue: Number,
  totalCountOfFrame: Number,
  firstValidSceneIndex: Number,
});
console.log(props.firstValidSceneIndex, "firstValidSceneIndex");

const emit = defineEmits(['update:modelValue', 'reset']);
const intervalId = ref(null);
const isPlaying = ref(false);
const isDragging = ref(false);

const sliderValue = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
});

watch(sliderValue, (newValue) => {
  emit('update:modelValue', newValue);
});

function increaseSliderValue () {
  if (sliderValue.value < props.totalCountOfFrame - 1) {
    sliderValue.value += 1;
    // console.log("Slider value if: " + sliderValue.value);
  } else {
    sliderValue.value = 0;
    // console.log("Slider value else: " + sliderValue.value);
  }
}

function startPlayback () {
  const totalAnimationTime = 9000; // 2 ms in microseconds
  const delayPerFrame = totalAnimationTime / (props.totalCountOfFrame - 1);

  if (intervalId.value) {
    return;
  }
  intervalId.value = setInterval(increaseSliderValue, delayPerFrame);
  isPlaying.value = true;
}

function stopPlayback () {
  if (intervalId.value) {
    clearInterval(intervalId.value);
    intervalId.value = null;
  }
  isPlaying.value = false;
}
function onInput () {
  isDragging.value = false;
  if (isPlaying.value) {
    stopPlayback();
  }
}
const togglePlayback = () => (isPlaying.value ? stopPlayback() : startPlayback());
// startPlayback();

function smoothReset (targetValue) {
  stopPlayback();
  const total = props.totalCountOfFrame;
  const current = sliderValue.value;
  const target = targetValue;

  if (current === target) {
    return;
  }

  // Calculate shortest direction
  const forwardSteps = (target - current + total) % total;
  const backwardSteps = (current - target + total) % total;
  const step = forwardSteps <= backwardSteps ? 1 : -1;

  const interval = 20;
  const animate = () => {
    if (sliderValue.value !== target) {
      sliderValue.value = (sliderValue.value + step + total) % total;
      setTimeout(animate, interval);
    }
  };
  animate();
}
</script>

<template>
  <div class="flex items-center gap-4">
    <div
      class="flex w-10 h-10 items-center cursor-pointer justify-center border border-none rounded-full bg-secondary"
      @click="togglePlayback"
    >
      <button>
        <svg
          v-if="!isPlaying"
          xmlns="http://www.w3.org/2000/svg"
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            d="M15.4137 13.059L10.6935 15.8458C9.93371 16.2944 9 15.7105 9 14.7868V9.21316C9 8.28947 9.93371 7.70561 10.6935 8.15419L15.4137 10.941C16.1954 11.4026 16.1954 12.5974 15.4137 13.059Z"
            fill="#1f2a37"
          />
        </svg>
        <svg
          v-if="isPlaying"
          xmlns="http://www.w3.org/2000/svg"
          width="14"
          height="14"
          viewBox="0 0 16 16"
          fill="none"
        >
          <path
            d="M4.79961 0H3.73294C2.55474 0 1.59961 0.89543 1.59961 2V14C1.59961 15.1046 2.55474 16 3.73294 16H4.79961C5.97782 16 6.93294 15.1046 6.93294 14V2C6.93294 0.89543 5.97782 0 4.79961 0Z"
            fill="#1f2a37"
          />
          <path
            d="M12.2663 0H11.1996C10.0214 0 9.06628 0.89543 9.06628 2V14C9.06628 15.1046 10.0214 16 11.1996 16H12.2663C13.4445 16 14.3996 15.1046 14.3996 14V2C14.3996 0.89543 13.4445 0 12.2663 0Z"
            fill="#1f2a37"
          />
        </svg>
      </button>
    </div>

    <input
      v-model="sliderValue"
      :min="0"
      :max="totalCountOfFrame - 1"
      type="range"
      class="w-44 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider"
      :style="{
        background: `linear-gradient(to right, #ffffff ${sliderValue * 100 / (totalCountOfFrame - 1)}%, #b4b4b4c9 ${sliderValue * 100 / (totalCountOfFrame - 1)}%)`,
      }"
      @input="onInput"
      @change="onDragEnd"
    >
    <div
      v-if="firstValidSceneIndex !== null"
      class="flex w-9 h-9 items-center justify-center border border-none rounded-full bg-secondary"
      @click="smoothReset(firstValidSceneIndex)"
    >
      <button>
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clip-path="url(#clip0_8711_4203)">
            <path
              d="M14.5179 7.2C14.0673 7.2 13.7031 7.5584 13.7031 8C13.7031 11.088 11.1437 13.6 7.99935 13.6C6.49357 13.6 5.06846 13.008 4.01572 12H6.36972C6.82031 12 7.18453 11.6416 7.18453 11.2C7.18453 10.7584 6.82031 10.4 6.36972 10.4H2.50179C2.34046 10.3672 2.17179 10.3864 2.01127 10.456C1.99661 10.4616 1.98113 10.464 1.96646 10.4704C1.95587 10.476 1.94446 10.4752 1.93387 10.4808C1.91187 10.4936 1.89965 10.5144 1.87846 10.528C1.64461 10.6696 1.48083 10.9112 1.48083 11.2V15.2C1.48083 15.6416 1.84505 16 2.29565 16C2.74624 16 3.11046 15.6416 3.11046 15.2V13.344C4.43861 14.5128 6.17009 15.2 7.99935 15.2C12.0433 15.2 15.3327 11.9696 15.3327 8C15.3327 7.5584 14.9685 7.2 14.5179 7.2Z"
              fill="#9CA3AF"
            />
            <path
              d="M2.29565 8C2.29565 4.912 4.85498 2.4 7.99935 2.4C9.50513 2.4 10.9302 2.992 11.983 4H9.62898C9.17839 4 8.81416 4.3584 8.81416 4.8C8.81416 5.2416 9.17839 5.6 9.62898 5.6H13.4953C13.6574 5.6336 13.8277 5.6136 13.9882 5.5432C14.0029 5.5376 14.0184 5.5352 14.0331 5.5288C14.0436 5.5232 14.0551 5.524 14.0656 5.5184C14.0876 5.5056 14.0999 5.4848 14.1211 5.4712C14.3541 5.3304 14.5179 5.0888 14.5179 4.8V0.8C14.5179 0.3584 14.1536 0 13.7031 0C13.2525 0 12.8882 0.3584 12.8882 0.8V2.656C11.5601 1.4872 9.82861 0.8 7.99935 0.8C3.95542 0.8 0.666016 4.0304 0.666016 8C0.666016 8.4416 1.03024 8.8 1.48083 8.8C1.93142 8.8 2.29565 8.4416 2.29565 8Z"
              fill="#9CA3AF"
            />
          </g>
          <defs>
            <clipPath id="clip0_8711_4203">
              <rect
                width="16"
                height="16"
                fill="white"
              />
            </clipPath>
          </defs>
        </svg>
      </button>
    </div>
  </div>
</template>

<style scoped>

.slider::-webkit-slider-thumb {
    -webkit-appearance: none; /* Override default appearance */
    appearance: none; /* Override default appearance */
    width: 18px; /* Width of the thumb */
    height: 18px; /* Height of the thumb */
    background: var(--primary); /* Thumb color */
    border-radius: 50%; /* Rounded thumb */
    cursor: pointer; /* Change cursor to pointer */
}
svg path{
  stroke:var(--secondaryText);
}

</style>
