<script setup>
import { onMounted, ref, nextTick } from 'vue';
import { FwbListGroup, FwbListGroupItem } from 'flowbite-vue';
import { creationToolStore } from '../../../store';

/* Props and Emits  */
const { numberOfFloors, perSlideView, defaultId, enabledFloors, floorsMobileViewToggle } = defineProps({
  enabledFloors: {
    type: Object,
    default () {
      return {};
    },
  },
  numberOfFloors: {
    type: Object,
    default () {
      return {};
    }},
  perSlideView: {type: Number, default: 0},
  defaultId: {type: String, default: ""},
  floorsMobileViewToggle: {type: String, default: ""},
}); // Props
const emits = defineEmits(['handleSelection', 'floorsMobileViewToggleEmit']); // Emits

const currentPage = ref(defaultId ? parseInt(defaultId) : 1);
const floors = ref([]);
const slideView = ref(perSlideView ? (perSlideView !== null || perSlideView !== undefined ? perSlideView :  4) : 4 ); // Per slide view
const nextCurrentIndex = ref(null);
const prevCurrentIndex = ref(null);
const heightRatio = ref(null);
const defaultIndex = ref(null); // Default index ref
const floorRef = ref(floorsMobileViewToggle);
const scrollableListRef = ref(null);
const Store = creationToolStore();

/* Methods */
// Const handlefloorswitchMobile = (val) => {
//   If (floorsMobileViewToggle){
//     If (floorswitchMobileEnableIds.value.includes(String(val))){
//       Object.values(layerData).forEach((item) => {
//         If (String(item.layer_data.floor_id) === String(val)){
//           MoveToNxtScene(item.layer_data.building_id, item.layer_data.layer_id, item.layer_data.floor_id, item.layer_data.scene_id);
//         }
//       });
//     } else {
//       ClickedId.value = false;
//       SceneId.value = false;
//       FloorId.value = false;
//       BuildingId.value = false;
//       ShowunitCard.value = false;
//     }
//   }
// };

const scrollToSelectedFloor = () => {
  nextTick(() => {
    const selectedFloorElement = document.querySelector(`.floor-item-${currentPage.value}`);
    if (selectedFloorElement && scrollableListRef.value) {
      scrollableListRef.value.scrollTop = selectedFloorElement.offsetTop - (scrollableListRef.value.clientHeight / 1) + (selectedFloorElement.clientHeight / 2.5);
    }
  });
};

const handleSelection = (val) => {
  currentPage.value = val;
  emits('handleSelection', val);
  scrollToSelectedFloor();
};

const floor = () => {
  floorRef.value = !floorRef.value;
  emits('floorsMobileViewToggleEmit', floorRef.value);
  scrollToSelectedFloor();
};

/* Hooks */
onMounted(() => {
  // Initialize
  if (numberOfFloors){
    floors.value = numberOfFloors;
    if (defaultId !== null && defaultId !== undefined && typeof defaultId === 'string'){
      Object.keys(numberOfFloors).forEach((item, index) => {
        if (defaultId === item){
          defaultIndex.value = index; // Default index value
          currentPage.value = defaultId;
          prevCurrentIndex.value = index; // Current previous index
          nextCurrentIndex.value = index + (slideView.value - 1); // Current next index
        }
      });
    } else {
      prevCurrentIndex.value = 0; // Current previous index
      nextCurrentIndex.value = 0 + (slideView.value - 1); // Current next index
    }
  }
  // Height Ratio
  heightRatio.value = (37 * slideView.value) + 8; // Height ratio
  nextTick(scrollToSelectedFloor);
});

</script>

<template>
  <div v-if="Store.isMobile">
    <button
      type="button"
      :class="[(!floorsMobileViewToggle ? 'rounded-bl-lg' : 'null'), 'flex flex-col justify-between gap-3 items-center bg-opacity-60 rounded-tl-lg backdrop-blur-[80px] w-[2.3rem] py-3 px-[0.56rem] bg-[#1F2A37CC] text-white']"
      @click="floor"
    >
      <span class="-rotate-90 py-3 px-[0.56rem]">
        Floor
      </span>
      <svg
        :class="[(floorsMobileViewToggle ? 'rotate-0' : 'rotate-180'), 'fill-white w-[6px] h-3 transition-transform duration-500 ease-[cubic-bezier(0.165, 0.84, 0.44, 1)]']"
        viewBox="0 0 6 12"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0.856844 12C0.63735 12 0.418064 11.9026 0.25109 11.7068C-0.0836966 11.316 -0.0836966 10.6827 0.25109 10.2921L3.93081 5.99927L0.25109 1.70762C-0.0836966 1.31679 -0.0836966 0.683538 0.25109 0.292945C0.585876 -0.0976483 1.12865 -0.0976483 1.46364 0.292945L5.74891 5.29254C6.0837 5.68313 6.0837 6.31638 5.74891 6.70722L1.46364 11.7068C1.29562 11.9026 1.07613 12 0.856844 12Z"
          fill=""
        />
      </svg>
    </button>

    <div
      v-if="floorsMobileViewToggle"
      ref="scrollableListRef"
      class="scrollable-list !h-[11.5rem]"
    >
      <FwbListGroup
        v-for="(newfloor) in numberOfFloors"
        :key="newfloor.floor_id"
        :active="currentPage === newfloor.floor_id"
        class="w-9 rounded-none !bg-[#1F2A37] bg-opacity-100 border-none"
        @click="enabledFloors.includes(String(newfloor.floor_id)) ? handleSelection(newfloor.floor_id) : null"
      >
        <FwbListGroupItem
          :class="[
            `floor-item-${newfloor.floor_id}`,
            currentPage === newfloor.floor_id ? 'text-white custom-gradient !border-white border-t-[2px] border-b-[2px]' : (
              newfloor.floor_id === parseInt(defaultId) ? 'border-none' : '!text-[#9CA3AF] border-none'
            ),
            !enabledFloors.includes(String(newfloor.floor_id)) ? '!text-[#4B5563] !pointer-events-none' : ''
          ]"
          class="items-center justify-center"
        >
          {{ newfloor.floor_id }}
        </FwbListGroupItem>
      </FwbListGroup>
    </div>
  </div>
</template>

<style lang="scss">

.custom-gradient {
  flex-shrink: 0;
  background: linear-gradient(to bottom, white -60%, transparent 50%, white 190%);
}

.floorswitchMobile > div {
  display: flex;
  flex-direction: column;
  background-color: #111928CC;
}

.floorswitchMobile > div > button:first-of-type {
  display: none;
}

.floorswitchMobile > div > button:last-of-type {
  display: none;
}

.bg-zinc-900 {
  background-color: #18181b;
}

.pointer-events-none {
  pointer-events: none;
}

.opacity-30 {
  opacity: 0.3;
}

.scrollable-list {
  max-height: 200px;
  overflow-y: auto;
}

</style>
