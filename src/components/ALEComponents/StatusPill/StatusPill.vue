<template>
  <!-- <div :class="getStatusClass(availabilityStatus)">
    <div
      class=" text-white text-base  font-medium text-center capitalize whitespace-nowrap"
      :class="Store.isMobile? 'text-sm' : ''"
    >
      {{ formatAvailabilityStatus(availabilityStatus) }} <span v-if="showAvailableUnits">{{ availableUnits ? `&#160;${formatSingleDigitNumber(availableUnits)}` : '' }}</span>
    </div>
  </div> -->
  <FwbBadge
    :type="getStatusClass(availabilityStatus)"
    class=" text-xs capitalize whitespace-nowrap"
    :class="Store.isMobile? 'text-sm' : ''"
  >
    <TranslationComp
      :text="formatAvailabilityStatus(availabilityStatus)"
    />

    <TranslationComp
      v-if="showAvailableUnits"
      :text="availableUnits ? `&#160;${formatSingleDigitNumber(availableUnits)}` : ''"
    />
  </FwbBadge>
</template>

<script setup>

import { defineProps } from 'vue';
import { creationToolStore } from '../../../store';
import { FwbBadge } from 'flowbite-vue';
import TranslationComp from '../TranslationComp/TranslationComp.vue';

const Store = creationToolStore();

defineProps({
  availabilityStatus: {type: String, default: ""},
  availableUnits: {type: Number, default: 0},
  showAvailableUnits: Boolean,
});

function getStatusClass (status) {
  if (!status){
    return 'default';
  }
  const statusColorMap = {
    available: { type: 'green' },
    sold: { type: 'red' },
    reserved: { type: 'indigo' },
    soldout: { type: 'red' },
    onreq: { type: 'indigo' },
  };

  const formatStatus = status.replace(/\s+/g, '').toLowerCase();

  const { type  } = statusColorMap[formatStatus] || { type: 'default' };

  return `${type}`;
}

function formatAvailabilityStatus (status){
  // Checks if the status is uppercased and converts to lowercase
  // If (status === status.toUpperCase()){
  //   Return status.toLowerCase();
  // }
  if (status === 'sold'){
    return 'Sold';
  }
  if (status === 'reserved'){
    return 'Booked';
  }
  if (status === 'soldOut') {
    return 'Sold Out';
  }
  if (status === 'onreq') {
    return 'Available On Request';
  }
  return status;

}

function formatSingleDigitNumber (number) {
  // Check if the number is less than 10
  if (number < 10) {
    // Pad the number with a leading zero if it's less than 10
    return `0${number}`;
  }
  // Return the original number as a string if it's 10 or greater
  return `${number}`;

}

</script>
