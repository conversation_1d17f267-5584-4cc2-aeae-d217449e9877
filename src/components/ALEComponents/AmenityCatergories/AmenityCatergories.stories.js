import AmenityCatergories from './AmenityCatergories.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/AmenityCatergories',
  component: AmenityCatergories,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    data: {
      "65ae73c47acabc2ec44f9fbf":
        {"_id": "65ae73c47acabc2ec44f9fbf",
          "project_id": "659f886821bd11d5d9218378",
          "name": "Gym ",
          "category": "indoor_amenities",
          "community_id": "null",
          "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/amenitiesthumbnail%2FRustomjee_cleon_thumbnail.jpg?alt=media",
          "media": [
            {"_id": "65ae73c77acabc2ec44f9fc3",
              "media_type": "360_image",
              "file": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2FFcxAht%2Fprojects%2F659f886821bd11d5d9218378%2Famenities%2F65ae73c47acabc2ec44f9fbf%2FRustomjee_cleon_amenity_gym_view_01.jpg?alt=media",
            },
          ],
          "__v": 1,
          "description": "Ride the tide of the best that life has to offer with the enchanting amenities of DAMAC Lagoons. Ride the tide of the best that life has to offer with the enchanting amenities of DAMAC Lagoons.",
        },
      "66029c6d1584e4822d2a630f":
        {"_id": "66029c6d1584e4822d2a630f", "project_id": "659f886821bd11d5d9218378", "name": "Swimming Pool", "category": "outdoor_amenities", "community_id": "65a10fb91d969d608f7fd1a2", "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2FFcxAht%2Fprojects%2F659f886821bd11d5d9218378%2Famenities%2F66029c6d1584e4822d2a630f%2F1710307528559-Daylight_03.jpg?alt=media", "media": [{"_id": "66029c6e1584e4822d2a6313", "media_type": "embed_link", "link": "https://showcase.propvr.tech/?m=zJ9Mimkmanb"}], "__v": 1},
      "3243242":
        {"_id": "3243242", "project_id": "659f886821bd11d5d9218378", "name": "Gym ", "category": "indoor_amenities", "community_id": "null", "thumbnail": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/amenitiesthumbnail%2FRustomjee_cleon_thumbnail.jpg?alt=media", "media": [{"_id": "65ae73c77acabc2ec44f9fc3", "media_type": "360_image", "file": "https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2FFcxAht%2Fprojects%2F659f886821bd11d5d9218378%2Famenities%2F65ae73c47acabc2ec44f9fbf%2FRustomjee_cleon_amenity_gym_view_01.jpg?alt=media"}], "__v": 1, "description": "Ride the tide of the best that life has to offer with the enchanting amenities of DAMAC Lagoons. Ride the tide of the best that life has to offer with the enchanting amenities of DAMAC Lagoons."},
    },
    categories: [{'category': 'indoor_amenities', 'count': '03'}, {'category': 'outdoor_amenities', 'count': '01'}],

  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/3ScwDHZPtk2FRiRLsSU2T2/ALE-V.2?type=design&node-id=93-10263&mode=dev",
    },
  },

};
