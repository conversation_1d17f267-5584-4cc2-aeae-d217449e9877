<script setup>
import { ref, computed } from "vue";
import { getCookie, thousandSeparator } from "../../../helpers/helper";
import StatusPill from "../StatusPill/StatusPill.vue";
// Import { GetCurrencySymbol } from "../../../helpers/API";

import { FwbButton } from 'flowbite-vue';
import { creationToolStore } from '../../../store';
import { useRoute } from 'vue-router';
import TranslationComp from "../TranslationComp/TranslationComp.vue";
import CurrencyComp from "../CurrencyComp/CurrencyComp.vue";
const {title, bedrooms, bathrooms, measurement, measurementType, buttonView, isShowButtons, status, hideEnterVr, type, community, price, maxPrice, currencyTitle, styleType, hideStatus, hideViewUnitplan, is_commercial, unitId} = defineProps({
  title: {type: String, default: ""},
  community: {type: String, default: ""},
  price: { type: Number, default: 0 },
  maxPrice: {type: Number, default: 0},
  currencyTitle: {type: String, default: ""},
  bedrooms: {type: Number, default: 0},
  bathrooms: {type: Number, default: 0},
  measurement: {type: Number, default: 0},
  measurementType: {type: String, default: ""},
  buttonView: {type: String, default: ""},
  status: {type: String, default: ""},
  isMobile: {type: Boolean, default: false},
  isShowButtons: {type: Boolean, default: false},
  isTouchScreen: {type: Boolean, default: false},
  hideClose: {type: Boolean, default: false},
  showFavIcon: {type: Boolean, default: false},
  unitId: {type: String, default: ""},
  favUnits: {type: Array, default: () => []},
  hideEnterVr: {type: Boolean, default: false},
  hideStatus: {type: Boolean, default: false},
  type: { type: String, default: null },
  styleType: { type: String, default: "" },
  hideViewUnitplan: {type: Boolean, default: false},
  is_commercial: {type: Boolean, default: false},
});

const getFormattedBedrooms = (bedrooms) => {
  const nonNumericTypes = [
    "studio",
    "penthouse",
    "townhouse",
    "plot",
    "duplex",
    "suite",
  ];
  if (nonNumericTypes.includes(bedrooms.toLowerCase())) {
    return bedrooms;
  }

  // Extract numeric bedrooms (e.g., "2BHK") and append "Bedroom"
  const match = bedrooms.match(/(\d+)BHK/i);
  return match ? `${match[1]} Bedroom` : bedrooms;
};

const translatedMeasurement = computed(() => {
  return measurementType === "sqmt" ? "Sq mt" : "Sq ft";
});

const Store = creationToolStore();

const route = useRoute();
const switchs = ref(false), showDetails = ref(false), showDefault = ref(), cardDetails = ref([]);
const selectedCurrencyTitle = ref(Store.currencyData.currency || getCookie('selectedCurrency'));
const emits = defineEmits(['showUnitplan', 'handleFavorite', 'closeModal', 'enterVR', 'bookUnit']);

if (Store.projectCardData[route.params.projectId]?.projectSettings.ale.unit_card_customize_type){
  cardDetails.value = Store.projectCardData[route.params.projectId].projectSettings.ale.unitcard_config;
} else {
  showDefault.value = true;
}

const closeModal = () => {
  switchs.value = !switchs.value;
  emits("closeModal");
};
// card drag functions
// const startDrag = (event) => {
//   if (event.type === "mousedown") {
//     startY.value = event.clientY;
//   } else if (event.type === "touchstart") {
//     startY.value = event.touches[0].clientY;
//   }
// };
// const endDrag = (event) => {
//   let endY;
//   if (event.type === "mouseup") {
//     endY = event.clientY;
//   } else if (event.type === "touchend") {
//     endY = event.changedTouches[0].clientY;
//   }
//   const dragDistance = endY - startY.value;
//   if (dragDistance < 0) {
//     showDetails.value = true;
//   } else if (dragDistance > 0) {
//     showDetails.value = false;
//   }
// };

const handleClick = () => {
  showDetails.value = !showDetails.value;
};

const handleExplore = () => {
  emits('enterVR');
};

const handleViewUnitPlan = () => {
  emits('showUnitplan');
};

</script>

<template>
  <div
    :class="['h-fit leading-tight rounded-2xl  bg-secondary',(isShowButtons ? ((buttonView.toLowerCase() === 'floorplanview' ? 'w-full sm:w-max border border-floorPlanCardBorder' : 'w-full sm:w-[26.56rem] ')) : Store.isLandscape ? 'w-[19.813rem]' : 'border-none  w-full sm:w-[18.813rem]' ), ' flex flex-col gap-4 justify-between items-center bg-blend-lighten relative z-50', isMobile ? ' flex flex-col w-screen absolute bottom-20 rounded-br-none rounded-bl-none rounded-t-[1.25rem] slide-animation': '',switchs?'!items-end  border-none !bottom-28':'']"
  >
    <div
      class="absolute bg-secondary sm:rounded-lg rounded-t-2xl rounded-b-auto rounded-b-none flex !items-center !justify-center backdrop-blur-[3.125rem] bottom-0 bg-opacity-60"
      :class="{
        ' w-[-webkit-fill-available] h-full !p-8 !m-0  ': !switchs,
        'w-9 h-9 p-0 mx-2 !rounded-full ': switchs,
      }"
      @click="closeModal()"
    >
      <div
        class="w-6 h-6"
        :class="{ hidden: !switchs, block: switchs }"
      >
        <svg
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8 15.5C6.51664 15.5 5.06659 15.0601 3.83323 14.236C2.59986 13.4119 1.63856 12.2406 1.07091 10.8701C0.50325 9.49968 0.354725 7.99168 0.644114 6.53683C0.933503 5.08197 1.64781 3.7456 2.6967 2.6967C3.7456 1.64781 5.08197 0.933503 6.53683 0.644114C7.99168 0.354725 9.49968 0.50325 10.8701 1.07091C12.2406 1.63856 13.4119 2.59986 14.236 3.83323C15.0601 5.06659 15.5 6.51664 15.5 8C15.4978 9.98845 14.7069 11.8948 13.3009 13.3009C11.8948 14.7069 9.98845 15.4978 8 15.5ZM8 2C6.81331 2 5.65328 2.3519 4.66658 3.01119C3.67989 3.67047 2.91085 4.60755 2.45673 5.7039C2.0026 6.80026 1.88378 8.00666 2.11529 9.17054C2.3468 10.3344 2.91825 11.4035 3.75736 12.2426C4.59648 13.0818 5.66558 13.6532 6.82946 13.8847C7.99335 14.1162 9.19975 13.9974 10.2961 13.5433C11.3925 13.0892 12.3295 12.3201 12.9888 11.3334C13.6481 10.3467 14 9.18669 14 8C13.9982 6.40925 13.3655 4.88417 12.2407 3.75934C11.1158 2.6345 9.59075 2.00179 8 2Z"
            fill="white"
          />
          <path
            d="M8 9.5C7.80109 9.5 7.61032 9.42098 7.46967 9.28033C7.32902 9.13968 7.25 8.94891 7.25 8.75V5C7.25 4.80109 7.32902 4.61032 7.46967 4.46967C7.61032 4.32902 7.80109 4.25 8 4.25C8.19891 4.25 8.38968 4.32902 8.53033 4.46967C8.67098 4.61032 8.75 4.80109 8.75 5V8.75C8.75 8.94891 8.67098 9.13968 8.53033 9.28033C8.38968 9.42098 8.19891 9.5 8 9.5Z"
            fill="white"
          />
          <path
            d="M8 11.75C8.41422 11.75 8.75 11.4142 8.75 11C8.75 10.5858 8.41422 10.25 8 10.25C7.58579 10.25 7.25 10.5858 7.25 11C7.25 11.4142 7.58579 11.75 8 11.75Z"
            fill="white"
          />
        </svg>
      </div>
    </div>

    <div
      v-if="!switchs"
      class="p-4 w-[-webkit-fill-available]"
    >
      <div
        class="w-full relative flex flex-col justify-start items-start sm:p-0"
        :class="Store.isMobile? (showDetails ? 'gap-4 pb-0' : 'gap-0 pb-0' ): (cardDetails.bedrooms || cardDetails.measurement || cardDetails.style ? 'gap-4 pb-4':'gap-0 pb-4' )"
      >
        <!-- Title -->
        <div
          v-if="Store.isMobile"
          class="flex w-full justify-center"
          @click="handleClick()"
        >
          <span class="w-[2.82rem] h-[0.32rem] rounded-[1.25rem] bg-secondaryText" />
        </div>
        <div
          class="flex items-center w-full p-0 justify-between"
          :class="'items-baseline'"
        >
          <div class="flex flex-col gap-2">
            <span
              v-if="!is_commercial"
              class="text-secondaryText font-semibold text-base leading-4 capitalize"
            >

              <TranslationComp
                :key="bedrooms"
                :text="getFormattedBedrooms(bedrooms)"
              />
              <TranslationComp
                v-show="type"
                :key="type"
                :text="type"
                class="ml-1"
              />
              <!-- {{ bedrooms != "studio" && bedrooms != "penthouse" && bedrooms != "townhouse" && bedrooms != "duplex" ? bedrooms.match(/(\d+)BHK/i)[1] : bedrooms === "studio" ? "Studio" : bedrooms === "penthouse" ? 'Penthouse' : bedrooms === "townhouse" ? 'Townhouse' : 'Duplex' }} {{ bedrooms != "studio" && bedrooms != "penthouse" && bedrooms != "townhouse" && bedrooms != "duplex" ?"Bedroom ":"" }}{{ type }} -->
            </span>
            <h2
              class="text-secondaryText w-max leading-[18px]"
              :class="is_commercial?'font-medium text-base ':'font-normal text-xs '"
            >
              <span>{{ text }}</span>

              <TranslationComp
                :key="title"
                :text="title"
              /><span v-if="community">, <TranslationComp
                :key="community"
                :text="community"
              /></span>
            </h2>
          </div>

          <div class="flex items-center gap-1">
            <StatusPill
              v-if="!hideStatus && (cardDetails.status || showDefault)"
              :availability-status="status"
              class="text-sm"
            />
            <button
              v-if="
                buttonView.toLowerCase() == 'unitplanview' || (buttonView.toLowerCase() == 'floorplanview' && isTouchScreen) ||buttonView.toLowerCase() == 'villaview'? true: isMobile || Store.isLandscape? true: false
              "
              class="h-8 w-8 p-2 bg-tertiary50opacity bg-opacity-60 rounded-full flex justify-start items-center gap-2"
              @click="
                () => {
                  emits('handleFavorite');
                }
              "
            >
              <svg
                class="w-[18px] h-4"
                viewBox="0 0 18 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9 14.6666C9 14.6666 1.5 10.4895 1.5 5.47693C1.5 0.464333 7.33333 0.0466183 9 3.96848C10.6667 0.0466183 16.5 0.464333 16.5 5.47693C16.5 10.4895 9 14.6666 9 14.6666Z"
                  :stroke="
                    Object.values(favUnits).some(
                      (innerObj) =>
                        (innerObj.unit_id && innerObj.unit_id === unitId) ||
                        (innerObj._id && innerObj._id === unitId) ||
                        unitId === true
                    )
                      ? '#FF4747'
                      : '#FFFFFF'
                  "
                  stroke-width="1.4"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  :fill="
                    Object.values(favUnits).some(
                      (innerObj) =>
                        (innerObj.unit_id && innerObj.unit_id === unitId) ||
                        (innerObj._id && innerObj._id === unitId)
                    )
                      ? '#FF4747'
                      : 'none'
                  "
                />
              </svg>
            </button>
            <!-- <div
              v-if="isMobile && hideClose === false && buttonView !== 'villaview'"
              class="h-5 w-5 "
              @click="closeModal()"
            >
              <svg
                class="w-[27px] h-27[px]"
                viewBox="0 0 27 27"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M20.25 20.25L6.75 6.75"
                  stroke="white"
                  stroke-width="1.575"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M20.25 6.75L6.75 20.25"
                  stroke="white"
                  stroke-width="1.575"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div> -->
          </div>
        </div>

        <!-- Price,sqft and bedrooms tiles -->
        <div
          v-if="!Store.isMobile || showDetails"
          :class="[(isShowButtons ? 'flex-row justify-start items-center' : 'flex-col justify-start items-start'),'w-full flex gap-3 flex-wrap']"
        >
          <div
            v-if="bedrooms || measurement"
            class="w-full grid grid-cols-2 justify-between items-center gap-5 flex-nowrap"
          >
            <!-- bedroom && bathroom -->
            <div
              v-if="bedrooms && (cardDetails.bedrooms || showDefault)"
              class="gap-2 flex items-center flex-nowrap"
            >
              <div class="   ">
                <svg
                  width="16"
                  height="17"
                  viewBox="0 0 16 17"
                  class="floorPlanSVG"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M14.6666 12.1666H1.33325"
                    stroke="#6B7280"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M14.6666 14.5V11.1667C14.6666 9.9096 14.6666 9.28107 14.2761 8.89053C13.8855 8.5 13.257 8.5 11.9999 8.5H3.99992C2.74284 8.5 2.1143 8.5 1.72378 8.89053C1.33325 9.28107 1.33325 9.9096 1.33325 11.1667V14.5"
                    stroke="#6B7280"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M14 8.5V5.40705C14 4.94595 14 4.71541 13.8719 4.49769C13.7438 4.27997 13.5613 4.16727 13.1963 3.94189C11.7246 3.03319 9.93287 2.5 8 2.5C6.06711 2.5 4.27543 3.03319 2.80372 3.94189C2.43869 4.16727 2.25618 4.27997 2.12809 4.49769C2 4.71541 2 4.94595 2 5.40705V8.5H14ZM7.33333 8V6.80893C7.33333 6.55515 7.2952 6.47027 7.0998 6.37025C6.693 6.16195 6.1991 6 5.66667 6C5.13423 6 4.64037 6.16195 4.2335 6.37025C4.03814 6.47027 4 6.55515 4 6.80893V8H7.33333ZM12.0001 6.80893V8H8.66675V6.80893C8.66675 6.55515 8.70488 6.47027 8.90028 6.37025C9.30708 6.16195 9.80095 6 10.3334 6C10.8659 6 11.3597 6.16195 11.7665 6.37025C11.9619 6.47027 12.0001 6.55515 12.0001 6.80893Z"
                    fill="#6B7280"
                  />
                  <rect
                    x="2"
                    y="9"
                    width="12"
                    height="3"
                    fill="#6B7280"
                  />
                </svg>
              </div>
              <p class="text-colormix text-sm font-normal leading-5 capitalize">
                <TranslationComp
                  :key="''+bedrooms+''"
                  :text="getFormattedBedrooms(bedrooms)"
                />
                <span v-if="bathrooms && (cardDetails.bathrooms || showDefault)"> +  <span>{{ bathrooms }} bath</span> </span>
              </p>  <!-- Allowing only the 3BHK's to have the "Bed" -->
            </div>

            <!-- measurement -->
            <div
              v-if="measurement && (cardDetails.measurement || showDefault)"
              class="gap-2 flex items-center flex-nowrap"
            >
              <div class="">
                <svg
                  class="w-6 h-6 floorPlanSVG"
                  viewBox="0 0 20 20"

                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M5.24458 6.42941C5.0388 6.62136 4.71638 6.61015 4.52443 6.40437L3.48866 5.29398C3.30564 5.09778 3.30614 4.79326 3.4898 4.59766L4.52557 3.4945C4.71819 3.28935 5.04065 3.27919 5.2458 3.47181C5.45095 3.66444 5.46111 3.98689 5.26849 4.19205L5.01286 4.4643H10.7728L10.5187 4.19183C10.3267 3.98606 10.3379 3.66363 10.5437 3.47168C10.7495 3.27973 11.0719 3.29094 11.2639 3.49672L12.2996 4.60711C12.4827 4.80332 12.4822 5.10783 12.2985 5.30344L11.2627 6.40659C11.0701 6.61174 10.7477 6.6219 10.5425 6.42928C10.3374 6.23666 10.3272 5.9142 10.5198 5.70905L10.7733 5.43906H5.01758L5.26962 5.70926C5.46157 5.91504 5.45036 6.23746 5.24458 6.42941ZM3.33301 8.91179C3.33301 8.19388 3.91499 7.6119 4.6329 7.6119H10.7816C11.4995 7.6119 12.0815 8.19388 12.0815 8.91179V15.0487C12.0815 15.7666 11.4995 16.3486 10.7816 16.3486H4.6329C3.91499 16.3486 3.33301 15.7666 3.33301 15.0487V8.91179ZM13.2826 8.45827C13.0768 8.65022 13.0656 8.97264 13.2575 9.17842C13.4495 9.3842 13.7719 9.39541 13.9777 9.20346L14.2503 8.94914V15.0045L13.9795 14.7503C13.7744 14.5577 13.4519 14.5678 13.2593 14.773C13.0667 14.9781 13.0768 15.3006 13.282 15.4932L14.3851 16.529C14.5808 16.7126 14.8853 16.7131 15.0815 16.5301L16.1919 15.4943C16.3976 15.3024 16.4089 14.98 16.2169 14.7742C16.025 14.5684 15.7025 14.5572 15.4967 14.7491L15.2251 15.0025V8.94899L15.4949 9.20233C15.7001 9.39495 16.0225 9.38479 16.2151 9.17964C16.4078 8.97448 16.3976 8.65203 16.1925 8.45941L15.0893 7.42364C14.8937 7.23998 14.5892 7.23948 14.393 7.4225L13.2826 8.45827Z"
                    fill="#CCCCCC"
                  />
                </svg>
              </div>

              <p class="text-colormix text-sm font-normal leading-5">
                {{ thousandSeparator(measurement) }}
                <TranslationComp
                  :key="measurement"
                  :text="thousandSeparator(translatedMeasurement)"
                />
              </p>
            </div>
          </div>

          <div
            v-if="type || style"
            class="w-full grid grid-cols-2 justify-between items-center gap-5 flex-nowrap"
          >
            <!-- type -->
            <div
              v-if="type && (cardDetails.type || showDefault)"
              class="gap-2 flex items-center flex-nowrap"
            >
              <div class="">
                <svg
                  width="20"
                  height="20"
                  class="floorPlanSVG"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="Frame 1000003889">
                    <g id="Group 1707479326">
                      <g id="Group">
                        <g id="Group_2">
                          <path
                            id="Vector"
                            d="M9.49621 9.22986V3.06689H4.87399C4.02659 3.06689 3.33325 3.76023 3.33325 4.60764V9.22986H9.49621ZM11.037 9.22986H17.1999V4.60764C17.1999 3.76023 16.5066 3.06689 15.6592 3.06689H11.037V9.22986ZM9.49621 10.7706H3.33325V15.3928C3.33325 16.2402 4.02659 16.9336 4.87399 16.9336H9.49621V10.7706ZM11.037 10.7706V16.9336H15.6592C16.5066 16.9336 17.1999 16.2402 17.1999 15.3928V10.7706H11.037Z"
                            fill="#9CA3AF"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                </svg>
              </div>

              <p class="text-colormix text-sm font-normal leading-2 capitalize">
                {{ type }}
              </p>
            </div>

            <!-- style -->
            <div
              v-if="style && (cardDetails.style || showDefault)"
              class="gap-2 flex items-center flex-nowrap"
            >
              <div class="   ">
                <svg
                  class="w-6 h-6 floorPlanSVG"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M5.24458 6.42941C5.0388 6.62136 4.71638 6.61015 4.52443 6.40437L3.48866 5.29398C3.30564 5.09778 3.30614 4.79326 3.4898 4.59766L4.52557 3.4945C4.71819 3.28935 5.04065 3.27919 5.2458 3.47181C5.45095 3.66444 5.46111 3.98689 5.26849 4.19205L5.01286 4.4643H10.7728L10.5187 4.19183C10.3267 3.98606 10.3379 3.66363 10.5437 3.47168C10.7495 3.27973 11.0719 3.29094 11.2639 3.49672L12.2996 4.60711C12.4827 4.80332 12.4822 5.10783 12.2985 5.30344L11.2627 6.40659C11.0701 6.61174 10.7477 6.6219 10.5425 6.42928C10.3374 6.23666 10.3272 5.9142 10.5198 5.70905L10.7733 5.43906H5.01758L5.26962 5.70926C5.46157 5.91504 5.45036 6.23746 5.24458 6.42941ZM3.33301 8.91179C3.33301 8.19388 3.91499 7.6119 4.6329 7.6119H10.7816C11.4995 7.6119 12.0815 8.19388 12.0815 8.91179V15.0487C12.0815 15.7666 11.4995 16.3486 10.7816 16.3486H4.6329C3.91499 16.3486 3.33301 15.7666 3.33301 15.0487V8.91179ZM13.2826 8.45827C13.0768 8.65022 13.0656 8.97264 13.2575 9.17842C13.4495 9.3842 13.7719 9.39541 13.9777 9.20346L14.2503 8.94914V15.0045L13.9795 14.7503C13.7744 14.5577 13.4519 14.5678 13.2593 14.773C13.0667 14.9781 13.0768 15.3006 13.282 15.4932L14.3851 16.529C14.5808 16.7126 14.8853 16.7131 15.0815 16.5301L16.1919 15.4943C16.3976 15.3024 16.4089 14.98 16.2169 14.7742C16.025 14.5684 15.7025 14.5572 15.4967 14.7491L15.2251 15.0025V8.94899L15.4949 9.20233C15.7001 9.39495 16.0225 9.38479 16.2151 9.17964C16.4078 8.97448 16.3976 8.65203 16.1925 8.45941L15.0893 7.42364C14.8937 7.23998 14.5892 7.23948 14.393 7.4225L13.2826 8.45827Z"
                    fill="#CCCCCC"
                  />
                </svg>
              </div>

              <p class="text-secondaryText text-sm font-normal leading-2 capitalize">
                {{ styleType }}
              </p>
            </div>
          </div>

          <!-- price -->
          <div
            v-if="price >= 1 && (cardDetails.price || showDefault)"
          >
            <div v-if="Store.isMobile">
              <!-- Showing Price Range  -->
              <p
                v-if="showDetails && maxPrice >= 1 && status==='available'"
                class=" text-base font-semibold not-italic leading-4"
              >
                <span class="uppercase !text-xs">
                  <TranslationComp
                    :key="currencyTitle"
                    :text="selectedCurrencyTitle ? selectedCurrencyTitle : currencyTitle"
                  /></span>
                <CurrencyComp
                  :price="price"
                  :unitCurrencyType="currencyTitle"
                /> -
                <span class="uppercase !text-xs"> <TranslationComp
                  :key="currencyTitle"
                  :text="selectedCurrencyTitle ? selectedCurrencyTitle : currencyTitle"
                /></span>
                <CurrencyComp
                  :price="maxPrice"
                  :unitCurrencyType="currencyTitle"
                />
              </p>
              <p
                v-else-if="status==='available'"
                class="text-base font-semibold not-italic leading-4 text-secondaryText"
              >
                <span class="uppercase !text-xs">
                  <TranslationComp
                    :key="currencyTitle"
                    :text="selectedCurrencyTitle ? selectedCurrencyTitle : currencyTitle"
                  /></span>
                <CurrencyComp
                  :price="price"
                  :unitCurrencyType="currencyTitle"
                />
              </p>
            </div>

            <div v-else>
              <!-- Showing Price Range  -->
              <p
                v-if="maxPrice >= 1"
                class="text-secondaryText text-sm font-semibold not-italic leading-4"
              >
                <span class="uppercase">
                  <TranslationComp
                    :key="currencyTitle"
                    :text="selectedCurrencyTitle ? selectedCurrencyTitle : currencyTitle"
                  />
                </span>
                <CurrencyComp
                  :price="price"
                  :unitCurrencyType="currencyTitle"
                  class="ml-1"
                /> -
                <span class="uppercase">
                  <TranslationComp
                    :key="currencyTitle"
                    :text="selectedCurrencyTitle ? selectedCurrencyTitle : currencyTitle"
                  />
                </span>
                <CurrencyComp
                  :price="maxPrice"
                  :unitCurrencyType="currencyTitle"
                  class="ml-1"
                />
              </p>
              <p
                v-else-if="status==='available'"
                class="text-secondaryText text-sm font-semibold not-italic leading-4"
              >
                <span class="uppercase">
                  <TranslationComp
                    :key="currencyTitle"
                    :text="selectedCurrencyTitle ? selectedCurrencyTitle : currencyTitle"
                  />
                </span>
                <CurrencyComp
                  :price="price"
                  :unitCurrencyType="currencyTitle"
                  class="ml-1"
                />
              </p>
            </div>
          </div>
        </div>

        <div
          v-if="isTouchScreen && !isMobile && isShowButtons"
          class="w-full"
        >
          <fwb-button
            class="w-full text-secondaryText"
            size="md"
            @click="emits('showUnitplan')"
          >
            <TranslationComp text="View Unit Plan" />
          </fwb-button>
        </div>
      </div>

      <div class="flex justify-around items-center gap-5 w-full mt-4">
        <fwb-button
          v-if="!hideEnterVr"
          :class="hideEnterVr ? 'w-full' : 'w-fit'"
          class="w-full z-10 bg-primary text-xs text-primaryText font-medium hover:bg-primary !ring-0"
          size="md"
          @click="handleExplore()"
        >
          <TranslationComp text="Explore Now" />
        </fwb-button>

        <fwb-button
          v-if="!hideViewUnitplan"
          :class="hideEnterVr ? 'w-full !bg-primary !text-primaryText  font-medium hover:bg-primary' : 'w-fit border-gray-600 border'"
          class="w-full z-10 !text-xs text-secondaryText bg-secondary hover:bg-secondary  !ring-0"
          size="md"
          @click="handleViewUnitPlan()"
        >
          <TranslationComp text="View Unit Plan" />
        </fwb-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
@media screen and (max-width: 430px) {
  .slide-animation {
    animation: popup 0.5s cubic-bezier(0.45, 0.05, 0.55, 0.95) forwards;
  }
}

.floorPlanSVG path,rect{
  stroke:var(--colormix);
  fill:var(--colormix) !important;
}

@keyframes popup {
  0% {
    bottom: -24em;
  }
  100% {
    @apply md:bottom-0;
  }
}
</style>
