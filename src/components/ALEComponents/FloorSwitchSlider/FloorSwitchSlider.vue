<script setup>
import { onMounted, ref} from 'vue';
import OverflowSlider from '../OverflowSlider/OverflowSlider.vue';
import { creationToolStore } from '../../../store';

const Store = creationToolStore();

const {numberOfFloors, defaultId, enabledFloors, perSlideView, activeIndicatorLine} = defineProps({
  enabledFloors: {
    type: Object,
    default () {
      return {};
    },
  },
  numberOfFloors: {
    type: Object,
    default () {
      return {};
    },
  },
  perSlideView: {type: Number, default: 0},
  defaultId: {type: String, default: ""},
  activeIndicatorLine: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['handleSelection']);
const floors = ref([]);
const slideView = ref(perSlideView ? (perSlideView !== null || perSlideView !== undefined  ? perSlideView :  5) : 7 ); // Per slide view
const heightRatio = ref(null);
const defaultIndex = ref(null);
const currentSelectionId = ref(null);
const dumpItems = ref(null);

if (slideView.value){
  let count = 1;
  const newArray = [];
  for (let i = count; i <= Number(String(slideView.value/2).split('.')[0]); i++){
    newArray.push(count);
    count++;
  }
  dumpItems.value = newArray;
}

/* Methods */

const handleCLick = (item) => {
  /* const captureIndex = val.detail[0].realIndex - dumpItems.value.length +  dumpItems.value.length ;
  floors.value.forEach((floor, index) => {
    if (index === captureIndex){
      emit('handleSelection', floor.floor_id, floor);
      currentSelectionId.value = floor.floor_id;
    }
  }); */

  emit('handleSelection', item.floor_id, item); // Emits
  currentSelectionId.value = item.floor_id; // Current value set
};

/* Hooks */
onMounted(() => {
  // Initialize
  if (numberOfFloors){
    floors.value = numberOfFloors.filter((floor) => floor.floor_id !== '0');
    if (defaultId !== null && defaultId !== undefined && typeof defaultId === 'string'){
      floors.value.forEach((floor, index) => {

        if (defaultId === floor.floor_id){
          defaultIndex.value = index; // Default index value
          currentSelectionId.value = floor.floor_id ; // Current selection id
        }
      });
    }  else {
      defaultIndex.value = 0;
    //  currentSelectionId.value = 1;
    }
  }
  // Height Ratio
  heightRatio.value = (47 * slideView.value) + 0; // Height ratio
});

</script>

<template>
  <div
    v-if="Object.keys(floors).length > 0 && slideView > 0 "
    class="w-12 bg-secondary bg-opacity-100  backdrop-blur-[80px]"
    :class="Store.isLandscape? 'rounded-t-lg':'rounded-bl-lg'"
  >
    <OverflowSlider
      id="overflowFloorSwitchSliderRef"
      direction="vertical"
      :slides-per-view="slideView"
      space-between="0"
      :vertical-fixed-height="heightRatio"
      class="bg-transparent "
      :initial-slide="defaultIndex"
      :mousewheel="true"
    >
      <template #options>
        <swiper-slide
          v-for="item in dumpItems"
          :key="item"
        />

        <swiper-slide
          v-for="item in floors"
          :key="item.floor_id"
          :class="['flex flex-col justify-center items-center gap-4', !enabledFloors.includes(String(item.floor_id)) ? '!pointer-events-none' : '']"
          @click="(e) => handleCLick(item)"
        >
          <p
            :class="[' text-sm font-semibold',( enabledFloors.length > 0 && enabledFloors.includes(String(currentSelectionId)) && String(currentSelectionId ) == item.floor_id ? 'text-primary' : 'text-secondaryText'), !enabledFloors.includes(String(item.floor_id)) ? '!text-secondaryText opacity-25' : '']"
            class="capitalize"
          >
            {{ item.floor_id }}
          </p>
        </swiper-slide>

        <swiper-slide
          v-for="item in dumpItems"
          :key="item"
        />
      </template>
      <swiper-slide />
      <swiper-slide />
    </OverflowSlider>

    <span
      v-if="activeIndicatorLine"
      class="h-10 absolute right-[0px] top-[48.7%] translate-y-[-50%] customBg  border-t-2 border-white border-b-2"
      :class="Store.isLandscape ? 'w-full' : 'w-9'"
    />
  </div>
</template>

<style scoped>
.customBg{
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 53.5%, rgba(255, 255, 255, 0.20) 100%);
}
</style>
