<script setup>
/* Props & State */
const { title, fixedWidth, border } = defineProps({
  title: { type: String, default: "" },
  theme: { type: String, default: "" },
  fixedWidth: Boolean,
  border: Boolean,
});
// Const props = defineProps(['title'])
const emit = defineEmits(['goTo']);

function goTo () {
  emit('goTo');
}

</script>

<template>
  <button
    class="rounded-[2em]"
    :class="[(border === 'borderStyle' ? 'border-white' : 'border-0'), (fixedWidth ? `w-full md:w-64 lg:w-72` : 'w-fit px-4'),'flex justify-center items-center gap-2  py-2.5  border flex-nowrap']"
    @click="goTo"
  >
    <span
      v-if="$slots.svg"
      class="w-5 h-5 flex justify-center items-center"
    >

      <slot name="svg" />
    </span>

    <span
      class="block whitespace-nowrap font-medium"
    >
      {{ title }}
    </span>
  </button>
</template>

<style scoped>
.bg-primary:hover {
  filter: saturate(1.6)
}

/* .bg-secondary {
  border: 1px solid #ffffff;
}

.bg-secondary:hover {
  filter: contrast(0.9);
} */
</style>
