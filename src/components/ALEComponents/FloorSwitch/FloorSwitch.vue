<script setup>
import { onMounted, ref } from 'vue';
import OverflowSlider from '../OverflowSlider/OverflowSlider.vue';
import TranslationComp from '../TranslationComp/TranslationComp.vue';

/* Props and Emits  */
const { numberOfFloors, perSlideView, defaultId, enabledFloors } = defineProps({
  enabledFloors: {
    type: Object,
    default () {
      return {};
    },
  },
  numberOfFloors: {
    type: Object,
    default () {
      return {};
    }},
  perSlideView: {type: Number, default: 0},
  defaultId: {type: String, default: ""},
}); // Props
const emits = defineEmits(['handleSelection']); // Emits

const floors = ref([]);
const currentSelectionId = ref(null); // Current selection id
const slideView = ref(perSlideView ? (perSlideView !== null || perSlideView !== undefined  ? perSlideView :  3) : 3 ); // Per slide view
const nextCurrentIndex = ref(null);
const prevCurrentIndex = ref(null);
const heightRatio = ref(null);
const defaultIndex = ref(null); // Default index ref

/* Methods */
// Fast Prev
const handleFastPrev = () => {
  if (prevCurrentIndex.value >= slideView.value){
    const calcIndex = prevCurrentIndex.value - slideView.value; // No negative
    document.getElementById('overflowSliderRef').swiper.slideTo(Number(calcIndex));// Index 0
  } else {
    document.getElementById('overflowSliderRef').swiper.slideTo(0); // Previous
  }
};
// Prev
const handlePrev = () => {
  document.getElementById('overflowSliderRef').swiper.slidePrev(); // Previous
};
// Next Fast
const handleFastNext = () => {
  if ((nextCurrentIndex.value <= (Object.keys(floors.value).length - slideView.value))){
    const calculate = nextCurrentIndex.value + 1; // No negative
    document.getElementById('overflowSliderRef').swiper.slideTo(Number(calculate));
  } else {
    document.getElementById('overflowSliderRef').swiper.slideTo(Object.keys(floors.value).length);
  }
};
// Next
const handleNext = () => {
  document.getElementById('overflowSliderRef').swiper.slideNext(); // Next
};
// Swiper Change
const handleSwiperSlideChange = (val) => {
  /*    Console.log("I'm Changing");
                console.log("-------------------------------");
                console.log("------ Real Index ----------------");
                console.log(val.detail[0].realIndex);
                console.log("------ Previous Index ----------------");
                console.log(val.detail[0].previousRealIndex );
                console.log("-------------------------------"); */
  prevCurrentIndex.value = val.detail[0].realIndex; // Current previous index
  nextCurrentIndex.value =val.detail[0].realIndex + (slideView.value -1 ); // Current next index
};
const handleSelection = (val) => {
  currentSelectionId.value = val;
  emits('handleSelection', val); // Emits
};

/* Hooks */
onMounted(() => {
  // Initialize
  if (numberOfFloors) {
  // Filter out the floor with floor_id '0' from the array
    floors.value = numberOfFloors.filter((floor) => floor.floor_id !== '0');
    if (defaultId !== null && defaultId !== undefined && typeof defaultId === 'string') {
    // Find the floor that matches the defaultId
      floors.value.forEach((floor, index) => {
        if (defaultId === floor.floor_id) {
          defaultIndex.value = index; // Default index value
          currentSelectionId.value = defaultId; // Id
          prevCurrentIndex.value = index; // Current previous index
          nextCurrentIndex.value = index + (slideView.value -1 ); // Current next index
        }
      });
    } else {
      prevCurrentIndex.value = 0; // Current previous index
      nextCurrentIndex.value = 0 + (slideView.value -1 ); // Current next index
    }
  }
  // Height Ratio
  heightRatio.value = (37 * slideView.value) + 25; // Height ratio
});

</script>
<template>
  <div
    v-if="Object.keys(floors).length > 0 && slideView > 0 "
    class="w-[4.8rem] h-max p-3 flex flex-col gap-2 justify-center items-center bg-secondary bg-opacity-40 rounded-lg border border-floorSwitchBorderColor border-opacity-40 backdrop-blur-2xl absolute top-[50%] right-8 translate-y-[-50%] z-[1]  md:flex"
  >
    <h2 class="text-secondaryText text-base font-medium">
      <TranslationComp
        text="Floor"
      />
    </h2>
    <div class="flex flex-col gap-2 justify-center items-center px-3">
      <div class="flex flex-col justify-between items-center gap-2">
        <button
          class=" p-2 bg-tertiary50opacity themesvg-secondary-fill bg-opacity-75  shadow-inner border border-floorSwitchArrowsBorderColor backdrop-blur-[7.50px] justify-center items-center inline-flex disabled:opacity-40 border-none   rounded-full  cursor-pointer h-10 w-10"
          :disabled="( Object.keys(floors).length > slideView && slideView > 1 ) ? false : true"
          @click="handleFastPrev"
        >
          <svg
            class="w-8 h-8"
            viewBox="0 0 16 16"
            fill="white"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M4 11.727L4.94 12.667L8 9.61366L11.06 12.667L12 11.727L8 7.72699L4 11.727Z"
              fill="#D8DBDF"
            />
            <path
              d="M4 7.33344L4.94 8.27344L8 5.2201L11.06 8.27344L12 7.33344L8 3.33344L4 7.33344Z"
              fill="#D8DBDF"
            />
          </svg>
        </button>
        <button
          class="w-10 h-10 p-2 bg-tertiary50opacity  bg-opacity-75 rounded-full shadow-inner border border-floorSwitchArrowsBorderColor backdrop-blur-[7.50px] justify-center items-center inline-flex disabled:opacity-40 border-none cursor-pointer"
          :disabled="Object.keys(floors).length > slideView ? false : true"
          @click="handlePrev"
        >
          <svg
            class="w-8 h-8"
            viewBox="0 0 16 16"
            fill="white"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M4 10.06L4.94 11L8 7.94667L11.06 11L12 10.06L8 6.06L4 10.06Z"
              fill="#D8DBDF"
            />
          </svg>
        </button>
      </div>
      <OverflowSlider
        id="overflowSliderRef"
        direction="vertical"
        :slides-per-view="slideView"
        space-between="8"
        :vertical-fixed-height="heightRatio"
        :initial-slide="defaultIndex"
        @swiper-slide-change="handleSwiperSlideChange"
      >
        <template #options>
          <swiper-slide
            v-for="item in floors"
            :key="item.floor_id"
            :class="[
              (currentSelectionId === item.floor_id ? 'bg-primary text-primaryText ' : 'text-secondaryText hover:text-secondaryText bg-tertiary50opacity'),
              'aspect-square bg-opacity-75 rounded-[50%] shadow-inner backdrop-blur-[7.50px] justify-center items-center inline-flex cursor-pointer ',
              enabledFloors.includes(item.floor_id) ? 'cursor-pointer' : 'bg-red-900 pointer-events-none opacity-30'
            ]"
            class="check w-10 !h-10"
            @click="handleSelection(item.floor_id)"
          >
            <p class="text-inherit text-base font-medium text-center uppercase">
              {{ item.floor_id }}
            </p>
          </swiper-slide>
        </template>
      </OverflowSlider>

      <div class="flex flex-col justify-between items-center gap-2">
        <button
          class="w-10 h-10 p-2 bg-tertiary50opacity themesvg-secondary-fill bg-opacity-75 rounded-[28.50px] shadow-inner border border-floorSwitchArrowsBorderColor backdrop-blur-[7.50px] justify-center items-center inline-flex  disabled:opacity-40 border-none cursor-pointer"
          :disabled="Object.keys(floors).length > slideView ? false : true"
          @click="handleNext"
        >
          <svg
            class="w-8 h-8"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 6.94L11.06 6L8 9.05333L4.94 6L4 6.94L8 10.94L12 6.94Z"
              fill="#D8DBDF"
            />
          </svg>
        </button>
        <button
          class="w-10 h-10 p-2 bg-tertiary50opacity themesvg-secondary-fill bg-opacity-75 rounded-[28.50px] shadow-inner border border-floorSwitchArrowsBorderColor backdrop-blur-[7.50px] justify-center items-center inline-flex disabled:opacity-40 border-none cursor-pointer"
          :disabled="Object.keys(floors).length > slideView && slideView > 1 ? false : true"
          @click="handleFastNext"
        >
          <svg
            class="w-8 h-8"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 4.27301L11.06 3.33301L8 6.38634L4.94 3.33301L4 4.27301L8 8.27301L12 4.27301Z"
              fill="#D8DBDF"
            />
            <path
              d="M12 8.66656L11.06 7.72656L8 10.7799L4.94 7.72656L4 8.66656L8 12.6666L12 8.66656Z"
              fill="#D8DBDF"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
svg path{
  fill:var(--secondaryText)
}
</style>
