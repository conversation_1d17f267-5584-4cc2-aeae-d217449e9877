import BottomNavigation from './BottomNavigation.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/BottomNavigation',
  component: BottomNavigation,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    'Navigator': {
      "Exterior": false,
      "Interior": true,
      "Floorplan": true,
    },
    'activeTab': 'Floorplan',
  },
};

export const Secondary = {
  args: {

  },
};

export const Large = {
  args: {

  },
};

export const Small = {
  args: {

  },
};
