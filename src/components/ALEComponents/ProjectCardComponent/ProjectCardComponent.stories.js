import ProjectCardComponent from './ProjectCardComponent.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/ProjectCardComponent',
  component: ProjectCardComponent,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    name: 'Prestige Park Grove',
    units: 12,
    amenities: 15,
    floor: 26,
    location: "Bangalore",
    id: "####",
    thumbnail: "https://images.adsttc.com/media/images/649c/670f/5921/183e/3b97/89cc/medium_jpg/london-architecture-city-guide-20-modern-and-contemporary-attractions-to-explore-in-uks-cultural-and-financial-powerhouse_1.jpg?1687971619",
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=1125-2323&mode=dev",
    },
  },
};
