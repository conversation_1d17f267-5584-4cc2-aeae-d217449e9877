<script setup>
import {  defineEmits } from 'vue';
const emit = defineEmits(['handleleftarrow', 'handlerightarrow']);
</script>

<template>
  <div>
    <nav aria-label="Page navigation example">
      <ul class="flex items-center -space-x-px !h-12 text-base">
        <li>
          <a
            href="#"
            class="flex items-center justify-center px-5 h-14 ms-0 leading-tight text-gray-500 border border-e-0 rounded-s-lg border-[#374151] bg-[#1F2A37]"
            color="light"
            pill
            square
            @click="emit('handleleftarrow')"
          >
            <span class="sr-only">Previous</span>
            <svg
              class="w-3 h-3 rtl:rotate-180"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 6 10"
            >
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M5 1 1 5l4 4"
              />
            </svg>
          </a>
        </li>
        <li>
          <a
            href="#"
            class="flex items-center justify-center px-6 h-14 leading-tight border bg-[#374151] border-[#374151]"
          >
            <svg
              class="w-6"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clip-path="url(#clip0_479_3803)">
                <path
                  d="M10.2096 12.2802C11.1215 12.2802 11.7953 12.3165 12.6347 12.3846M14.6407 12.6197C17.8267 13.1178 20 14.1129 20 15.2592C20 16.9045 15.5228 18.2382 10 18.2382C4.47715 18.2382 0 16.9045 0 15.2592C0 14.4541 1.07217 13.7235 2.81437 13.1874M6.27245 12.494C5.70048 12.5625 5.15467 12.6463 4.64072 12.7436"
                  stroke="white"
                  stroke-width="0.486111"
                />
                <g filter="url(#filter0_d_479_3803)">
                  <path
                    d="M16.0022 10.8583C14.6334 10.8583 13.5198 9.74281 13.5198 8.37182V5.9751C13.6565 2.67572 18.3492 2.67826 18.4848 5.9751V8.37185C18.4848 9.74282 17.3711 10.8583 16.0022 10.8583ZM16.0022 4.77035C15.3399 4.77035 14.801 5.31079 14.801 5.9751V8.37185C14.8673 9.97054 17.1378 9.96928 17.2034 8.37185V5.9751C17.2034 5.31079 16.6646 4.77035 16.0022 4.77035ZM12.3665 8.36522C12.3753 6.49812 10.2961 5.28334 8.68285 6.20273V5.9751C8.68285 5.31083 9.22169 4.77035 9.88405 4.77035C10.5522 4.77035 11.0436 5.11489 11.0486 5.11841C11.4587 5.4414 12.0834 5.1369 12.0783 4.61011C12.0783 4.36621 11.9421 4.15422 11.7415 4.04592C11.5136 3.89317 10.8215 3.48865 9.88405 3.48865C8.51518 3.48865 7.40154 4.60409 7.40154 5.97508V8.37819C7.40154 8.38037 7.40154 8.38261 7.40158 8.38479C7.55283 11.6671 12.2165 11.6646 12.3665 8.38479C12.3665 8.38261 12.3666 8.38037 12.3666 8.37819V8.37182C12.3666 8.36964 12.3665 8.36742 12.3665 8.36522ZM9.88405 9.57658C9.22275 9.57658 8.68454 9.0379 8.68285 8.37502C8.75255 6.78054 11.0162 6.78175 11.0853 8.37502C11.0835 9.03786 10.5453 9.57658 9.88405 9.57658ZM5.48675 7.19515C5.89804 6.81886 6.18432 6.29882 6.18432 5.71391C6.18432 4.48694 5.18275 3.48872 3.95166 3.48872C3.02957 3.48872 2.37729 4.02945 2.30596 4.0911L2.30734 4.0927C2.17088 4.21022 2.08417 4.38389 2.08417 4.57813C2.08186 5.13056 2.74833 5.42304 3.15194 5.05478C3.21994 5.003 3.54795 4.77037 3.95166 4.77037C4.47622 4.77037 4.90302 5.19365 4.90302 5.71391C4.90302 6.14999 4.34399 6.53267 4.00611 6.53267C3.65228 6.53267 3.36546 6.81957 3.36546 7.17349C3.36546 7.52743 3.65228 7.81433 4.00611 7.81433C4.10038 7.81433 4.3394 7.89322 4.57946 8.10491C4.77601 8.27819 4.90302 8.48553 4.90302 8.63305C4.90302 9.15331 4.47625 9.57658 3.95166 9.57658C3.54798 9.57658 3.21997 9.34396 3.15194 9.29218C2.74807 8.92386 2.08186 9.2165 2.08413 9.76883C2.08413 9.96307 2.17085 10.1367 2.3073 10.2543L2.30593 10.2559C2.37726 10.3175 3.02954 10.8583 3.95162 10.8583C5.18273 10.8583 6.18428 9.86002 6.18428 8.63305C6.18432 8.09117 5.89477 7.57837 5.48675 7.19515ZM7.8508 15.9529C7.82348 15.9529 7.8786 15.9565 7.8508 15.9529V15.9529ZM17.684 2.84785C18.1263 2.84785 18.4848 2.48921 18.4848 2.04681C18.4426 0.984251 16.9252 0.98454 16.8832 2.04681C16.8832 2.48921 17.2417 2.84785 17.684 2.84785ZM12.8462 15.816C12.8169 15.82 12.8751 15.816 12.8462 15.816V15.816Z"
                    fill="white"
                  />
                </g>
              </g>
              <defs>
                <filter
                  id="filter0_d_479_3803"
                  x="-1.2814"
                  y="1.25"
                  width="23.1312"
                  height="21.4352"
                  filterUnits="userSpaceOnUse"
                  color-interpolation-filters="sRGB"
                >
                  <feFlood
                    flood-opacity="0"
                    result="BackgroundImageFix"
                  />
                  <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  />
                  <feOffset dy="3.36539" />
                  <feGaussianBlur stdDeviation="1.68269" />
                  <feComposite
                    in2="hardAlpha"
                    operator="out"
                  />
                  <feColorMatrix
                    type="matrix"
                    values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.06 0"
                  />
                  <feBlend
                    mode="normal"
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_479_3803"
                  />
                  <feBlend
                    mode="normal"
                    in="SourceGraphic"
                    in2="effect1_dropShadow_479_3803"
                    result="shape"
                  />
                </filter>
                <clipPath id="clip0_479_3803">
                  <rect
                    width="20"
                    height="20"
                    fill="white"
                  />
                </clipPath>
              </defs>
            </svg>
          </a>
        </li>
        <li>
          <a
            href="#"
            class="flex items-center justify-center px-5 h-14 leading-tight text-gray-500 border rounded-e-lg border-e-0 border-[#374151] bg-[#1F2A37]"
            color="light"
            pill
            square
            @click="emit('handlerightarrow')"
          >
            <span class="sr-only">Next</span>
            <svg
              class="w-3 h-3 rtl:rotate-180"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 6 10"
            >
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="m1 9 4-4-4-4"
              />
            </svg>
          </a>
        </li>
      </ul>
    </nav>
  </div>
</template>
