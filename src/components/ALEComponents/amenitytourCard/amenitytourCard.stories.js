import amenitytourCard from "./amenitytourCard.vue";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: "Design System/ALE/amenitytourCard",
  component: amenitytourCard,
  tags: ["autodocs"],
  argTypes: {},
};

export const Primary = {
  args: {},
  parameters:
  {
    design:
    {
      type: 'figma',
      url: 'https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=629-9396&mode=design&t=MJoHYvcCnJNPKlHr-4',
    },
  },
};
