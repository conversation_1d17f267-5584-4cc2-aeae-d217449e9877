import NearByFloatingButton from './NearByFloatingButton.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/NearByFloatingButton',
  component: NearByFloatingButton,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    category:
      ['hotel', 'hospital', 'park', 'Health', 'Landmarks'],
  },
  parameters:
  {
    design:
    {
      type: "figma",
      url: "https://www.figma.com/file/VkmPdhAqPV0a37NbUglcZx/PropVR-web-product-V.2?type=design&node-id=1140-888&mode=dev",

    },
  },
};
