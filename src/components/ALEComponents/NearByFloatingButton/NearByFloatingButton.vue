<script setup>
import { ref, watch, defineProps, onMounted, computed } from 'vue';
import { FwbButton, FwbCard } from 'flowbite-vue';
import OverflowSlider from '../OverflowSlider/OverflowSlider.vue';
import { creationToolStore } from '../../../store';
import { useRoute } from 'vue-router';
import TranslationComp from '../TranslationComp/TranslationComp.vue';
import PictureCard from '../PictureCard/PictureCard.vue';
import { getUnitType, getBedroomRange, cdn } from '../../../helpers/helper';
import TranslationTest from '../TranslationComp/TranslationTest.vue';

const route = useRoute();
const isAtEnd = ref(false);
const props = defineProps(
  {
    itemsList: {
      type: Array,
      default () {
        return [];
      },
    },
    active: {type: Number, default: 0},
    sliderButton: {type: Boolean, default: false},
    leftButton: {type: String, default: ""},
    rightButton: {type: String, default: ""},
    objectNameKey: {type: String, default: ''},
    objectIconKey: {type: String, default: ''},
    amenityCategory: {type: String, default: ''},
    projectsCategory: {type: Boolean, default: false},
  },
);
const emit = defineEmits(['buttonClicked', 'gotoTour', 'gotoProject']);
const activeIndex = ref(props.active);
// Ref to Swiper instance
const showButtons = ref(false);
const circularloader = ref(true);
const Store = creationToolStore();
const isPrevDisabled = ref(false);
const isNextDisabled = ref(false);
const checkButtonStates = () => {
  const swiperContainer = document.getElementById('overflowSliderRef').swiper;
  if (!swiperContainer) {
    return;
  }
  const swiperInstance = swiperContainer;
  isPrevDisabled.value = swiperInstance.isBeginning;
  isNextDisabled.value = swiperInstance.isEnd;
  isAtEnd.value = swiperInstance.isEnd;
};
const handleClick = (item, index) => {
  emit('buttonClicked', item, index);
};

const handleClickTour = (item) => {
  emit('gotoTour', item);
};

const handleClickProject = (item) => {
  emit('gotoProject', item._id);
};
const showFadeMask = computed(() => showButtons.value && !isAtEnd.value);
// Next
const handleNext = () => {
  const swiperContainer = document.getElementById('overflowSliderRef').swiper;
  if (!swiperContainer.isEnd) {
    swiperContainer.slideNext();
    checkButtonStates();
  }
};

// Previous
const handlePrev = () => {
  const swiperContainer = document.getElementById('overflowSliderRef').swiper;
  if (!swiperContainer.isBeginning) {
    swiperContainer.slidePrev();
    checkButtonStates();
  }
};

const checkScrollEffect = () => {
  const swiperContainer = document.getElementById('overflowSliderRef').swiper;
  if (!swiperContainer) {
    return;
  }
  console.log(swiperContainer);
  const swiperInstance = swiperContainer;
  const slides = swiperInstance.slides;
  const totalSlidesWidth = Array.from(slides).reduce((acc, slide) => acc + slide.offsetWidth, 0);
  const containerWidth = swiperInstance.width;
  console.log(containerWidth, totalSlidesWidth);
  showButtons.value = totalSlidesWidth > containerWidth;
};

onMounted(() => {
  setTimeout(() => {
    checkScrollEffect();
    checkButtonStates();
  }, 200);
  const swiperContainer = document.getElementById('overflowSliderRef').swiper;
  if (swiperContainer){
    swiperContainer.on('scroll', checkButtonStates);
    swiperContainer.on('transitionEnd', checkButtonStates);
  }
});

watch(() => showButtons.value);

watch(() => props.itemsList, () => {
  onMounted(() => {
    const swiperContainer = document.getElementById('overflowSliderRef').swiper;
    const swiperInstance = swiperContainer;
    checkScrollEffect();
    swiperInstance.on('resize', checkScrollEffect);
  });
}, { immediate: true });

watch(() => props.active, (newval) => {
  activeIndex.value = newval;
  document.getElementById('overflowSliderRef').swiper.slideTo(activeIndex.value, 0);
});

</script>

<template>
  <div class="flex justify-center">
    <div
      class="flex gap-4 justify-center max-w-full items-center"
      :class="Store.isLandscape?'!ml-4':''"
    >
      <div
        v-if="(sliderButton && showButtons) && !Store.isMobile && !Store.isLandscape "
        class="w-fit h-fit"
      >
        <FwbButton
          :disabled="isPrevDisabled"
          class="bg-secondary prev !border-none hover:bg-secondary [&>div]:mr-0 h-10 w-10 rounded-[80px] flex justify-center items-center dark:focus:ring-0 focus:ring-0"
          @click="handlePrev"
        >
          <template #prefix>
            <slot
              class="h-5 w-5 m-0 "
            >
              <div
                class="svgslot w-[21px] h-[21px] flex justify-center items-center"
                v-html="leftButton"
              />
            </slot>
          </template>
        </FwbButton>
      </div>
      <OverflowSlider
        id="overflowSliderRef"
        :slides-per-view="'auto'"
        direction="horizontal"
        :mousewheel="true"
        space-between="16"
        :lazy-loading="true"
        keyboard="true"
        :class="['sample w-full', showFadeMask ? 'slider-fade-mask' : '']"
        :initialSlide="activeIndex"
        @swiper-slide-change="checkScrollEffect"
      >
        <template #options>
          <swiper-slide
            v-for="(item, index) in itemsList"
            :key="index"
            class="pills"
          >
            <FwbCard
              v-if="amenityCategory"
              pill
              class="relative overflow-hidden !border-none cursor-pointer"
              @click="handleClickTour(item)"
            >
              <div class="relative rounded-lg flex justify-center items-center overflow-hidden">
                <img
                  class="sm:w-[152px] sm:h-[100px] w-[120px] h-[72px] object-cover relative"
                  :src="item.thumbnail"
                  alt="icon"
                  @load="()=>circularloader=false"
                >
                <div
                  v-if="circularloader"
                  class="z-10 absolute top-0 left-0 h-full w-full bg-secondary bg-opacity-40 backdrop-blur-[20px] rounded-none flex"
                >
                  <div class="m-auto loader border-4 border-gray-200 border-t-4 border-t-black rounded-full w-5 h-5 sm:mb-10 mb-8 animate-spin" />
                </div>
                <div
                  class="absolute inset-0"
                  :class="Store.projectCardData[route.params.projectId]?.projectSettings?.theme?.theme === 'dark' ? 'gradient' :''"
                >
                  <span>
                    <TranslationComp
                      class="absolute bottom-0 sm:text-base text-xs font-medium text-secondaryText w-full whitespace-nowrap text-ellipsis overflow-hidden text-center bg-secondary p-2 inline "
                      :text="item.name"
                    />
                  </span>
                </div>
              </div>
            </FwbCard>

            <div
              v-else-if="projectsCategory"
              size="md"
              pill
              class="relative overflow-hidden !border-none cursor-pointer"
              @click="handleClickProject(item)"
            >
              <div
                class="relative rounded-lg flex justify-center items-center"
                :class="activeIndex === index ?'border-4 border-blue-500' : '' "
              >
                <PictureCard
                  class="min-h-[85px] w-fit"
                  :title="item.name"
                  :location="item.city"
                  :bedrooms="getBedroomRange(item?.units?.unitDetails?.uniqueBedroomTypes)"
                  :type="getUnitType(item.units?.unitPlanTypeCounts)"
                  :currencyTitle="item?.units?.unitDetails?.currencyType"
                  :price="item?.units?.unitDetails?.minPrice"
                  :imageUrl="cdn(item.project_thumbnail)"
                  :showExplore="false"
                  :isProjectCategory="true"
                />
              </div>
            </div>

            <FwbButton
              v-else
              size="md"
              pill
              :class="[(activeIndex === index ? ' bg-primary text-primaryText hover:bg-primary hover:text-primaryText' : 'border-transparent !text-secondaryText bg-secondary hover:bg-secondary text-col'), 'dark:focus:ring-0 focus:ring-0 whitespace-nowrap capitalize px-5 py-2.5 h-[41px] justify-center items-center inline-flex text-sm font-medium leading-[21px]']"
              @click="handleClick(item,index)"
            >
              <template
                v-if="item[objectIconKey]"
                #prefix
              >
                <slot
                  :name="item[objectNameKey]"
                  class="h-5 w-5 m-0 mr-2"
                >
                  <div
                    :class="[ activeIndex === index ? ' slot stroke-secondary fill-none' : 'stroke-secondaryText ']"
                    class="w-4 h-4"
                    v-html="item[objectIconKey]"
                  />
                </slot>
              </template>
              <TranslationTest
                :key="item[objectNameKey]"
                :text="item[objectNameKey]"
              />
            </FwbButton>
          </swiper-slide>
        </template>
      </OverflowSlider>
      <div
        v-if="(sliderButton && showButtons) && !Store.isMobile && !Store.isLandscape"
        class="w-fit h-fit"
      >
        <FwbButton
          :disabled="isNextDisabled"
          class="bg-secondary !border-none hover:bg-secondary [&>div]:mr-0 h-10 w-10 rounded-[80px] flex justify-center items-center dark:focus:ring-0 focus:ring-0"
          @click="handleNext"
        >
          <template #prefix>
            <slot
              class="h-5 w-5 m-0"
            >
              <div
                class="svgslot w-[21px] h-[21px] flex justify-center items-center"
                v-html="rightButton"
              />
            </slot>
          </template>
        </FwbButton>
      </div>
    </div>
  </div>
</template>

<style scoped>
.activeIndex {
  border:none;
  background: rgb(228,213,180);
  background: linear-gradient(90deg, rgba(228,213,180,1) 0%, rgba(224,186,103,1) 100%);
}

::-webkit-scrollbar {
    width: 0px;
    display: none
}

.activeIndex span {
    @apply text-[#333333] ;
}

.activeIndex path {
    @apply fill-red-500;
}

.activeIndex .svgslot{
    @apply brightness-[0.2]
}

.activeIndex .slot{
    @apply brightness-[0.2]
}

.scrollVisible::-webkit-scrollbar {
    display: none
}
.scrollHidden {
    @apply overflow-hidden
}
.scrollVisible {
    @apply overflow-x-scroll
}
.pills{
    text-wrap: nowrap;
    width:auto;
    overflow: hidden;
    white-space: nowrap;
}
.gradient
{
  background: linear-gradient(180deg, rgba(2,0,36,0) 0%, rgba(28, 28, 28, 0.781) 77%, rgb(0, 0, 0) 100%);
}
</style>

<style lang="scss">
.svgslot svg path{
  stroke:var(--secondaryText) ;
}

.slot svg path{
  stroke:var(--primaryText) ;
}
.slider-fade-mask {
  -webkit-mask-image: linear-gradient(to right, black 60%, transparent);
  mask-image: linear-gradient(to right, black 60%, transparent);
}
</style>
