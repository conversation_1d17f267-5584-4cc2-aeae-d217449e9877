<template>
  <!-- <button v-if="Store.isMobile"
    class="relative p-3 flex max-sm:flex-col-reverse  rounded-full justify-start items-center gap-2 state sm:border sm:border-white"
    :class="Store.isMobile ? '!p-2' : ''"
    @click="emit('goToFavourites')"
  >
    <svg
      id="goToFavotiteSvg"
      class="w-5 h-5 "
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 16.6668C10 16.6668 2.5 12.4897 2.5 7.47711C2.5 2.46452 8.33333 2.0468 10 5.96867C11.6667 2.0468 17.5 2.46452 17.5 7.47711C17.5 12.4897 10 16.6668 10 16.6668Z"
        :stroke="border()"
        stroke-width="1.4"
        stroke-linecap="round"
        stroke-linejoin="round"
        :fill="filler()"
      />
    </svg>

    <div
      id="goToFavotiteText"
      class="absolute -top-[2px] -right-1 sm:-top-[10px] sm:-right-2 w-5 h-5 sm:w-6 sm:h-6 bg-white rounded-full text-black sm:text-xs text-center text-[10px] font-normal whitespace-nowrap flex items-center justify-center leading-tight"
    >
      {{ count < 10 ? count == 0 ? `0` : `0${count}` : count }}
    </div>
  </button> -->

  <button
    type="button"
    class=" rounded-tl-lg rounded-br-lg rounded-tr-lg rounded-bl-lg  inline-flex justify-center items-center gap-2 relative  px-5 py-2.5 text-sm font-medium text-center text-secondaryText rounded-lg focus:outline-none"
    :class="[Store.isMobile ? '!p-2 rounded-full w-11 h-11' : 'bg-secondary backdrop-filter backdrop-blur-[0.625rem]', Store.isMobile && activeSvg ? 'favButtonMobActive' : 'favButtonMobInactive', !Store.isMobile && activeSvg ? 'favButtonActive' : 'favButtonInactive']"
    @click="emit('goToFavourites')"
  >
    <div v-html="localSvg" />

    <span class="sr-only">Notifications</span>
    <TranslationComp
      :text="text"
    />
    <div
      v-if="count > 0"
      class="absolute inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-primaryText bg-primary rounded-full -top-2 -end-2 dark:border-gray-900"
    >
      {{ count < 10 ? `0${count}` : count }}
    </div>
  </button>
</template>

<script setup>

import {defineProps, defineEmits, ref, watch} from 'vue';
const emit = defineEmits(['goToFavourites']);
import { creationToolStore } from '../../../store';
import TranslationComp from '../TranslationComp/TranslationComp.vue';
const Store = creationToolStore();
const props = defineProps({
  count: {type: Number, default: 0},
  text: {type: String, default: '' },
  svg: {
    type: String,
    default: "",
  },
  active: Boolean,
});
const localSvg = ref(props.svg);
const activeSvg = ref(props.active);

watch(() => props.active, (newSvg) => {
  activeSvg.value = newSvg;
}, { immediate: true });

// Const filler = () => (props.count> 0 ? '#FF4D00' : 'none');
// Const border = () => (props.count> 0 ? '#FF4D00' : '#D8DBDF');

</script>
<style scoped>
.state:hover>div::v-deep>svg > path , .state:hover{
  background-color: rgba(160, 160, 160, 0.4);
  cursor: pointer;
}

.favButton{
padding: var(--25, 10px) var(--5, 20px);
}
.favButtonMob{
padding: var(--25, 15px) var(--5, 15px);
}
.favButtonInactive>div::v-deep>svg > g > path{
  stroke:var(--secondaryText);
  fill:var(--secondaryText)
}
.favButtonActive>div::v-deep>svg > g > path{
  stroke:var(--primaryText);
  fill:var(--primaryText);
}
.favButtonMobInactive>div::v-deep>svg > path{
  stroke:var(--secondaryText);
  fill:var(--secondaryText)
}
.favButtonMobActive>div::v-deep>svg > path{
  stroke:var(--primaryText);
  fill:var(--primaryText);
}
</style>
