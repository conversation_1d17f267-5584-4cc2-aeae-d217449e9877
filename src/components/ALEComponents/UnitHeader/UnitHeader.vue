<template>
  <div
    class="h-fit w-screen px-6 py-4 bg-white  flex flex-col gap-3"
    aria-label="Unit Header"
  >
    <div
      v-if="floor"
      class="font-bold"
    >
      {{ floor }}th Floor
    </div>
    <div
      v-else
      class="font-bold"
    >
      {{ unitname }} Unit
    </div>
    <div class="text-xs text-gray-500 md:text-sm flex gap-4 md:gap-6">
      <div class="flex gap-1 sm:gap-2">
        <div
          v-if="units"
          class="h-fit my-auto w-fit"
        >
          <svg
            width="15"
            height="15"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M2.22222 0.5H8.38889V8.38889H0.5V2.22222C0.5 1.27614 1.27614 0.5 2.22222 0.5ZM19.5 2.22222V8.38889H11.6111V0.5H17.7778C18.7239 0.5 19.5 1.27614 19.5 2.22222ZM0.5 17.7778V11.6111H8.38889V19.5H2.22222C1.27614 19.5 0.5 18.7239 0.5 17.7778ZM17.7778 19.5H11.6111V11.6111H19.5V17.7778C19.5 18.7239 18.7239 19.5 17.7778 19.5Z"
              stroke="#6B7280"
            />
          </svg>
        </div>
        <div class="w-fit my-auto h-fit">
          {{ units?units+' units':GetCurrencySymbol(currency)+' '+price }}
        </div>
      </div>
      <div class="flex gap-1 sm:gap-2">
        <div class="h-fit my-auto w-fit">
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M3.45491 5.06728C3.16158 5.3409 2.70198 5.32492 2.42837 5.03159L0.951924 3.44878C0.691039 3.1691 0.691747 2.73502 0.953544 2.4562L2.42999 0.883702C2.70456 0.591268 3.16421 0.576788 3.45664 0.85136C3.74908 1.12593 3.76356 1.58558 3.48898 1.87802L3.12474 2.26596H11.3346L10.973 1.87833C10.6994 1.585 10.7154 1.1254 11.0087 0.851782C11.302 0.578166 11.7616 0.594146 12.0353 0.887475L13.5117 2.47029C13.7726 2.74997 13.7719 3.18404 13.5101 3.46287L12.0336 5.03536C11.7591 5.3278 11.2994 5.34228 11.007 5.06771C10.7145 4.79313 10.7001 4.33348 10.9746 4.04105L11.3367 3.65543H3.13119L3.4906 4.04074C3.76422 4.33407 3.74824 4.79367 3.45491 5.06728ZM12.078 7.75002H1.85742C1.78839 7.75002 1.73242 7.80599 1.73242 7.87503V18.0788C1.73242 18.1478 1.78839 18.2038 1.85742 18.2038H12.078C12.1471 18.2038 12.203 18.1478 12.203 18.0788V7.87502C12.203 7.80599 12.1471 7.75002 12.078 7.75002ZM1.85742 6.75002C1.2361 6.75002 0.732422 7.2537 0.732422 7.87503V18.0788C0.732422 18.7001 1.2361 19.2038 1.85742 19.2038H12.078C12.6993 19.2038 13.203 18.7001 13.203 18.0788V7.87502C13.203 7.2537 12.6993 6.75002 12.078 6.75002H1.85742ZM14.9164 7.95812C14.6231 8.23174 14.6071 8.69134 14.8807 8.98467C15.1544 9.278 15.614 9.29398 15.9073 9.02036L16.2961 8.65771V17.2901L15.9104 16.928C15.618 16.6534 15.1584 16.6679 14.8838 16.9603C14.6092 17.2528 14.6237 17.7124 14.9161 17.987L16.4886 19.4634C16.7675 19.7252 17.2015 19.726 17.4812 19.4651L19.064 17.9886C19.3574 17.715 19.3733 17.2554 19.0997 16.9621C18.8261 16.6687 18.3665 16.6528 18.0732 16.9264L17.6855 17.288V8.65776L18.07 9.01874C18.3624 9.29331 18.8221 9.27883 19.0967 8.9864C19.3712 8.69397 19.3568 8.23432 19.0643 7.95974L17.4918 6.4833C17.213 6.2215 16.7789 6.22079 16.4992 6.48168L14.9164 7.95812Z"
              fill="#6B7280"
            />
          </svg>
        </div>
        <div class="w-fit my-auto h-fit">
          {{ area }}
        </div>
      </div>
      <div class="flex gap-1 sm:gap-2">
        <div class="h-fit my-auto w-fit">
          <svg
            width="24"
            height="18"
            viewBox="0 0 24 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.5 3.75H2.25V1.5C2.25 1.30109 2.17098 1.11032 2.03033 0.96967C1.88968 0.829018 1.69891 0.75 1.5 0.75C1.30109 0.75 1.11032 0.829018 0.96967 0.96967C0.829018 1.11032 0.75 1.30109 0.75 1.5V16.5C0.75 16.6989 0.829018 16.8897 0.96967 17.0303C1.11032 17.171 1.30109 17.25 1.5 17.25C1.69891 17.25 1.88968 17.171 2.03033 17.0303C2.17098 16.8897 2.25 16.6989 2.25 16.5V13.5H21.75V16.5C21.75 16.6989 21.829 16.8897 21.9697 17.0303C22.1103 17.171 22.3011 17.25 22.5 17.25C22.6989 17.25 22.8897 17.171 23.0303 17.0303C23.171 16.8897 23.25 16.6989 23.25 16.5V7.5C23.25 6.50544 22.8549 5.55161 22.1516 4.84835C21.4484 4.14509 20.4946 3.75 19.5 3.75ZM2.25 5.25H9V12H2.25V5.25ZM10.5 12V5.25H19.5C20.0967 5.25 20.669 5.48705 21.091 5.90901C21.5129 6.33097 21.75 6.90326 21.75 7.5V12H10.5Z"
              fill="#6B7280"
            />
          </svg>
        </div>
        <div
          v-if="bedroom"
          class="w-fit my-auto h-fit"
        >
          {{ bedroom }}
        </div>
        <div
          v-else
          class="w-fit my-auto h-fit"
        >
          {{ minBedrooms }} - {{ maxBedrooms }}
        </div>
      </div>
      <div
        v-if="status"
        class="flex gap-1 sm:gap-2"
      >
        <div
          :class="getStatusClass(status)"
          class="w-fit my-auto h-fit text-lg font-normal capitalize"
        >
          {{ status }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { GetCurrencySymbol } from '../../../helpers/API';
import { getStatusClass } from '../../../helpers/helper';
defineProps(
  {
    modalWidth: {type: Number, default: 0},
    floor: {type: Number, default: 0},
    units: {type: Number, default: 0},
    area: {type: String, default: ""},
    minBedrooms: {type: Number, default: 0},
    maxBedrooms: {type: Number, default: 0},
    unitname: {type: String, default: ""},
    status: {type: String, default: ""},
    currency: {type: String, default: ""},
    price: {type: Number, default: 0},
    bedroom: {type: Number, default: 0},
  },
);
</script>
