import BlobImageLoader from "./BlobImageLoader.vue";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: "Design System/ALE/BlobImageLoader ",
  component: BlobImageLoader,
  tags: ["autodocs"],
  argTypes: {},
};

export const Primary = {
  args: {
    name: 'sample',
    thumbnail: 'https://storagecdn.propvr.ai/CreationtoolAssets%2FFcxAht%2Fprojects%2F6630f65133732b2ec33512c6%2Fgallery%2F6697a4a897ecece5334abb87%2F25Single%20Tower-gigapixel-high%20fidelity%20v2-2048h_thumb.webp',
    url: 'https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2FFcxAht%2Fprojects%2F6630f65133732b2ec33512c6%2Fgallery%2F6697a4a897ecece5334abb87%2F25Single%20Tower-gigapixel-high%20fidelity%20v2-2048h.jpg?alt=media',
  },

};
