import StatusPopup from './StatusPopup.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/ALE/StatusPopup',
  component: StatusPopup,
  tags: ['autodocs'],
  argTypes: {
    show: {
      control: 'boolean',
      description: 'Controls popup visibility',
    },
    type: {
      control: 'select',
      options: ['success', 'error'],
      description: 'Popup type - determines icon and styling',
    },
    title: {
      control: 'text',
      description: 'Popup title',
    },
    message: {
      control: 'text',
      description: 'Popup message content',
    },
    buttonText: {
      control: 'text',
      description: 'Button text',
    },
    isMobile: {
      control: 'boolean',
      description: 'Mobile layout flag',
    },
  },
};

export const Success = {
  args: {
    show: true,
    type: 'success',
    title: 'Successfully Registered Your Interest',
    message: 'Our team will reach out to you soon.',
    buttonText: 'Continue',
    isMobile: false,
  },
};

export const Error = {
  args: {
    show: true,
    type: 'error',
    title: 'Error',
    message: 'Failed to submit your interest. Please try again.',
    buttonText: 'Try Again',
    isMobile: false,
  },
};

export const Mobile = {
  args: {
    show: true,
    type: 'success',
    title: 'Success',
    message: 'Your request has been processed successfully.',
    buttonText: 'Continue',
    isMobile: true,
  },
};

export const LongMessage = {
  args: {
    show: true,
    type: 'error',
    title: 'Connection Error',
    message: 'We are experiencing technical difficulties connecting to our servers. Please check your internet connection and try again. If the problem persists, contact our support team.',
    buttonText: 'Retry',
    isMobile: false,
  },
};
