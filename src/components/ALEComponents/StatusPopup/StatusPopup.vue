<template>
  <div
    v-if="show"
    class="fixed inset-0 lg:min-w-[35vw] !z-[55] flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-[30px] p-4"
  >
    <div
      class="bg-secondary rounded-lg flex-col justify-center items-center inline-flex w-full max-w-md p-2 lg:min-w-[35vw]"
    >
      <div
        class="h-5 w-5 w-full flex items-end justify-end cursor-pointer"
        @click="$emit('close')"
      >
        <svg
          class="w-5 h-5"
          viewBox="0 0 27 27"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M20.25 20.25L6.75 6.75"
            stroke="var(--secondaryText)"
            stroke-width="1.575"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M20.25 6.75L6.75 20.25"
            stroke="var(--secondaryText)"
            stroke-width="1.575"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div class="flex justify-center items-center w-full">
        <div class="flex items-center justify-center mb-2">
          <svg
            v-if="!isError"
            xmlns="http://www.w3.org/2000/svg"
            width="48"
            height="48"
            viewBox="0 0 48 48"
            fill="none"
          >
            <path
              d="M0 24C0 10.7452 10.7452 0 24 0C37.2548 0 48 10.7452 48 24C48 37.2548 37.2548 48 24 48C10.7452 48 0 37.2548 0 24Z"
              fill="#0E9F6E"
            />
            <path
              d="M21.1554 32.3333C20.829 32.3349 20.515 32.1816 20.2807 31.9062L14.3774 24.9625C14.2598 24.8232 14.1659 24.6572 14.1011 24.474C14.0364 24.2908 14.0021 24.0939 14.0001 23.8946C13.9961 23.4921 14.1242 23.1041 14.3562 22.816C14.5882 22.528 14.9051 22.3635 15.2372 22.3586C15.5692 22.3538 15.8893 22.5091 16.1269 22.7903L21.1604 28.7085L31.8722 16.098C32.1102 15.8168 32.4305 15.6617 32.7628 15.6668C33.0951 15.6719 33.4122 15.8368 33.6442 16.1253C33.8762 16.4137 34.0041 16.8021 33.9999 17.2049C33.9957 17.6077 33.8596 17.992 33.6217 18.2732L22.0301 31.9062C21.7958 32.1816 21.4818 32.3349 21.1554 32.3333Z"
              fill="white"
            />
          </svg>
          <svg
            v-else
            xmlns="http://www.w3.org/2000/svg"
            width="30"
            height="30"
            viewBox="0 0 20 20"
            fill="none"
          >
            <path
              d="M10 0C8.02219 0 6.08879 0.58649 4.4443 1.6853C2.79981 2.78412 1.51809 4.3459 0.761209 6.17316C0.00433284 8.00042 -0.1937 10.0111 0.192152 11.9509C0.578004 13.8907 1.53041 15.6725 2.92894 17.0711C4.32746 18.4696 6.10929 19.422 8.0491 19.8078C9.98891 20.1937 11.9996 19.9957 13.8268 19.2388C15.6541 18.4819 17.2159 17.2002 18.3147 15.5557C19.4135 13.9112 20 11.9778 20 10C19.9971 7.34873 18.9426 4.80688 17.0679 2.93215C15.1931 1.05741 12.6513 0.0029116 10 0ZM10 15C9.80222 15 9.60888 14.9413 9.44443 14.8315C9.27998 14.7216 9.15181 14.5654 9.07612 14.3827C9.00044 14.2 8.98063 13.9989 9.01922 13.8049C9.0578 13.6109 9.15304 13.4327 9.2929 13.2929C9.43275 13.153 9.61093 13.0578 9.80491 13.0192C9.99889 12.9806 10.2 13.0004 10.3827 13.0761C10.5654 13.1518 10.7216 13.28 10.8315 13.4444C10.9414 13.6089 11 13.8022 11 14C11 14.2652 10.8946 14.5196 10.7071 14.7071C10.5196 14.8946 10.2652 15 10 15ZM11 11C11 11.2652 10.8946 11.5196 10.7071 11.7071C10.5196 11.8946 10.2652 12 10 12C9.73479 12 9.48043 11.8946 9.2929 11.7071C9.10536 11.5196 9 11.2652 9 11V6C9 5.73478 9.10536 5.48043 9.2929 5.29289C9.48043 5.10536 9.73479 5 10 5C10.2652 5 10.5196 5.10536 10.7071 5.29289C10.8946 5.48043 11 5.73478 11 6V11Z"
              fill="#E02424"
            />
          </svg>
        </div>
      </div>
      <div class="text-center">
        <h3
          v-if="title"
          class="text-secondaryText text-lg font-semibold text-xl mb-2"
        >
          <TranslationComp :text="title" />
        </h3>
        <p class="font-normal text-base leading-relaxed text-secondaryText mb-4">
          <TranslationComp
            :text="message"
          />
        </p>
        <div class="flex justify-center mb-2">
          <div class="button-small-width">
            <ButtonComponent
              :message="buttonText"
              @handle-button-click="$emit('close')"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import TranslationComp from '../TranslationComp/TranslationComp.vue';
import ButtonComponent from '../ButtonComponent/ButtonComponent.vue';

const props = defineProps({
  show: { type: Boolean, default: false },
  type: { type: String, required: true, validator: (v) => ['success', 'error'].includes(v) },
  title: { type: String, required: true },
  message: { type: String, required: true },
  buttonText: { type: String, default: 'Continue' },
  isMobile: { type: Boolean, default: false },
});

defineEmits(['close']);

const isError = computed(() => props.type === 'error');

</script>

<style scoped>
.button-small-width :deep(.buttons) {
  width: auto !important;
  min-width: auto !important;
  padding: 8px 0px 8px 16px !important;
}
</style>
