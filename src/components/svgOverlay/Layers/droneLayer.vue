<script setup>
import {defineProps, ref} from 'vue';

defineProps({layerData: {
  type: Object,
  default () {
    return {};
  },
}});
const clickedId=ref(false);
</script>
<template>
  <!-- eslint-disable vue/no-v-html -->
  <g
    v-for="(layer,index) in layerData"
    :id="layer.layer_data.layer_id"
    :key="index"
    :class="[
      'cursor-pointer opacity-80' ,
      'hover:opacity-100',
      layer.layer.getAttribute('class') +' '+layer.layer_data.type,
      clickedId==layer.layer_data.layer_id && clickedId!=false?'fill-white':'bg-black'
    ]"
    @click="moveToNxtScene(layer.layer_data.layer_id)"
    v-html="layer.layer.innerHTML"
  />
  <!--eslint-enable-->
  <portal to="drone">
    <div />
  </portal>
</template>
<style scoped>

</style>
