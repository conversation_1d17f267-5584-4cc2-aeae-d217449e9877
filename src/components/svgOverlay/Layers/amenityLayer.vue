<script setup>

import { ref, defineProps, onMounted, watch } from 'vue';
// Import PanoramaCard from '../../ALEComponents/PanoramaCard/PanoramaCard.vue';
import router from '../../../router';
import { creationToolStore } from '../../../store/index';
import { useRoute } from 'vue-router';
// import AmenityCard from '../../ALEComponents/AmenityCard/AmenityCard.vue';
// import Icon360 from '../../../../public/assets/Icons/360.gif';
// Import PopUpMessageBox from '../../ALEComponents/PopUpMessageBox/PopUpMessageBox.vue';
import { addSVGDeepZoom, cdn, Googleanalytics, setActiveElem } from '../../../helpers/helper';
import OpenSeadragon from 'openseadragon';

// ADDED
// Import AmenitiesDropdown from '../../ALEComponents/AmenitiesDropdown/AmenitiesDropdown.vue';
// Const closeFullscreenModal = ref(false);
// Const checkAutoExit = ref(false);
const route = useRoute();
const Store = creationToolStore();
const props = defineProps({layerData: {
  type: Object,
  default () {
    return {};
  },
}, sceneType: {
  type: String,
  default: '',
}});
// const show_modal = ref(false);
// const position = ref(false);
const amenityId = ref();
const startTime = ref(new Date), timespent = ref(0), amenitySVGElem=ref({});

// function closeModal (e){
//   // Router.back()
//   if ((e===undefined || e) && (e.target.getAttribute("clickaway")==="true")){
//     show_modal.value=false;
//   }
// }
// Emit Added
// Const handleGoToProject = (list) => {
//   AmenityId.value = list._id;
//   Router.push({ name: "projectScene", query: { amenity_id: list._id } });
//   Position.value = { x: 2, y: 2 };
//   Show_modal.value = true;
// };

function gotoTour (){
  var endtime = new Date();
  var difference = Math.abs(startTime.value - endtime) / 1000;
  timespent.value = difference;
  startTime.value=new Date();
  Googleanalytics("amenity_clicked", {
    amenity_name: Store.amenityCardData[amenityId.value].name,
    amenity_id: amenityId.value,
    amenity_category: Store.amenityCardData[amenityId.value].category,
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
    // amenity_timespent: timespent.value,
    // amenity_timestart: startTime.value,
  });
  router.push({name: 'amenityview', params: {amenityId: amenityId.value}, query: {...route.query}});
}

function placeModal (layer_id, amenity_id) {
  amenityId.value = amenity_id;
  // router.push({ name: "projectScene", query: {...route.query, amenity_id: amenity_id } });
  // show_modal.value = true;
  gotoTour();
}
// Function setFullScreenCookie () {
//   If (!getCookie('fullscreen')) {
//     Const expiryTime = new Date(Date.now() + (30 * 6000)); // 30 minutes in milliseconds
//     Document.cookie = `fullscreen=true; path=/; expires=${expiryTime.toUTCString()};`;
//     CloseFullscreenModal.value = true;
//   }
// }
// Const onClickButton = (id) => {
//   If (id === 'fullscreen') {
//     SetFullScreenCookie();
//     If (!document.fullscreenElement &&
//       !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
//       If (document.documentElement.requestFullscreen) {
//         Document.documentElement.requestFullscreen();
//       } else if (document.documentElement.mozRequestFullScreen) {
//         Document.documentElement.mozRequestFullScreen();
//       } else if (document.documentElement.webkitRequestFullscreen) {
//         Document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
//       } else if (document.documentElement.msRequestFullscreen) {
//         Document.documentElement.msRequestFullscreen();
//       }
//       CheckAutoExit.value = false;
//       Store.isFullScreen = !Store.isFullScreen;

//     } else {
//       If (document.exitFullscreen) {
//         Document.exitFullscreen();
//       } else if (document.mozCancelFullScreen) {
//         Document.mozCancelFullScreen();
//       } else if (document.webkitExitFullscreen) {
//         Document.webkitExitFullscreen();
//       } else if (document.msExitFullscreen) {
//         Document.msExitFullscreen();
//       }
//       CheckAutoExit.value = true;
//       Store.isFullScreen = !Store.isFullScreen;
//     }
//   }
// };
if (props.sceneType==='deep_zoom'){
  onMounted(() => {
    if (Object.values(props.layerData).length > 0) {
      Object.values(props.layerData).forEach(async (item) => {
        const requestOptions = {
          method: "GET",
          redirect: "follow",
        };
        const response = await fetch(cdn(item.layer), requestOptions);
        const svgString = await response.text();
        const obj = addSVGDeepZoom({
          g: svgString,
          zIndex: item.layer_data.zIndex,
          reSize: item.layer_data.reSize,
          x: item.layer_data.x,
          y: item.layer_data.y,
          width: item.layer_data.width,
          height: item.layer_data.height,
          placement: item.layer_data.placement,
          layer_id: item.layer_data.layer_id,
        }, window.viewer);

        obj.svgElement.style.cursor= 'pointer';
        amenitySVGElem.value[item.layer_data.layer_id] = {'g': obj.svgElement, 'minZoomLevel': item.layer_data.minZoomLevel, 'maxZoomLevel': item.layer_data.maxZoomLevel};
        obj.svgElement.addEventListener("mouseleave", () =>
          obj.svgElement.classList.remove('opacity-100'),
        );
        obj.svgElement.addEventListener("mouseenter", () =>
          obj.svgElement.classList.add('opacity-100'),
        );
        new OpenSeadragon.MouseTracker({
          element: obj.svgElement,
          clickHandler: function (event) {
            placeModal(item.layer_data.layer_id, item.layer_data.amenity_id, event);
          },
        });
        if (item.layer_data.minZoomLevel  && item.layer_data.maxZoomLevel){
          if (Store.currentZoomlevel >= item.layer_data.minZoomLevel
          && Store.currentZoomlevel<= item.layer_data.maxZoomLevel){
            obj.svgElement.classList.add('!visible');
            obj.svgElement.classList.remove('!hidden');
          } else {
            obj.svgElement.classList.remove('!visible');
            obj.svgElement.classList.add('!hidden');
          }
        }
      });
    }
  });
}
watch(() => Store.currentZoomlevel, () => {
  setActiveElem(amenitySVGElem.value, Store.currentZoomlevel);
});
watch(() => Store.svgVisibility.showSVG, (isVisible) => {
  if (props.sceneType !== 'deep_zoom') {
    return;
  }

  Object.values(props.layerData).forEach((layer) => {
    if (amenitySVGElem.value[layer.layer_data.layer_id]) {
      const amenityElement = amenitySVGElem.value[layer.layer_data.layer_id].g;
      if (isVisible) {
        amenityElement.classList.remove('!hidden');
      } else {
        amenityElement.classList.add('!hidden');
      }
    }
  });
});
</script>
<template>
  <!-- <rect
    v-if="show_modal"
    class="rect_overlay"
    width="100%"
    height="100%"
    fill="rgba(0, 0, 0, 0.5)"
  /> -->

  <!-- eslint-disable vue/no-v-html -->
  <template v-if="Store.svgVisibility.showSVG">
    <g
      v-for="layer in layerData"
      :id="layer.layer_data.layer_id"
      :key="layer"
      :class="props.sceneType!=='deep_zoom'?[layer.layer.getAttribute('class') + ' ' + layer.layer_data.type + ' opacity-80 hover:opacity-100 cursor-pointer']:''"
      @click="(event) => placeModal(layer.layer_data.layer_id, layer.layer_data.amenity_id, event)"
      v-html="layer.layer.innerHTML"
    />
  </template>
  <!--eslint-enable-->
  <!--
  <portal to="amenity">
    <div
      v-if="show_modal && Store.amenityCardData[amenityId]"
      class="fixed top-0 left-0 w-screen h-full flex justify-center z-20"

      clickaway="true"

      @click="closeModal"
    >
      <div class="absolute md:bottom-8 md:bottom-12 bottom-24 md:left-auto slide-animation">
        <PanoramaCard
          :hideclose="false"
          :name="Store.amenityCardData[amenityId].name"
          media-type="image"
          :link="Store.amenityCardData[amenityId]['media'][0]['file']"
          icon="https://external-content.duckduckgo.com/iu/?u=http%3A%2F%2Fgetv360.com%2Fimages%2F360-icon2x.png&f=1&nofb=1&ipt=3fcdc5b063d87798c0818c6254968fa2f3829747c3af1b45d7346ec52ae8cf88&ipo=images"
          @on-click-close="closeModal"
          @on-open-project="gotoTour"
        />
        <AmenityCard
          :name="Store.amenityCardData[amenityId].name"
          :hideclose="false"
          :description="Store.amenityCardData[amenityId].description"
          :link="Store.amenityCardData[amenityId].thumbnail"
          :icon="Icon360"
          @goto-tour="gotoTour"
        />
      </div>
    </div>
  </portal> -->
</template>

<style scoped>

.slide-animation
{
  animation:slide 0.3s 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}
@keyframes slide
{
  0%
  {
    bottom :-12em;
  }

  100%
  {
   @apply md:bottom-[0.4em];

  }
}

</style>
