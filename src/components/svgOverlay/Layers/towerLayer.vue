<script setup>
import { ref, defineProps, onMounted, watch, reactive } from 'vue';
import { cdn, addSVGDeepZoom, setActiveElem } from '../../../helpers/helper';
import OpenSeadragon from 'openseadragon';
import PictureCard from '../../ALEComponents/PictureCard/PictureCard.vue';
import router from '../../../router';
import { creationToolStore } from '../../../store/index';
import { useRoute } from 'vue-router';
import { fetchVideo } from '../../../helpers/API';
import { onClickOutside } from '@vueuse/core';
import { availUnitOrgs } from '../../../config/masterdata';

const route = useRoute();
const Store = creationToolStore();

const show_modal = ref(false);
const position = ref(false), buildingId = ref(), sceneId = ref(), communityId = ref(), layerId=ref();
const projectId = ref(route.params.projectId);
const towerSVGElem = ref({}), clickedLayerId = ref(false);
const towerRef = reactive({}), towerTextElem = ref({});
const pictureCardRef = ref(null);
let isZoomingAfterClick = false;

// Function to set community refs properly
const setTowerRef = (el, index) => {
  if (el) {
    towerRef[index] = el;
  }
};
const props = defineProps({layerData: {
  type: Object,
  default () {
    return {};
  },
},
sceneType: {
  type: String,
  default: '',
}});
fetchVideo(props.layerData, cdn(Store.SceneData[route.params.sceneId].sceneData.background?.low_resolution));

function moveTonxtScene (){
  if (props.layerData[layerId.value].layer_data.video_tag){
    Store.currentSceneVideo=props.layerData[layerId.value].layer_data.video_tag;
    Store.currentSceneVideoThumb=Store.SceneData[route.params.sceneId].sceneData.background.low_resolution;
    window.asd = function (){
      if (Object.values(Store.floorData).length===0){
        Store.getFloorplanDetails(buildingId.value, route.params.projectId, route.params.organizationId).then(() => {
          router.push({ name: 'projectScene', params: { sceneId: sceneId.value }, query: { building_id: buildingId.value } });
        });
      } else {
        router.push({ name: 'projectScene', params: { sceneId: sceneId.value }, query: { building_id: buildingId.value } });
      }
    };
  } else {
    if (Object.values(Store.floorData).length===0){
      Store.getFloorplanDetails(buildingId.value, route.params.projectId, route.params.organizationId).then(() => {
        router.push({ name: 'projectScene', params: { sceneId: sceneId.value }, query: { building_id: buildingId.value } });
      });
    } else {
      router.push({ name: 'projectScene', params: { sceneId: sceneId.value }, query: { building_id: buildingId.value } });
    }
  }
}
function placeModal (layer_id, building_id, community_id, scene_id) {
  layerId.value=layer_id;
  const svgGroup = document.getElementById(layer_id);
  const rect = svgGroup.getBoundingClientRect();
  communityId.value = community_id;
  show_modal.value = true;
  buildingId.value = building_id;
  sceneId.value = scene_id;
  router.push({ name: 'projectScene', query: { building_id: building_id } });
  position.value = { x: rect.x, y: rect.y + 30 };
}
if (props.sceneType==='deep_zoom'){
  onMounted(() => {
    if (Object.values(props.layerData).length > 0) {
      Object.values(props.layerData).forEach(async (item) => {
        const requestOptions = {
          method: "GET",
          redirect: "follow",
        };
        const response = await fetch(cdn(item.layer), requestOptions);
        const svgString = await response.text();
        const obj = addSVGDeepZoom({
          g: svgString,
          zIndex: item.layer_data.zIndex,
          reSize: item.layer_data.reSize,
          x: item.layer_data.x,
          y: item.layer_data.y,
          width: item.layer_data.width,
          height: item.layer_data.height,
          placement: item.layer_data.placement,
          layer_id: item.layer_data.layer_id,
        }, window.viewer);
        obj.svgElement.children[0].style.cursor= 'pointer';

        obj.svgElement.children[0].setAttribute("label",  item.layer_data.showLabel ? item.layer_data.name : '');
        // opacity based on showLabel
        if (item.layer_data.showLabel){
          obj.svgElement.children[0].classList.add('opacity-40', 'stroke-[0.2rem]');
          // create label overlay and pin with animation
          const outerDiv = document.createElement("div");
          outerDiv.classList.add("w-[100px]", "h-[70px]", "grid", "place-items-center", "z-[1]");

          const middleDiv = document.createElement("div");
          middleDiv.classList.add("w-[80px]", "h-[80px]", "rounded-[50%]", "grid", "place-items-center", "z-[1]", "animation", "bg-primary");

          const innerDiv = document.createElement("div");
          innerDiv.classList.add("w-[35px]", "h-[35px]", "bg-secondary", "rounded-[50%]", "grid", "place-items-center", "z-[1]");

          const coreDiv = document.createElement("div");
          coreDiv.classList.add("w-[16px]", "h-[16px]", "bg-primary", "rounded-[50%]", "z-[1]");

          innerDiv.appendChild(coreDiv);
          middleDiv.appendChild(innerDiv);
          outerDiv.appendChild(middleDiv);

          // Add label overlay
          const svgElement = document.createElement("span");
          svgElement.classList.add("font-medium", "rounded", "z-[1]");
          svgElement.classList.add("pointer-events-none", 'text-[10px]', 'p-[3px]', 'text-secondaryText', 'bg-secondary', "opacity-75");
          svgElement.id = item.layer_data.layer_id + 'label';
          svgElement.innerHTML = item.layer_data.showLabel ? item.layer_data.name : '';

          // Create a single container for both overlays
          const combinedOverlay = document.createElement('div');
          combinedOverlay.style.display = 'flex';
          combinedOverlay.style.flexDirection = 'column';
          combinedOverlay.style.alignItems = 'center';
          combinedOverlay.style.gap = '10px';

          combinedOverlay.appendChild(outerDiv);
          combinedOverlay.appendChild(svgElement);

          //  final wrapper
          const finalWrapper = document.createElement('div');
          finalWrapper.appendChild(combinedOverlay);

          // Calculate the center coordinates of the existing element
          const centerX = item.layer_data.x + (item.layer_data.width / 2);
          const centerY = item.layer_data.y + (item.layer_data.height / 2);

          window.viewer.addOverlay({
            element: finalWrapper,
            px: centerX,
            py: centerY,
            placement: "CENTER",
            checkResize: false,
          });

          towerTextElem.value[item.layer_data.layer_id] = {
            'g': finalWrapper,
            'minZoomLevel': item.layer_data.minZoomLevel,
            'maxZoomLevel': item.layer_data.maxZoomLevel,
          };
        }  else { // is showLabel is false
          obj.svgElement.children[0].classList.add('opacity-80', 'stroke-[0.2rem]');

          towerSVGElem.value[item.layer_data.layer_id] = {
            'g': obj.svgElement.children[0],
            'minZoomLevel': item.layer_data.minZoomLevel,
            'maxZoomLevel': item.layer_data.maxZoomLevel,
          };
        }

        // towerSVGElem.value[item.layer_data.layer_id] = {
        //   'g': obj.svgElement.children[0],
        //   'minZoomLevel': item.layer_data.minZoomLevel,
        //   'maxZoomLevel': item.layer_data.maxZoomLevel
        // };

        new OpenSeadragon.MouseTracker({
          element: obj.svgElement.children[0],
          clickHandler: function (e) {
            if (item.layer_data.layer_id !== clickedLayerId.value){
              clickedLayerId.value = item.layer_data.layer_id;
              // placeModal(item.layer_data.layer_id, item.layer_data.building_id, item.layer_data.community_id, item.layer_data.scene_id, e);
              const viewport = window.viewer.viewport;
              const viewerRect = window.viewer.element.getBoundingClientRect();
              const overlayRect = obj.svgElement.children[0].getBoundingClientRect(); // Get the element position and sizings
              // Calculate the center of the overlay in screen coordinates
              const overlayCenterX = overlayRect.left + (overlayRect.width / 2); // X
              const overlayCenterY = overlayRect.top + (overlayRect.height / 2); // Y
              // Convert the screen coordinates to viewport coordinates
              const viewportPoint = viewport.pointFromPixel(
                new OpenSeadragon.Point(
                  overlayCenterX - viewerRect.left,
                  overlayCenterY - viewerRect.top,
                ),
              );

              if (Store.currentZoomlevel < 2.5){
                isZoomingAfterClick = true; // Set the flag
                // Define the zoom level you want to zoom into
                const zoomLevel = Math.round(viewport.getZoom()) * 2.5; // Example: zoom in by a factor of 2
                viewport.zoomTo(zoomLevel, viewportPoint); // Zoom in to calculated point
                viewport.applyConstraints();
              } else {
                placeModal(item.layer_data.layer_id, item.layer_data.building_id, item.layer_data.community_id, item.layer_data.scene_id, e);
              }

              window.viewer.addOnceHandler('animation-finish', function () {
                isZoomingAfterClick = false; // Unset the flag
                // ONLY show the card if the clickedLayerId has NOT been reset by a zoom-out
                if (clickedLayerId.value === item.layer_data.layer_id) {
                  placeModal(item.layer_data.layer_id, item.layer_data.building_id, item.layer_data.community_id, item.layer_data.scene_id, e);
                }
              });
            }
          },
        });

        if (item.layer_data.minZoomLevel  && item.layer_data.maxZoomLevel){
          if (Store.currentZoomlevel >= item.layer_data.minZoomLevel
          && Store.currentZoomlevel<= item.layer_data.maxZoomLevel){
            obj.svgElement.children[0].classList.add('!visible');
            obj.svgElement.children[0].classList.remove('!hidden');
          } else {
            obj.svgElement.children[0].classList.remove('!visible');
            obj.svgElement.children[0].classList.add('!hidden');
          }
        }

        // hover state
        if (item.layer_data.showLabel){
          obj.svgElement.children[0].addEventListener("mouseover", () => {
            obj.svgElement.children[0].classList.remove('opacity-40');
            obj.svgElement.children[0].classList.add('opacity-5');
          });

          obj.svgElement.children[0].addEventListener("mouseleave", () => {
            obj.svgElement.children[0].classList.remove('opacity-5');
            obj.svgElement.children[0].classList.add('opacity-40');

          });
        } else {
          obj.svgElement.children[0].addEventListener("mouseover", () => {
            obj.svgElement.children[0].classList.remove('opacity-80');
            obj.svgElement.children[0].classList.add('opacity-100');
          });

          obj.svgElement.children[0].addEventListener("mouseleave", () => {
            obj.svgElement.children[0].classList.remove('opacity-100');
            obj.svgElement.children[0].classList.add('opacity-80');

          });
        }
      });
    }
  });
}

const bedroomRange = () => {
  const unitTypes = Store.buildingData[buildingId.value].uniqueBedrooms || Store.buildingData[buildingId.value].uniqueUnitPlans;
  if (!unitTypes) {
    return '';
  }
  const bedroomNumbers = unitTypes
    .map((type) => parseInt(type, 10))
    .filter(Boolean)
    .sort((a, b) => a - b);

  if (bedroomNumbers.length === 0) {
    return '';
  }
  if (bedroomNumbers.length === 1) {
    return `${bedroomNumbers[0]} BR`;
  }

  const rangeString = bedroomNumbers.slice(0, -1).join(', ');
  const lastNumber = bedroomNumbers.at(-1);

  return `${rangeString} & ${lastNumber} BR`;
};

watch(() => Store.currentZoomlevel, () => {
  setActiveElem(towerSVGElem.value, Store.currentZoomlevel);
});
// function closeModal () {
//   show_modal.value = false;
// }

// onMounted(() => {
//   onClickOutside(pictureCardRef, closeModal);
// });

function toggleOpacity (layer_id, isIncreaseOpacity) {
  var svgGroup = document.getElementById(layer_id);
  if (isIncreaseOpacity){
    // Add
    svgGroup.classList.remove("opacity-70");
    svgGroup.classList.add("opacity-100");
  } else {
    // Remove
    svgGroup.classList.remove("opacity-100");
    svgGroup.classList.add("opacity-90");
  }
}

watch(() => Store.svgVisibility.showSVG, (isVisible) => {
  if (props.sceneType !== 'deep_zoom') {
    return;
  }

  Object.values(props.layerData).forEach((layer) => {
    if (towerSVGElem.value[layer.layer_data.layer_id]) {
      const towerElement = towerSVGElem.value[layer.layer_data.layer_id].g;
      if (isVisible) {
        towerElement.classList.add('!visible');
      } else {
        towerElement.classList.remove('!hidden');
      }
    }
  });
});

if (props.sceneType === "deep_zoom"){
  window.viewer.addHandler('zoom', function () {
    if (isZoomingAfterClick) {
      return; // Do not reset state if zoom was triggered by a click
    }
    show_modal.value = false;
    clickedLayerId.value  = false;
    communityId.value = null;
    buildingId.value = null;
    sceneId.value = null;
    layerId.value = null;
  });
}

onClickOutside(pictureCardRef, (event) => {
  if (props.sceneType !== 'deep_zoom'){
    show_modal.value = false;
    clickedLayerId.value  = false;
  } else {
    if (show_modal.value && event.target.tagName === 'DIV'){
      console.log('condition working for this');
      show_modal.value = false;
      clickedLayerId.value  = false;
      layerId.value = null;
      communityId.value = null;
      buildingId.value=null;
      sceneId.value = null;
    }
  }
});
</script>
<template>
  <!-- eslint-disable vue/no-v-html -->
  <template v-if="Store.svgVisibility.showSVG">
    <g
      v-for="layer, id, LayerIndex in layerData"
      v-show="sceneType !== 'deep_zoom'"
      :id="id"
      :key="layer"
      class="opacity-70 hover:opacity-100 cursor-pointer group"
      @click="(event) => { placeModal(layer.layer_data.layer_id,layer.layer_data.building_id,layer.layer_data.community_id,layer.layer_data.scene_id,event);
                           toggleOpacity(layer.layer_data.layer_id, true); } "
      @mouseleave="(event) => toggleOpacity(layer.layer_data.layer_id,false)"
    >
      <g v-if="layer.layer_data.showLabel">
        <g
          :ref="(el) => setTowerRef(el, LayerIndex)"
          :label="Store.buildingData[layer.layer_data.building_id]?.name"
          class=" opacity-40 hover:opacity-5 hover:fill-grey cursor-pointer'"
          :class="sceneType !=='deep_zoom' ? layer.layer.getAttribute('class') + ' ' + layer.layer_data.type:''"
          v-html="layer.layer.innerHTML"
        />

        <foreignObject
          v-if="towerRef[LayerIndex] && Store.buildingData[layer.layer_data.building_id]?.name"
          :key="`foreign-${LayerIndex}`"
          :x="towerRef[LayerIndex].getBBox().x + (towerRef[LayerIndex].getBBox().width - 140) / 2"
          :y="towerRef[LayerIndex].getBBox().y + (towerRef[LayerIndex].getBBox().height - 120) / 2"
          :width="170"
          :height="240"
          class="lg:block building pointer-events-none bg-transparent relative group:hover:hidden "
        >
          <div class="h-full w-full">
            <span class=" z-10 w-full h-full flex flex-col  gap-4 items-center">
              <div class="w-[100px] h-[70px] grid place-items-center">
                <div class="w-[35px] h-[35px] rounded-[50%] grid  place-items-center animation">
                  <div class="w-[35px] h-[35px] bg-secondary rounded-[50%] grid  place-items-center">
                    <div class="w-[16px] h-[16px] bg-primary rounded-[50%]" />
                  </div>
                </div>
              </div>
              <div
                class="uppercase h-fit text-wrap max-w-[150px] font-medium px-2 py-2 !rounded bg-secondary text-secondaryText inline-block whitespace-normal break-words overflow-hidden text-center"
              >
                {{ towerRef[LayerIndex]?.getAttribute('label') }}
              </div>
            </span>
          </div>
        </foreignObject>
      </g>

      <g
        v-else
        v-show="sceneType!=='deep_zoom'"
        :id="layer.layer_data.layer_id"
        :key="`layer-${LayerIndex}`"
        :class="sceneType!=='deep_zoom'?[layer.layer.getAttribute('class') +' '+layer.layer_data.type+' opacity-80 hover:opacity-100 hover:stroke-[4px] cursor-pointer']:''"
        v-html="layer.layer.innerHTML"
      />
    </g>
  </template>

  <!-- eslint-enable -->
  <portal to="building">
    <div
      v-if="show_modal"
      ref="pictureCardRef"
      :class="Store.isMobile ? '!w-full !absolute !bottom-0 z-[10]' :
        Store.isLandscape && Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0 ? '!bottom-2 !w-fit !absolute !z-[10] !right-20' :
        Store.isLandscape? '!bottom-2 !right-4 !w-fit !absolute !z-[10]' : 'w-fit absolute !top-20 !right-7 z-[10]'"
    >
      <PictureCard
        :imageUrl="cdn(Store.buildingData[buildingId].thumbnail)"
        :title="Store.buildingData[buildingId].name"
        :location="Store.projectCardData[projectId].name ?? ''"
        :bedrooms="Store.buildingData[buildingId].uniqueBedrooms || Store.buildingData[buildingId].uniqueUnitPlans ? bedroomRange() : ''"
        :units="Store.buildingData[buildingId].totalUnits + ' Units'"
        :type="Store.buildingData[buildingId].category"
        :totalFloors="Store.buildingData[buildingId].floors"
        :towerLayer="true"
        :communityLayer="false"
        :showExplore="Store.buildingData[buildingId].unitStatus.Available > 0 ? true : false"
        :status="Store.buildingData[buildingId].unitStatus.Available > 0 ? '':availUnitOrgs.includes(route.params.organizationId) ? 'onreq' : 'soldout'"
        @explore="moveTonxtScene"
        @showunits="router.push({ name: 'unitplansviewer' })"
      />
    </div>
  </portal>
</template>

<style scoped>

.animation {
  animation: ripple 1.1s linear infinite;
}

/* @keyframes ripple {
  0% {
    box-shadow: 0 0 0 0rem var(--secondaryText);
  }
  100% {
    box-shadow: 0 0 5px .3rem var(--secondaryText),
                0 0 10px .5rem var(--secondaryText),
                0 0 20px .6rem rgba(255, 255, 255, 0.4);
  }
} */
@keyframes ripple {
  0% {
    height: 35px;
    width: 35px;
    background-color: var(--secondaryText);
  }
  50% {
    height: 55px;
    width: 55px;
    background-color: var(--secondaryText);
  }
  100% {
    height: 70px;
    width: 70px;
    background-color:rgba(255, 255, 255, 0);
  }
}

/* 0 0 0 1.4rem color-mix(in srgb, var(--primary) 35%, transparent); */
</style>
