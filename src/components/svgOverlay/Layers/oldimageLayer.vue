<script setup>
import { defineProps, ref, onMounted, defineEmits, watch } from 'vue';
import { loadImageData, cdn } from '../../../helpers/helper';
const props = defineProps({highres: {type: String, default: ""}, lowres: {type: String, default: ""}, video: {type: String, default: ""}});
const emit = defineEmits(['lowresLoaded', 'highresLoaded', 'progressData']);
const lowResFlag = ref(true), lowRes=ref(), highRes=ref(false), videoUrl = ref();
// HighRes.value =cdn(props.highres);
videoUrl.value=cdn(props.video);
function lowResLoaded (){
  setTimeout(() => {
    lowResFlag.value = false;
  }, 1000);
}

onMounted(() => {
  loadImageData(cdn(props.lowres), (progress) => emit('progressData', (progress/2)))
    .then((data) => {
      lowResFlag.value = false;
      lowRes.value = data;
      lowResFlag.value = false;
      emit('lowresLoaded');
      if (props.highres) {
        loadImageData(cdn(props.highres), (progress) => emit('progressData', 50+(progress/2)), () => emit('progressData', false))
          .then((data) => {
            highRes.value = data;
            emit('highresLoaded');
          });
      }
    });
});
watch(() => props.highres, () => {
  if (props.highres) {
    if (!highRes.value) {
      loadImageData(cdn(props.highres), (progress) => emit('progressData', 50+(progress/2)), () => emit('progressData', false))
        .then((data) => {
          highRes.value = data;
          emit('highresLoaded');
        });
    }
  }
});
</script>
<template>
  <image
    v-if="lowResFlag || highRes==false"
    id="svg_lowres_image"
    style="transform-origin: center center;transition: transform 2s opacity 0.1s;object-fit:contain;height:100%;width:100%"
    :xlink:href="lowRes"
    @load="lowResLoaded"
  />
  <image
    v-if="!props.video && highRes"
    id="svg_highres_image"
    style="transform-origin: center center;transition: transform 2s, opacity 0.1s;object-fit:cover;"
    :xlink:href="highRes || lowRes"
    @load="highResLoaded"
  />
  <foreignObject
    v-if="cdn(props.video)"
    style="position: absolute; top:0; left:0; width:100%; height:100%; object-fit: contain;"
  >
    <video
      :poster="cdn(lowres)"
      style="position: absolute; top:0; left:0; width:100%; height:100%; object-fit: contain;"
      muted
      autoplay
      loop
    >
      <source :src="videoUrl">
    </video>
  </foreignObject>
</template>
<style scoped>

</style>
