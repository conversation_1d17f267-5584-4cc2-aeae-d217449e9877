<script setup>
import {defineProps, ref, onMounted, watch} from 'vue';
import router from '../../../router';
import { useRoute } from 'vue-router';
import { addSVGDeepZoom, setActiveElem, cdn } from '../../../helpers/helper';
import {creationToolStore} from '../../../store/index';
import OpenSeadragon from 'openseadragon';
import { fetchVideo } from '../../../helpers/API';
const Store = creationToolStore();
const route = useRoute();
const props = defineProps({layerData: {
  type: Object,
  default () {
    return {};
  },
},
sceneType: {
  type: String,
  default: '',
},
});

fetchVideo(props.layerData, cdn(Store.SceneData[route.params.sceneId].sceneData.background?.low_resolution));

const clickedId=ref(false), pinSVGElem = ref({});
async function moveToNxtScene (id){
  clickedId.value=id;
  if (props.layerData[id].layer_data.video_tag){
    Store.currentSceneVideo=cdn(props.layerData[id].layer_data.video_tag);
    Store.currentSceneVideoThumb=cdn(Store.SceneData[route.params.sceneId].sceneData.background.low_resolution);
    window.asd = function (){
      if (route.fullPath.includes('masterscene')) {
        router.push({ name: 'masterScene', params: {sceneId: props.layerData[id].layer_data.scene_id}});
      } else {
        router.push({ name: 'projectScene', params: {sceneId: props.layerData[id].layer_data.scene_id}});
      }
    };
  } else {

    if (route.fullPath.includes('masterscene')) {
      router.push({ name: 'masterScene', params: {sceneId: props.layerData[id].layer_data.scene_id}});
    } else {
      router.push({ name: 'projectScene', params: {sceneId: props.layerData[id].layer_data.scene_id}});
    }
  }
}
if (props.sceneType==='deep_zoom'){
  onMounted(() => {
    if (Object.values(props.layerData).length > 0) {
      Object.values(props.layerData).forEach(async (item) => {
        const requestOptions = {
          method: "GET",
          redirect: "follow",
        };
        const response = await fetch(cdn(item.layer), requestOptions);
        const svgString = await response.text();
        const obj = addSVGDeepZoom({
          g: svgString,
          zIndex: item.layer_data.zIndex,
          reSize: item.layer_data.reSize,
          x: item.layer_data.x,
          y: item.layer_data.y,
          width: item.layer_data.width,
          height: item.layer_data.height,
          placement: item.layer_data.placement,
          layer_id: item.layer_data.layer_id,
          class: " pin",
        }, window.viewer);

        obj.svgElement.style.cursor= 'pointer';
        pinSVGElem.value[item.layer_data.layer_id] = { 'g': obj.svgElement, 'minZoomLevel': item.layer_data.minZoomLevel, 'maxZoomLevel': item.layer_data.maxZoomLevel };
        if (Store.svgVisibility.showSVG) {
          obj.svgElement.classList.remove('!hidden');
        } else {
          obj.svgElement.classList.add('!hidden');
        }
        obj.svgElement.addEventListener("mouseleave", () =>
          obj.svgElement.classList.remove('opacity-100'),
        );
        obj.svgElement.addEventListener("mouseenter", () =>
          obj.svgElement.classList.add('opacity-100'),
        );
        new OpenSeadragon.MouseTracker({
          element: obj.svgElement,
          clickHandler: function () {
            moveToNxtScene(item.layer_data.layer_id);
          },
        });
        if (item.layer_data.minZoomLevel  && item.layer_data.maxZoomLevel){
          if (Store.currentZoomlevel >= item.layer_data.minZoomLevel
          && Store.currentZoomlevel<= item.layer_data.maxZoomLevel){
            obj.svgElement.classList.add('!visible');
            obj.svgElement.classList.remove('!hidden');
          } else {
            obj.svgElement.classList.remove('!visible');
            obj.svgElement.classList.add('!hidden');
          }
        }
      });
    }
  });
}

watch(() => Store.svgVisibility.showSVG, (isVisible) => {
  if (props.sceneType !== 'deep_zoom') {
    return;
  }

  Object.values(props.layerData).forEach((layer) => {
    if (pinSVGElem.value[layer.layer_data.layer_id]) {
      const pinElement = pinSVGElem.value[layer.layer_data.layer_id].g;
      if (isVisible) {
        pinElement.classList.remove('!hidden');
      } else {
        pinElement.classList.add('!hidden');
      }
    }
  });
});
watch(() => Store.currentZoomlevel, () => {
  setActiveElem(pinSVGElem.value, Store.currentZoomlevel);
});
</script>
<template>
  <!-- eslint-disable vue/no-v-html -->
  <template v-if="Store.svgVisibility.showSVG">
    <g
      v-for="(layer,index) in layerData"
      :id="layer.layer_data.layer_id"
      :key="index"
      :class="props.sceneType!=='deep_zoom'?[
        'cursor-pointer !opacity-100' ,
        'hover:opacity-100 pin',
        layer.layer.getAttribute('class') +' '+layer.layer_data.type,
        clickedId==layer.layer_data.layer_id && clickedId!=false?'fill-white':'fill-black'
      ]:''"
      @click="moveToNxtScene(layer.layer_data.layer_id)"
      v-html="layer.layer.innerHTML"
    />
  </template>
  <!-- cant show with Opacity because of the Deepzoom Pin Opacity -->
  <!--eslint-enable-->
  <portal to="pin">
    <div />
  </portal>
</template>
<style scoped>

</style>
