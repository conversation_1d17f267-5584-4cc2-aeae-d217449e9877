<script setup>
import {defineProps, ref, onMounted, watch} from 'vue';
import router from '../../../router';
import { useRoute } from 'vue-router';
import { addSVGDeepZoom, setActiveElem, cdn, getBedroomRange, getUnitType } from '../../../helpers/helper';
import {creationToolStore} from '../../../store/index';
import OpenSeadragon from 'openseadragon';
import PictureCard from '../../ALEComponents/PictureCard/PictureCard.vue';
import { cardPosition } from '../../../helpers/cardPosition';
// import ProjectsCategory from '../../ALEComponents/ProjectsCategory/ProjectsCategory.vue';
import { onClickOutside } from '@vueuse/core';

const Store = creationToolStore();
const route = useRoute();
const props = defineProps({layerData: {
  type: Object,
  default () {
    return {};
  },
},
sceneType: {
  type: String,
  default: '',
},
});
const clickedId=ref(false), pinSVGElem = ref({}), showPictureCard = ref(false), projectId=ref(false), position= ref(), arrayResult = ref([]), temp=ref();
const card = ref(), picturecard = ref(), flag = ref(), flag1 = ref();

// function moveToNxtScene (id){
//   clickedId.value=id;
//   // Store.currentSceneVideo="/assets/z222.mp4";
//   // Store.currentSceneVideoThumb=Store.SceneData[route.params.sceneId].sceneData.background["high_resolution"]
//   if (props.layerData[id].layer_data.videoTag){
//     Store.currentSceneVideo=props.layerData[id].layer_data.videoTag;
//     Store.currentSceneVideoThumb=Store.SceneData[route.params.sceneId].sceneData.background.high_resolution;
//     setTimeout(() => {
//       router.push({ name: 'projectScene', params: {projectId: props.layerData[id].layer_data.project_id, sceneId: props.layerData[id].layer_data.scene_id}});
//     }, 50);
//   } else {
//     router.push({ name: 'projectScene', params: {projectId: props.layerData[id].layer_data.project_id, sceneId: props.layerData[id].layer_data.scene_id}});
//   }
// }

if (Object.keys(Store.projectCardData).length === 0){
  Store.getListofProjects(route.params.organizationId).then((res) => {
    Store.projectCardData = res.data;
    arrayResult.value = Object.values(res.data);
  });
} else {
  arrayResult.value = Object.values(Store.projectCardData);
}

function placeModal (layer_id, project_id, event = null) {
  if (!card.value) {
    return;
  }
  // avoid openning card if same layer is clicked
  if (temp.value && temp.value === layer_id && props.sceneType === 'deep_zoom'){
    showPictureCard.value = false;
    temp.value = null;
    flag.value = false;
    return;
  }
  // Find the specific element matching the layer_id
  const targetElement = Array.from(card.value).find((element) => element.id === layer_id);
  if (!targetElement) {
    return;
  }
  // get the position of the target layer
  const rect = targetElement.getBoundingClientRect();
  const offset = 20;
  let newPosition;
  if (event) {
    // Positioning using cursor event
    const cursorX = event.clientX;
    const cursorY = event.clientY;
    const masterview = true;
    newPosition = cardPosition(rect, cursorX, cursorY, rect.width, rect.height, masterview);
  } else {
    // using rect for deep_zoom scenes
    // Positioning using rect
    newPosition = {
      x: rect.right + offset,
      y: rect.top + (rect.height / 2) - (rect.height / 2),
    };
  }
  position.value = newPosition;
  temp.value = layer_id;
  clickedId.value = layer_id;
  projectId.value = project_id;
  showPictureCard.value = true;
  flag1.value = false;
}
if (props.sceneType==='deep_zoom'){
  onMounted(() => {
    if (Object.values(props.layerData).length > 0) {
      Object.values(props.layerData).forEach(async (item) => {
        const requestOptions = {
          method: "GET",
          redirect: "follow",
        };
        const response = await fetch(cdn(item.layer), requestOptions);
        const svgString = await response.text();
        const obj = addSVGDeepZoom({
          g: svgString,
          zIndex: item.layer_data.zIndex,
          reSize: item.layer_data.reSize,
          x: item.layer_data.x,
          y: item.layer_data.y,
          width: item.layer_data.width,
          height: item.layer_data.height,
          placement: item.layer_data.placement,
          layer_id: item.layer_data.layer_id,
        }, window.viewer);

        obj.svgElement.style.cursor= 'pointer';
        pinSVGElem.value[item.layer_data.layer_id] = { 'g': obj.svgElement, 'minZoomLevel': item.layer_data.minZoomLevel, 'maxZoomLevel': item.layer_data.maxZoomLevel };
        if (Store.svgVisibility.showSVG) {
          obj.svgElement.classList.remove('!hidden');
        } else {
          obj.svgElement.classList.add('!hidden');
        }
        obj.svgElement.addEventListener("mouseleave", () =>
          obj.svgElement.classList.remove('opacity-100'),
        );
        obj.svgElement.addEventListener("mouseenter", () =>
          obj.svgElement.classList.add('opacity-100'),
        );

        new OpenSeadragon.MouseTracker({
          element: obj.svgElement,
          clickHandler: function () {
            console.log("inside mousetracker", item.layer_data.layer_id !== clickedId.value);
            if (item.layer_data.layer_id !== clickedId.value) {
              flag.value = true;
              flag1.value = true;
              console.log("obj.svg", obj.svgElement.children[0]);
              clickedId.value = item.layer_data.layer_id;
              const viewport = window.viewer.viewport;
              const viewerRect = window.viewer.element.getBoundingClientRect();
              const overlayRect = obj.svgElement.getBoundingClientRect(); // Get the element position and sizings

              // Calculate the center of the overlay in screen coordinates
              const overlayCenterX = overlayRect.left + (overlayRect.width / 2); // X
              const overlayCenterY = overlayRect.top + (overlayRect.height / 2); // Y

              // Convert the screen coordinates to viewport coordinates
              const viewportPoint = viewport.pointFromPixel(
                new OpenSeadragon.Point(
                  overlayCenterX - viewerRect.left,
                  overlayCenterY - viewerRect.top,
                ),
              );
              // // Get the current center of the viewport
              // const currentViewportCenter = viewport.getCenter();
              // // Round the x and y values of both points to one decimal place for comparison
              // const isSamePoint = (Math.round(currentViewportCenter.x * 10) / 10 === Math.round(viewportPoint.x * 10) / 10) &&
              //   (Math.round(currentViewportCenter.y * 10) / 10 === Math.round(viewportPoint.y * 10) / 10);

              //   console.log("center point",isSamePoint)
              // Check if the clicked point is already at the center of the viewport
              // if (isSamePoint) {
              //   if (Store.currentZoomlevel < 2.5) {
              //     const zoomLevel = Math.round(viewport.getZoom()) * 2.5; // Example: zoom in by a factor of 2
              //     // Zoom in to the calculated viewport point
              //     viewport.zoomTo(zoomLevel, viewportPoint);
              //   } else {
              //     // obj.svgElement.classList.remove ('!fill-[#ffffff00]');
              //     // obj.svgElement.classList.remove(getUnitData(item.layer_data.bedrooms));
              //     // svgElement.classList.add('!hidden')
              //   }
              // }

              // Check if zooming is required
              if (Store.currentZoomlevel < 2.5) {
                console.log("zoomed in");
                projectId.value = item.layer_data.project_id;
                // Define the zoom level you want to zoom into
                const zoomLevel = Math.round(viewport.getZoom()) * 2.5; // Example: zoom in by a factor of 2
                // Zoom in to the calculated viewport point
                viewport.zoomTo(zoomLevel, viewportPoint);
                viewport.panTo(viewportPoint);
                viewport.applyConstraints();
              } else {
                projectId.value = item.layer_data.project_id;
                console.log("centered view");
                // Pan to the clicked point
                viewport.panTo(viewportPoint);
                // Constrain the bounds to prevent panning beyond the image
                viewport.applyConstraints();
              }
              window.viewer.addOnceHandler('animation-finish', function () {
                placeModal(item.layer_data.layer_id, item.layer_data.project_id);
              });

              if (flag.value){
                obj.svgElement.children[0].style.opacity = "1";
                Array.from(document.getElementsByClassName('openseadragon')).forEach((otherChild) => {
                  if (otherChild.children[0].tagName === 'g' && otherChild.children[0] !== obj.svgElement.children[0]){
                    otherChild.children[0].style.opacity = "0.4";
                    otherChild.children[0].style.transitionDuration = "0.4s";
                  }
                });
              }
            }
          },
        });
        obj.svgElement.children[0].addEventListener("mouseover", () => {
          // console.log("mouseover", projectId.value && item.layer_data.project_id === projectId.value);
          if (projectId.value){
            if (item.layer_data.project_id === projectId.value){
              obj.svgElement.children[0].style.opacity = "1";
              Array.from(document.getElementsByClassName('openseadragon')).forEach((otherChild) => {
                if (otherChild.children[0].tagName === 'g' && otherChild.children[0] !== obj.svgElement.children[0]){
                  otherChild.children[0].style.opacity = "0.4";
                  otherChild.children[0].style.transitionDuration = "0.4s";
                }
              });
            }
          } else {
            if (!flag1.value){
              obj.svgElement.children[0].style.opacity = "1";
              Array.from(document.getElementsByClassName('openseadragon')).forEach((otherChild) => {
                if (otherChild.children[0].tagName === 'g' && otherChild.children[0] !== obj.svgElement.children[0]){
                  otherChild.children[0].style.opacity = "0.4";
                  otherChild.children[0].style.transitionDuration = "0.4s";
                }
              });
            }

          }
        });

        obj.svgElement.children[0].addEventListener("mouseleave", () => {
          // console.log("mouseleave", projectId.value || item.layer_data.project_id === projectId.value);
          if (projectId.value){
            // if (item.layer_data.project_id !== projectId.value){
            //   // Array.from(document.getElementsByClassName('openseadragon')).forEach((otherChild) => {
            //   //   if (otherChild.children[0].tagName === 'g'){
            //   //     otherChild.children[0].style.opacity = "1";
            //   //     otherChild.children[0].style.transitionDuration = "0.4s";
            //   //   }
            //   // });
            // }
          } else {
            if (!flag1.value){
              Array.from(document.getElementsByClassName('openseadragon')).forEach((otherChild) => {
                if (otherChild.children[0].tagName === 'g'){
                  otherChild.children[0].style.opacity = "1";
                  otherChild.children[0].style.transitionDuration = "0.4s";
                }
              });
            }
          }
        });

        if (item.layer_data.minZoomLevel  && item.layer_data.maxZoomLevel){
          if (Store.currentZoomlevel >= item.layer_data.minZoomLevel
          && Store.currentZoomlevel<= item.layer_data.maxZoomLevel){
            obj.svgElement.classList.add('!visible');
            obj.svgElement.classList.remove('!hidden');
          } else {
            obj.svgElement.classList.remove('!visible');
            obj.svgElement.classList.add('!hidden');
          }
        }
      });
    }
  });
}

watch(() => Store.currentZoomlevel, () => {
  setActiveElem(pinSVGElem.value, Store.currentZoomlevel);
});

watch(() => Store.svgVisibility.showSVG, (isVisible) => {
  if (props.sceneType !== 'deep_zoom') {
    return;
  }
  Object.values(pinSVGElem.value).forEach((elem) => {
    if (elem && elem.g) {
      if (isVisible) {
        elem.g.classList.remove('!hidden');
      } else {
        elem.g.classList.add('!hidden');
      }
    }
  });
});

const handleGoToProject = (project_id) => {
  router.push({ name: "project", params: { projectId: project_id} });
};

// const selectedProject = ref();

// function getLayerIdByProjectId (projectId) {
//   for (const key in props.layerData) {
//     if (props.layerData[key]?.layer_data?.project_id === projectId) {
//       return props.layerData[key]?.layer_data?.layer_id;
//     }
//   }
//   return null; // Return null if no match is found
// }

// const handleImageClick = (id) => {
//   selectedProject.value = id;
//   clickedId.value = getLayerIdByProjectId(id);
//   placeModal(clickedId.value, id);
// };

watch(() => {
  projectId.value;
});

window.addEventListener('resize', Store.callbackFunctionMonitorChanges);
Store.callbackFunctionMonitorChanges();
onMounted(() => {
  onClickOutside(picturecard, (event) => {
    if (props.sceneType !== 'deep_zoom'){
      showPictureCard.value = false;
      clickedId.value = null;
    } else {
      // for deep_zoom avoid clicking on layers to hide card
      // this to hide card if the same layer is clicked that functionality is in placeModal
      if (!Object.keys(props.layerData).includes(event.target.id)){
        if (showPictureCard.value){
          showPictureCard.value = false;
          clickedId.value = null;
          temp.value = null;
          projectId.value = false;
          Array.from(document.getElementsByClassName('openseadragon')).forEach((otherChild) => {
            if (otherChild.children[0].tagName === 'g'){
              otherChild.children[0].style.opacity = "1";
              otherChild.children[0].style.transitionDuration = "0.4s";
            }
          });
        }
      }
    }
  });
});
if (props.sceneType==="deep_zoom"){
  window.viewer.addHandler('zoom', function () {
    console.log("card closed");
    showPictureCard.value = false;
    clickedId.value = false;
    temp.value = false;
    projectId.value = false;
    Array.from(document.getElementsByClassName('openseadragon')).forEach((otherChild) => {
      if (otherChild.children[0].tagName === 'g'){
        otherChild.children[0].style.opacity = "1";
        otherChild.children[0].style.transitionDuration = "0.4s";
      }
    });
  });
}
</script>
<template>
  <!-- eslint-disable vue/no-v-html -->
  <g
    v-for="(layer,index) in layerData"
    :id="layer.layer_data.layer_id"
    :key="index"
    ref="card"
    :class="props.sceneType!=='deep_zoom'?[
      'cursor-pointer opacity-100' ,
      'hover:opacity-100 ',
      layer.layer.getAttribute('class') +' '+layer.layer_data.type,
      clickedId==layer.layer_data.layer_id && clickedId!=false?'':'fill-black'
    ]:''"
    @click="(event) => {placeModal(layer.layer_data.layer_id,layer.layer_data.project_id, event)}"
    v-html="layer.layer.innerHTML"
  />
  <!--eslint-enable-->
  <portal to="project">
    <div
      v-if="showPictureCard"
      ref="picturecard"
      class="flex fixed z-[10]"
      :class="Store.isMobile ? '!w-full slide-in-blurred-bottom bottom-0' : Store.isLandscape ? 'top-14 right-4 absolute': 'top-14 right-10 absolute'"
    >
      <PictureCard
        class="!z-[10] fixed"
        :class="Store.isMobile?'left-0': Store.isLandscape ? ' -bottom-16 !right-0':'top-[20px]  right-[40px]'"
        :title="Store.projectCardData[projectId].name"
        :location="Store.projectCardData[projectId].city"
        :bedrooms="getBedroomRange(Store.projectCardData[projectId]?.units?.unitDetails?.uniqueBedroomTypes)"
        :type="getUnitType(Store.projectCardData[projectId]?.units?.unitPlanTypeCounts)"
        :currencyTitle="Store.projectCardData[projectId]?.units?.unitDetails?.currencyType"
        :price="Store.projectCardData[projectId]?.units?.unitDetails?.minPrice"
        :imageUrl="cdn(Store.projectCardData[projectId].project_thumbnail)"
        :isMasterScene="true"
        :showExplore="true"
        :towerLayer="false"
        @explore="handleGoToProject(projectId)"
      />
    </div>
    // <div v-if="!Store.isMobile">
      //   <ProjectsCategory
        :key="arrayResult"
        :data="arrayResult"
        :projectsCategory="projectsCategory"
        :scrollIntoView="show_modal"
        :selectedProjectId="projectId"
        @selected-project="handleImageClick"
      />
      //
    </div>

    <div />
  </portal>
</template>
<style scoped>
</style>
