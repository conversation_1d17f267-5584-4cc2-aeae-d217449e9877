<!-- groupedUnitsLayer.vue -->
<script setup>
import {defineProps, ref, onMounted, watch} from 'vue';
import { useRoute } from 'vue-router';
import { addSVGDeepZoom, setActiveElem, cdn } from '../../../helpers/helper';
import {creationToolStore} from '../../../store/index';
import OpenSeadragon from 'openseadragon';
import { fetchVideo } from '../../../helpers/API';

const Store = creationToolStore();
const route = useRoute();
const props = defineProps({
  layerData: {
    type: Object,
    default () {
      return {};
    },
  },
  sceneType: {
    type: String,
    default: '',
  },
});

const clickedId = ref(null);
const groupSVGElem = ref({});

fetchVideo(props.layerData, cdn(Store.SceneData[route.params.sceneId].sceneData.background?.low_resolution));
console.log("props", props.sceneType);
// Enhanced click handler for deep zoom functionality
function handleSVGClick (event) {
  if (props.sceneType === 'deep_zoom') {
    event.preventDefault();
    event.stopPropagation();
    return;
  }
}

function setSVG () {
  if (Object.values(props.layerData).length > 0) {
    Object.values(props.layerData).forEach(async (item) => {

      const requestOptions = {
        method: "GET",
        redirect: "follow",
      };
      const response = await fetch(cdn(item.layer), requestOptions);
      const svgString = await response.text();
      const obj = addSVGDeepZoom({
        g: svgString,
        zIndex: item.layer_data.zIndex,
        reSize: item.layer_data.reSize,
        x: item.layer_data.x,
        y: item.layer_data.y,
        width: item.layer_data.width,
        height: item.layer_data.height,
        placement: item.layer_data.placement,
        layer_id: item.layer_data.layer_id,
      }, window.viewer);

      obj.svgElement.children[0].style.cursor= 'pointer';

      groupSVGElem.value[item.layer_data.layer_id] = {
        'g': obj.svgElement.children[0],
        'minZoomLevel': item.layer_data.minZoomLevel,
        'maxZoomLevel': item.layer_data.maxZoomLevel,
      };

      new OpenSeadragon.MouseTracker({
        element: obj.svgElement.children[0],
        clickHandler: function () {

          if (item.layer_data.layer_id !== clickedId.value) {
            clickedId.value = item.layer_data.layer_id;
            const viewport = window.viewer.viewport;
            const viewerRect = window.viewer.element.getBoundingClientRect();
            const overlayRect = obj.svgElement.getBoundingClientRect(); // Get the element position and sizings

            // Calculate the center of the overlay in screen coordinates
            const overlayCenterX = overlayRect.left + (overlayRect.width / 2); // X
            const overlayCenterY = overlayRect.top + (overlayRect.height / 2); // Y

            // Convert the screen coordinates to viewport coordinates
            const viewportPoint = viewport.pointFromPixel(
              new OpenSeadragon.Point(
                overlayCenterX - viewerRect.left,
                overlayCenterY - viewerRect.top,
              ),
            );
            // // Get the current center of the viewport
            // const currentViewportCenter = viewport.getCenter();
            // // Round the x and y values of both points to one decimal place for comparison
            // const isSamePoint = (Math.round(currentViewportCenter.x * 10) / 10 === Math.round(viewportPoint.x * 10) / 10) &&
            //   (Math.round(currentViewportCenter.y * 10) / 10 === Math.round(viewportPoint.y * 10) / 10);

            //   console.log("center point",isSamePoint)
            // // Check if the clicked point is already at the center of the viewport
            // if (isSamePoint) {
            //   if (Store.currentZoomlevel < 2.5) {
            //     const zoomLevel = Math.round(viewport.getZoom()) * 2.5; // Example: zoom in by a factor of 2
            //     // Zoom in to the calculated viewport point
            //     viewport.zoomTo(zoomLevel, viewportPoint);
            //   } else {
            //     // obj.svgElement.classList.remove ('!fill-[#ffffff00]');
            //     // obj.svgElement.classList.remove(getUnitData(item.layer_data.bedrooms));
            //     // svgElement.classList.add('!hidden')
            //   }
            // }
            // Check if zooming is required
            clickedId.value = null;
            if (Store.currentZoomlevel < 2.5) {
              // Define the zoom level you want to zoom into
              const zoomLevel = Math.round(viewport.getZoom()) * 2.5; // Example: zoom in by a factor of 2
              // Zoom in to the calculated viewport point
              viewport.zoomTo(zoomLevel, viewportPoint);
              // viewport.panTo(viewportPoint);
              viewport.applyConstraints();
            } else {
              // Pan to the clicked point
              viewport.panTo(viewportPoint);
              // Constrain the bounds to prevent panning beyond the image
              viewport.applyConstraints();
            }
          }
        },
      });
      // Hover effects for main SVG element
      obj.svgElement.children[0].addEventListener("mouseover", () => {
        obj.svgElement.children[0].style.opacity = "1";
        Array.from(document.getElementsByClassName('openseadragon')).forEach((otherChild) => {
          if (otherChild.children[0].tagName === 'g' && otherChild.children[0] !== obj.svgElement.children[0]){
            otherChild.children[0].style.opacity = "0.4";
            otherChild.children[0].style.transitionDuration = "0.4s";
          }
        });
      },
      );
      obj.svgElement.children[0].addEventListener("mouseleave", () => {
        Array.from(document.getElementsByClassName('openseadragon')).forEach((otherChild) => {
          if (otherChild.children[0].tagName === 'g'){
            otherChild.children[0].style.opacity = "1";
            otherChild.children[0].style.transitionDuration = "0.4s";
          }
        });
      });
    });
  }
}

onMounted(() => {
  if (props.sceneType === 'deep_zoom') {
    setSVG();
  }
});
watch(() => Store.currentZoomlevel, () => {
  setActiveElem(groupSVGElem.value, Store.currentZoomlevel);
});
</script>

<template>
  <!-- eslint-disable vue/no-v-html -->
  <g
    v-for="(layer,index) in layerData"
    :id="layer.layer_data.layer_id"
    :key="index"
    class="pointer-events-none"
    @click="handleSVGClick($event)"
  />
  <!--eslint-enable-->
  <portal to="zoom_target">
    <div />
  </portal>
</template>

<style scoped>
</style>
