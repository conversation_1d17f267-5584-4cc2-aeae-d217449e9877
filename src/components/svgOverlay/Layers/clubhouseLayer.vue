<script setup>
import { ref, defineProps } from 'vue';
import AmenityCard from '../../ALEComponents/AmenityCard/AmenityCard.vue';
import router from '../../../router';
import { creationToolStore } from '../../../store/index';
const Store = creationToolStore();

defineProps({layerData: {
  type: Object,
  default () {
    return {};
  },
}});
const show_modal = ref(false);
const position = ref(false), clubhouse_id = ref();
function placeModal (layer_id, clubhouseid) {
  var svgGroup = document.getElementById(layer_id);
  var rect = svgGroup.getBoundingClientRect();
  clubhouse_id.value = clubhouseid;
  router.push({ name: "projectScene", query: { clubhouse_id: clubhouseid } });
  position.value = { x: rect.x, y: rect.y + 30 };
  show_modal.value = true;
}
function closeModal (e) {
  if (e.target.getAttribute("clickaway") === "true") {
    show_modal.value = false;
    router.push({ name: "projectScene" });
  }
}
</script>
<template>
  <!-- eslint-disable vue/no-v-html -->
  <g
    v-for="layer in layerData"
    :id="layer.layer_data.layer_id"
    :key="layer"
    :class="layer.layer.getAttribute('class') + ' ' + layer.layer_data.type + ' opacity-100'"
    @click="(event) => placeModal(layer.layer_data.layer_id, layer.layer_data.clubhouse_id, event)"
    v-html="layer.layer.innerHTML"
  />
  <!--eslint-enable-->
  <portal to="clubhouse">
    <div
      v-if="show_modal"
      class="rect_overlay fixed top-0 left-0 w-screen h-screen"
      clickaway="true"
      @click="closeModal"
    >
      <AmenityCard
        :data="Store.amenityCardData[clubhouse_id]"
        :style="{ 'top': position.y + 'px', 'left': position.x + 'px' }"
      />
    </div>
    <div class="absolute bottom-4 right-3 flex gap-3">
      <button
        v-for="layer in layerData"
        :id="layer.layer_data.layer_id"
        :key="layer"
      />
    </div>
  </portal>
</template>
