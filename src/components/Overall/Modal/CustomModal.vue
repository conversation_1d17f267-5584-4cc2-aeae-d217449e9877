<script setup>
import { defineProps, defineEmits } from 'vue';
import TranslationComp from '../../ALEComponents/TranslationComp/TranslationComp.vue';
import { creationToolStore } from '../../../store';

const Store = creationToolStore();
const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    default: 'Modal',
  },
  showOverlay: {
    type: Boolean,
    default: true,
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['xs', 'sm', 'md', 'lg', 'xl', '2xl', '3xl'].includes(value),
  },
  modalPlacement: {
    type: String,
    default: 'center',
    validator: (value) => ['top-left', 'top-right', 'bottom-left', 'bottom-right', 'center'].includes(value),
  },
});

const emit = defineEmits(['update:isOpen']);

const sizeClasses = {
  'xs': 'max-w-xs w-full',
  'sm': 'max-w-sm w-full',
  'md': 'max-w-md w-full',
  'lg': 'max-w-lg w-full',
  'xl': 'max-w-xl w-full',
  '2xl': 'max-w-2xl w-full',
  '3xl': 'max-w-3xl w-full',
};

const placementClasses = {
  'top-left': 'flex items-start justify-start',
  'top-right': 'flex items-start justify-end',
  'bottom-right': 'flex items-end justify-end',
  'bottom-left': 'flex items-end justify-start',
  'center': 'flex items-center justify-center',
};

const closeModal = () => {
  emit('update:isOpen', false);
};
</script>
<template>
  <div
    v-if="isOpen"
    class="inset-0 z-10"
    :class="[placementClasses[props.modalPlacement] || placementClasses['center']]"
  >
    <div
      v-if="showOverlay"
      class="absolute inset-0 bg-secondary  bg-opacity-50"
      @click="closeModal"
    />
    <div
      :class="[
        Store.isMobile?'rounded-t-lg':'rounded-lg',
        'bg-secondary min-w-fit shadow-xl z-1',
        sizeClasses[size] || sizeClasses['md'],
        'max-h-[90vh]'
      ]"
    >
      <header class="flex justify-between items-center py-2 px-4 text-base font-semibold text-secondaryText">
        <TranslationComp
          class="flex justify-between items-center py-2 px-2"
          :text="title"
        />
        <button
          class="text-secondaryText hover:text-secondaryText"
          @click="closeModal"
        >
          <svg
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </header>
      <div>
        <slot name="content" />
      </div>
      <footer class="p-2">
        <slot name="footer" />
      </footer>
    </div>
  </div>
</template>

<style scoped>
/* Add any additional styling if necessary */
</style>
