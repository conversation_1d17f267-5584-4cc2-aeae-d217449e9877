<template>
  <div :class="[toastClasses, animationClass]">
    <div class="flex items-center justify-between">
      <div>
        <slot name="content" />
      </div>

      <button
        type="button"
        class="flex-shrink-0 inline-flex justify-center w-7 h-7 items-center text-secondaryText hover:bg-secondaryText hover:text-secondary rounded-lg text-sm p-1.5 dark:hover:bg-secondaryText dark:hover:text-secondaryText"
        @click="handleClose"
      >
        <svg
          class="w-3 h-3"
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 14 14"
        >
          <path
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
          />
        </svg>
        <span class="sr-only">Close banner</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  position: {
    type: String,
    default: 'bottom-center',
    validator: (value) => ['top-center', 'bottom-center'].includes(value),
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg', 'xl', '2xl', '3xl'].includes(value),
  },
  autoClose: {
    type: Boolean,
    default: false,
  },
  duration: {
    type: Number,
    default: 0,
  },
});

const timer = ref(-1);
const startedAt = ref(0);
const delay = ref(0);

const emit = defineEmits(['update:showToast']);

const handleClose = () => {
  emit('update:showToast', false);
};

const sizeClasses = {
  'sm': 'w-64',
  'md': 'w-96',
  'lg': 'w-full max-w-lg',
  'xl': 'w-full max-w-xl',
  '2xl': 'w-full max-w-2xl',
  '3xl': 'w-full max-w-3xl',
};

const toastClasses = computed(() => {
  return [
    'fixed',
    'h-14',
    sizeClasses[props.size],
    'bg-secondary',
    'text-secondaryText',
    'p-4',
    'rounded-lg',
    'shadow-lg',
    'z-50',
    'transition-transform',
    'duration-300',
    'transform',
    'left-1/2',
    '-translate-x-1/2',
    props.position === 'top-center' ? 'top-5' : 'bottom-5',
  ];
});

const animationClass = computed(() => {
  return props.position === 'top-center' ? 'popup-top' : 'popup-bottom';
});

onMounted(() => {
  if (props.autoClose) {
    startedAt.value = Date.now();
    delay.value = props.duration * 1000;
    timer.value = setTimeout(handleClose, delay.value);
  }
});
</script>

<style scoped>
.popup-top {
  animation: dropAnimation 0.5s ease forwards;
}

.popup-bottom {
  animation: popDownAnimation 0.5s ease forwards;
}

@keyframes dropAnimation {
  from {
    top: 0%;
  }

  to {
    top: 2%;
  }
}

@keyframes popDownAnimation {
  from {
    bottom: 0%;
  }

  to {
    bottom: 8.5%;
  }
}
</style>
