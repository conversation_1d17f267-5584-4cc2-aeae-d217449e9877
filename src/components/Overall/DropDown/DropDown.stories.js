import DropDown from './DropDown.vue';

export default {
  title: 'Design System/Overall/DropDown',
  component: DropDown,
  tags: ['autodocs'],

};

export const Primary = {
  args: {
    title: "das",
    defaultValue: "option1",
    options: [
      { label: 'All', value: 'all' },
      { label: 'Option 1', value: 'option1' },
      { label: 'Option 2', value: 'option2' },
      { label: 'Option 3', value: 'option3' },
    ],
  },
};
