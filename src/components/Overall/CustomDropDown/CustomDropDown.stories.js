import CustomDropDown from './CustomDropDown.vue';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
export default {
  title: 'Design System/Overall/CustomDropDown',
  component: CustomDropDown,
  tags: ['autodocs'],
  argTypes: {

  },
};

export const Primary = {
  args: {
    conTitle: "das",
    defaultValue: 'option1',
    options: [
      { label: 'All', value: 'all' },
      { label: 'Option 1', value: 'option1' },
      { label: 'Option 2', value: 'option2' },
      { label: 'Option 3', value: 'option3' },
    ],
  },
};

export const WithoutDefaultValue = {
  args: {
    conTitle: "das",
    options: [
      { label: 'All', value: 'all' },
      { label: 'Option 1', value: 'option1' },
      { label: 'Option 2', value: 'option2' },
      { label: 'Option 3', value: 'option3' },
    ],
  },
};
