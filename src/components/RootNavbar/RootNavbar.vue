
<script setup>
import { ref } from 'vue';
import { creationToolStore } from '../../store';
import { getCookie } from '../../helpers/helper';

const Store = creationToolStore();
const closeFullscreenModal = ref(false);
const checkAutoExit = ref(false);
function setFullScreenCookie () {
  if (!getCookie('fullscreen')) {
    const expiryTime = new Date(Date.now() + (30 * 60000));
    document.cookie = `fullscreen=true; path=/; expires=${expiryTime.toUTCString()};`;
    closeFullscreenModal.value = true;
  }
}
// ADDED
const onClickButton = () => {
  setFullScreenCookie();
  if (!document.fullscreenElement &&
      !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
    if (document.documentElement.requestFullscreen) {
      document.documentElement.requestFullscreen();
    } else if (document.documentElement.mozRequestFullScreen) {
      document.documentElement.mozRequestFullScreen();
    } else if (document.documentElement.webkitRequestFullscreen) {
      document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
    } else if (document.documentElement.msRequestFullscreen) {
      document.documentElement.msRequestFullscreen();
    }
    checkAutoExit.value = false;
    Store.isFullScreen = !Store.isFullScreen;
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
    checkAutoExit.value = true;
    Store.isFullScreen = !Store.isFullScreen;
  }
};

</script>

<template>
  <div
    v-if="Store.isMobile"
    class="navMobile"
  >
    <div class="h-14 p-3 bg-[#1f2a37]/80  rounded-md flex justify-between items-center ml-4 w-full">
      <!-- <img
        class="w-6 h-6"
        :src="Store.organization_thumbnail"
      > -->
      <div>
        <p class="text-white text-sm font-normal capitalize">
          {{ Store.organization_name }}
        </p>
      </div>

      <div class="cursor-pointer flex gap-6 relative left-[-10px]">
        <div>
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g id="heart">
              <path
                id="Vector"
                d="M2.78546 6.48159C2.97391 6.02661 3.25014 5.61321 3.59837 5.26499C3.94659 4.91677 4.35998 4.64054 4.81496 4.45208C5.26993 4.26362 5.75757 4.16663 6.25003 4.16663C6.74249 4.16663 7.23013 4.26362 7.68511 4.45208C8.14008 4.64054 8.55348 4.91677 8.9017 5.26499L10 6.36333L11.0984 5.26499C11.8016 4.56173 12.7555 4.16664 13.75 4.16664C14.7446 4.16664 15.6984 4.56173 16.4017 5.26499C17.105 5.96826 17.5001 6.92209 17.5001 7.91666C17.5001 8.91123 17.105 9.86506 16.4017 10.5683L10 16.97L3.59837 10.5683C3.25014 10.2201 2.97391 9.80671 2.78546 9.35173C2.597 8.89676 2.5 8.40912 2.5 7.91666C2.5 7.4242 2.597 6.93656 2.78546 6.48159Z"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </g>
          </svg>
        </div>
        <div>
          <svg
            width="20"
            height="21"
            viewBox="0 0 20 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g id="phone">
              <path
                id="Vector"
                d="M2.98816 3.48816C2.67559 3.80072 2.5 4.22464 2.5 4.66667V5.5C2.5 12.4033 8.09667 18 15 18H15.8333C16.2754 18 16.6993 17.8244 17.0118 17.5118C17.3244 17.1993 17.5 16.7754 17.5 16.3333V13.6008C17.5 13.4259 17.445 13.2553 17.3427 13.1134C17.2404 12.9714 17.096 12.8653 16.93 12.81L13.1858 11.5617C12.9956 11.4984 12.7888 11.5059 12.6036 11.5827C12.4184 11.6596 12.2671 11.8006 12.1775 11.98L11.2358 13.8608C9.19538 12.9389 7.5611 11.3046 6.63917 9.26417L8.52 8.3225C8.69938 8.23288 8.84042 8.08158 8.91726 7.89637C8.9941 7.71116 9.00158 7.50445 8.93833 7.31417L7.69 3.57C7.63475 3.40413 7.52874 3.25984 7.38696 3.15754C7.24519 3.05525 7.07483 3.00013 6.9 3H4.16667C3.72464 3 3.30072 3.17559 2.98816 3.48816Z"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </g>
          </svg>
        </div>
      </div>
    </div>
  </div>
  <div
    v-if="!Store.isMobile"
    class="nav bg-[#111928]"
  >
    <div class="h-full w-[97%] mx-[auto] flex items-center">
      <div class="flex w-full h-[58px] items-center gap-2">
        <div class="w-[62px] h-full p-[19px] bg-[#1f2a37] rounded-lg backdrop-blur-[20px] justify-center items-center inline-flex cursor-pointer">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M3.09787 11.4659C3.00053 11.7211 2.97508 12.002 3.02475 12.2729C3.07441 12.5438 3.19696 12.7927 3.37689 12.988L8.52007 18.5737C8.63868 18.7071 8.78056 18.8134 8.93743 18.8866C9.0943 18.9598 9.26303 18.9983 9.43375 18.9999C9.60448 19.0016 9.77379 18.9662 9.93181 18.896C10.0898 18.8258 10.2334 18.7221 10.3541 18.591C10.4748 18.4599 10.5703 18.304 10.635 18.1324C10.6996 17.9607 10.7322 17.7769 10.7307 17.5914C10.7292 17.406 10.6937 17.2228 10.6263 17.0524C10.5589 16.882 10.461 16.728 10.3382 16.5991L7.38857 13.3957H19.7142C20.0552 13.3957 20.3823 13.2486 20.6234 12.9867C20.8645 12.7248 21 12.3697 21 11.9993C21 11.6289 20.8645 11.2738 20.6234 11.0119C20.3823 10.75 20.0552 10.6029 19.7142 10.6029H7.38857L10.3369 7.40086C10.4597 7.27205 10.5577 7.11796 10.625 6.94759C10.6924 6.77722 10.7279 6.59398 10.7294 6.40856C10.7309 6.22314 10.6983 6.03927 10.6337 5.86765C10.569 5.69603 10.4736 5.54012 10.3528 5.409C10.2321 5.27789 10.0885 5.1742 9.93053 5.10399C9.77251 5.03377 9.6032 4.99844 9.43247 5.00005C9.26174 5.00166 9.09302 5.04019 8.93615 5.11337C8.77927 5.18656 8.63739 5.29294 8.51878 5.42631L3.3756 11.012C3.25648 11.142 3.1621 11.2962 3.09787 11.4659Z"
              fill="white"
            />
          </svg>
        </div>

        <div class="w-[130px] h-full p-3 bg-[#1f2a37] rounded-md flex justify-center items-center gap-2 flex-col">
          <div class="text-center">
            <span class="text-white text-sm font-normal capitalize">{{ Store.organization_name }}</span>
          </div>
          <!-- <img
        class="w-6 h-6"
        :src="Store.organization_thumbnail"
      > -->
        </div>
      </div>

      <div class="w-[435px] h-full justify-end items-center gap-3 inline-flex">
        <div
          class="p-3.5 bg-[#1f2a37] rounded-full backdrop-blur-[20px] justify-start items-start flex cursor-pointer"
          @click="onClickButton"
        >
          <svg
            width="16"
            height="17"
            viewBox="0 0 16 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g id="expand">
              <g id="Vector">
                <path
                  d="M13.3333 2.5H10.6667C10.298 2.5 10 2.79867 10 3.16667C10 3.53467 10.298 3.83333 10.6667 3.83333H11.8727L9.52867 6.19067C9.268 6.45133 9.268 6.87267 9.52867 7.13333C9.65867 7.26333 9.82933 7.32867 10 7.32867C10.1707 7.32867 10.3413 7.26333 10.4713 7.13333L12.6667 4.926V5.83333C12.6667 6.20133 12.9647 6.5 13.3333 6.5C13.702 6.5 14 6.20133 14 5.83333V3.16667C14 2.79867 13.702 2.5 13.3333 2.5Z"
                  fill="white"
                />
                <path
                  d="M13.3333 10.5C12.9647 10.5 12.6667 10.7987 12.6667 11.1667V12.2313L10.4713 10.0287C10.2107 9.768 9.78933 9.768 9.52867 10.0287C9.268 10.2893 9.268 10.7107 9.52867 10.9713L11.7167 13.1667H10.6667C10.298 13.1667 10 13.4653 10 13.8333C10 14.2013 10.298 14.5 10.6667 14.5H13.3333C13.702 14.5 14 14.2013 14 13.8333V11.1667C14 10.7987 13.702 10.5 13.3333 10.5Z"
                  fill="white"
                />
                <path
                  d="M4.276 3.83333H5.33333C5.702 3.83333 6 3.53467 6 3.16667C6 2.79867 5.702 2.5 5.33333 2.5H2.66667C2.58 2.5 2.49333 2.518 2.412 2.55133C2.24867 2.61867 2.11867 2.74867 2.05133 2.912C2.018 2.99333 2 3.08 2 3.16667V5.83333C2 6.20133 2.298 6.5 2.66667 6.5C3.03533 6.5 3.33333 6.20133 3.33333 5.83333V4.776L5.69 7.13333C5.82 7.26333 5.99067 7.32867 6.16133 7.32867C6.332 7.32867 6.50267 7.26333 6.63267 7.13333C6.89333 6.87267 6.89333 6.45133 6.63267 6.19067L4.276 3.83333Z"
                  fill="white"
                />
                <path
                  d="M5.69 10.0287L3.33333 12.3853V11.1667C3.33333 10.7987 3.03533 10.5 2.66667 10.5C2.298 10.5 2 10.7987 2 11.1667V13.8333C2 14.2013 2.298 14.5 2.66667 14.5H5.33333C5.702 14.5 6 14.2013 6 13.8333C6 13.4653 5.702 13.1667 5.33333 13.1667H4.43733L6.63267 10.9713C6.89333 10.7107 6.89333 10.2893 6.63267 10.0287C6.372 9.768 5.95067 9.768 5.69 10.0287Z"
                  fill="white"
                />
              </g>
            </g>
          </svg>
        </div>
        <div class="hidden px-5 py-2.5 bg-[#1c64f2] rounded-lg justify-center items-center gap-2 flex cursor-pointer">
          <div class="w-3.5 h-3.5 relative">
            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g id="mail">
                <path
                  id="Vector"
                  d="M1.40234 4.11879L7.00024 6.91739L12.5981 4.11879C12.5774 3.7621 12.4211 3.42683 12.1612 3.18165C11.9013 2.93646 11.5575 2.79992 11.2002 2.79999H2.80024C2.44295 2.79992 2.09916 2.93646 1.83927 3.18165C1.57938 3.42683 1.42307 3.7621 1.40234 4.11879Z"
                  fill="white"
                />
                <path
                  id="Vector_2"
                  d="M12.6004 5.68259L7.00039 8.48259L1.40039 5.68259V9.79999C1.40039 10.1713 1.54789 10.5274 1.81044 10.7899C2.07299 11.0525 2.42909 11.2 2.80039 11.2H11.2004C11.5717 11.2 11.9278 11.0525 12.1903 10.7899C12.4529 10.5274 12.6004 10.1713 12.6004 9.79999V5.68259Z"
                  fill="white"
                />
              </g>
            </svg>
          </div>

          <p class="text-white text-sm font-medium font-['Inter'] leading-[21px]">
            Make An Enquiry
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.nav{
    /* box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25); */
    backdrop-filter: blur(50px);
    flex-shrink: 0;
    height: 10%;
    display: flex;
    align-items: center;
}
.navMobile{
    /* box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25); */
    background: rgba(31, 42, 55, 0.80);
    backdrop-filter: blur(50px);
    flex-shrink: 0;
    height: 50px;
    display: flex;
    align-items: center;
}
</style>
