<script setup>
import {defineProps, ref, onMounted, defineEmits} from 'vue';
import router from '../../router';
import { useRoute } from 'vue-router';
import { cdn } from '../../helpers/helper';
const route = useRoute();
const props = defineProps({layer: {
  type: Object,
  default () {
    return {};
  },
}});
const emit = defineEmits(['appendObject']);
const clickedId=ref(false), pinLayer= ref(null);
function moveToNxtScene (id){
  clickedId.value=id;
  if (route.fullPath.includes('masterscene')) {
    router.push({ name: 'masterScene', params: {sceneId: id}});
  } else {
    router.push({ name: 'projectScene', params: {sceneId: id}});
  }
}
onMounted(() => {
  var requestOptions = {
    method: "GET",
    redirect: "follow",
  };
  const url =  cdn(props.layer.svg_url);
  fetch(url, requestOptions)
    .then((response) => response.text())
    .then((result) => {
      const svgString = result;
      var div = document.createElement('div');
      div.id=props.layer.layer_id;
      div.innerHTML=svgString;
      div.setAttribute('class', 'amenityIcons');
      pinLayer.value=div;
      document.body.appendChild(div);
      document.getElementById(props.layer.layer_id).addEventListener( 'click', () => moveToNxtScene(props.layer.scene_id) );
      emit('appendObject', pinLayer.value);
    });
});
</script>
