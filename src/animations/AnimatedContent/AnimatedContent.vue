<!--
	Installed from https://vue-bits.dev/ui/
-->

<script setup lang="ts">
import { onMounted, onUnmounted, watch, useTemplateRef } from "vue";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { creationToolStore } from "../../store";

gsap.registerPlugin(ScrollTrigger);

interface AnimatedContentProps {
  distance?: number;
  direction?: "vertical" | "horizontal";
  reverse?: boolean;
  duration?: number;
  ease?: string | ((progress: number) => number);
  initialOpacity?: number;
  animateOpacity?: boolean;
  scale?: number;
  threshold?: number;
  delay?: number;
  className?: string;
  show?: boolean;
}

const props = withDefaults(defineProps<AnimatedContentProps>(), {
  distance: 100,
  direction: "vertical",
  reverse: false,
  duration: 0.8,
  ease: "power3.out",
  initialOpacity: 0,
  animateOpacity: true,
  scale: 1,
  threshold: 0.1,
  delay: 0,
  className: "",
  show: true
});

const emit = defineEmits<{
  complete: [];
}>();

const containerRef = useTemplateRef<HTMLDivElement>("containerRef");
const Store = creationToolStore();
// Debounced resize handler for better performance
let resizeTimeout: ReturnType<typeof setTimeout>;
const handleResize = () => {
  clearTimeout(resizeTimeout);
  resizeTimeout = setTimeout(() => {
    ScrollTrigger.refresh();
    // Force re-evaluation of mobile breakpoint for existing ScrollTriggers
    const el = containerRef.value;
    if (el) {
      const existingTriggers = ScrollTrigger.getAll().filter(trigger => trigger.trigger === el);
      if (existingTriggers.length > 0) {
        existingTriggers.forEach(trigger => {
          const startPct = (1 - props.threshold) * 100;
          trigger.vars.start = `top ${window.innerWidth <= 640 ? '95%' : `${startPct}%`}`;
          trigger.refresh();
        });
      }
    }
  }, 100); // 100ms debounce
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
  const el = containerRef.value;
  if (!el) return;

  const axis = props.direction === "horizontal" ? "x" : "y";
  const offset = props.reverse ? -props.distance : props.distance;
  const startPct = (1 - props.threshold) * 100;

  gsap.set(el, {
    [axis]: offset,
    scale: props.scale,
    opacity: props.animateOpacity ? props.initialOpacity : 1,
  });

  // For mobile devices, use a more immediate trigger
  if (window.innerWidth <= 640) {
    gsap.to(el, {
      [axis]: 0,
      scale: 1,
      opacity: 1,
      duration: props.duration,
      ease: props.ease,
      delay: props.delay + 0.1, // Small delay to ensure element is in DOM
      onComplete: () => emit("complete"),
    });
  } else {
    gsap.to(el, {
      [axis]: 0,
      scale: 1,
      opacity: 1,
      duration: props.duration,
      ease: props.ease,
      delay: props.delay,
      onComplete: () => emit("complete"),
      scrollTrigger: {
        trigger: el,
        start: `top ${startPct}%`,
        toggleActions: "play none none none",
        once: true,
      },
    });
  }
});

watch(
  () => [
    props.distance,
    props.direction,
    props.reverse,
    props.duration,
    props.ease,
    props.initialOpacity,
    props.animateOpacity,
    props.scale,
    props.threshold,
    props.delay,
  ],
  () => {
    const el = containerRef.value;
    if (!el) return;

    ScrollTrigger.getAll().forEach((t) => t.kill());
    gsap.killTweensOf(el);

    const axis = props.direction === "horizontal" ? "x" : "y";
    const offset = props.reverse ? -props.distance : props.distance;
    const startPct = (1 - props.threshold) * 100;

    gsap.set(el, {
      [axis]: offset,
      scale: props.scale,
      opacity: props.animateOpacity ? props.initialOpacity : 1,
    });

    // For mobile devices, use a more immediate trigger
    if (window.innerWidth <= 640) {
      gsap.to(el, {
        [axis]: 0,
        scale: 1,
        opacity: 1,
        duration: props.duration,
        ease: props.ease,
        delay: props.delay + 0.1, // Small delay to ensure element is in DOM
        onComplete: () => emit("complete"),
      });
    } else {
      gsap.to(el, {
        [axis]: 0,
        scale: 1,
        opacity: 1,
        duration: props.duration,
        ease: props.ease,
        delay: props.delay,
        onComplete: () => emit("complete"),
        scrollTrigger: {
          trigger: el,
          start: `top ${startPct}%`,
          toggleActions: "play none none none",
          once: true,
        },
      });
    }
  },
  { deep: true },
);

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  clearTimeout(resizeTimeout);
  const el = containerRef.value;
  if (el) {
    ScrollTrigger.getAll().forEach((t) => t.kill());
    gsap.killTweensOf(el);
  }
});
</script>

<template>
  <div v-if="props.show"
    ref="containerRef" :class="`animated-content ${props.className}`">
    <slot />
  </div>
  <div v-else>
    <slot />
  </div>

</template>

<style scoped>
.animated-content {
  border-radius: inherit; /* inherit from prop class or set your own */
  overflow: hidden;
  height: auto;
}
/* GSAP will handle all transforms and opacity */
</style>
