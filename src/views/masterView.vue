<script setup>
import { ref } from 'vue';
import mainCompView from './mainCompView.vue';
import globeView from './globeView.vue';
import { creationToolStore } from '../store';
import { useRoute } from 'vue-router';
import NavBar from '../components/ALEComponents/NavBar/NavBar.vue';
import router from '../router';
import { Googleanalytics, removeCurrencyParam, setCookie } from '../helpers/helper';
import SeadragonOverlay from '../components/openseadragon/seadragonOverlay.vue';
import { watch } from 'vue';
const route = useRoute();
const Store = creationToolStore();
const scenes = ref(false);
const props = defineProps({'sceneId': {type: String, default: ""}});
const removeOverlay = ref(false);
const showPoster = ref(true);
Store.galleryList = false;
Store.amenityCardData = {};
Store.amenityOptions = {};
Store.galleryCategories = false;
function onVideoEnded (){
  Store.currentSceneVideo=false;
}
function routeScene (){
  window.asd();
}
if (Object.values(Store.SceneData).length===0){
  Store.getMasterScenes(route.params.organizationId).then((res) => {
    scenes.value=res;
  });
} else {
  scenes.value=Store.SceneData;
}

if (Object.keys(Store.organizationDetails).length <= 0) {
  Store.getOrganization(route.params.organizationId);
}

Store.updateSceneType(route.fullPath);
Store.getTranslation(route.params.organizationId);

if (route.query.currency) {
  setCookie('selectedCurrency', route.query.currency.toUpperCase(), 1);
  Store.currencyData.currency = route.query.currency.toUpperCase();
  const queryObject = removeCurrencyParam(route.query);
  router.replace({ name: route.name, query: queryObject });
}

function goToPreviousScene () {
  var parentId = Store.SceneData[props.sceneId].sceneData.parent;
  if (parentId) {
    console.log(Store.SceneData, parentId);

    if (Store.SceneData[parentId].sceneData.master_scene) {
      router.push({name: 'masterScene', params: {sceneId: Store.SceneData[props.sceneId].sceneData.parent}});
    } else {
      router.push({name: 'projectScene', params: {sceneId: Store.SceneData[props.sceneId].sceneData.parent}});
    }
  }
}

window.addEventListener('resize', Store.callbackFunctionMonitorChanges);
Store.callbackFunctionMonitorChanges();
Store.isNavigatedFromMasterScene = true;

// if (Object.keys(Store.projectCardData).length === 0){
//   console.log('projectCardData',Store.projectCardData);

//   Store.getListofProjects(route.params.organizationId).then(() => {
//     loadFont(Store.projectCardData[route.params.projectId]?.projectSettings?.theme?.font_url).then(() => {
//       Store.loadedFont = Store.projectCardData[route.params.projectId]?.projectSettings?.theme?.font_type;
//     });
//   });
// }

const callGoogleanalytics = () => {
  if (!Store.isExperienceStartEventSent) {
    Googleanalytics("experience_start", {
      organization_id: route.params.organizationId,
      organization_name: Store.organizationDetails?.name,
      project_id: route.params.projectId ? route.params.projectId : '',
      project_name: Store.projectCardData?.[route.params.projectId]?.name ? Store.projectCardData?.[route.params.projectId]?.name :'',
    });
    Store.isExperienceStartEventSent = true;
  }
};

watch(() => Store.organizationDetails,
  (newVal, oldVal) => {
    if (Object.keys(newVal).length !== 0 && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
      callGoogleanalytics();
    }
  },
  { immediate: false, deep: true },
);
</script>
<template>
  <div
    class="!h-full !w-full"
    :style="{ fontFamily: Store.loadedFont }"
  >
    <div
      v-if="Store.currentSceneVideo"
      class="absolute top-4 right-2 z-[9]"
    >
      <!-- Close Button -->
      <button
        class="p-2 bg-gray-200 rounded-full hover:bg-gray-300 focus:outline-none"
        @click="()=>{Store.currentSceneVideo=false}"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 text-gray-700"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 9.293l4.646-4.647a.5.5 0 01.708.708L10.707 10l4.647 4.646a.5.5 0 01-.708.708L10 10.707l-4.646 4.647a.5.5 0 01-.708-.708L9.293 10 4.646 5.354a.5.5 0 11.708-.708L10 9.293z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
    </div>
    <div
      v-if="Store.currentSceneVideo"
      class="h-full w-full fixed top-0 left-0 z-[8]"
    >
      <video
        :poster="Store.currentSceneVideoThumb"
        class="h-full w-full object-cover"
        muted
        preload="auto"
        playsinline
        :autoplay="true"
        @playing="()=>{
          showPoster=false;
          routeScene();
        }"
        @ended="onVideoEnded"
      >
        <source
          :src="Store.currentSceneVideo"
          type="video/mp4"
        >
      </video>
    <!-- <img v-show="showPoster" class="videoPoster" :src="Store.currentSceneVideoThumb"/> -->
    </div>
    <SeadragonOverlay
      v-if="scenes && scenes[route.params.sceneId]['sceneData'].type.toLowerCase()==='deep_zoom'"
      :scenes="scenes"
      :scene-id="props.sceneId"
      :project-id="props.projectId"
      :organizationId="route.params.organizationId"
      @remove-loader="removeLoader = true"
    />
    <div
      v-if="scenes && scenes[route.params.sceneId]['sceneData'].type.toLowerCase()==='image'"
      id="maincompview"
      class="h-full w-full"
    >
      <div
        v-if="!removeOverlay "
        class="absolute top-0 left-0 w-full h-full bg-black opacity-50 z-10"
      />
      <mainCompView
        :scenes="scenes"
        :scene-id="props.sceneId"
        :project-id="props.projectId"
        :organizationId="route.params.organizationId"
        @loadothers="loadothers"
        @remove-loader="removeLoader = true"
        @remove-overlay="(val)=>removeOverlay = val"
      />
    </div>
  </div>
  <globeView
    v-if="scenes && scenes[route.params.sceneId]['sceneData'].type.toLowerCase()==='earth'"
    :scenes="scenes"
  />
  <NavBar
    v-if="scenes && scenes[route.params.sceneId]"
    :is-root="scenes[route.params.sceneId].sceneData.root"
    :isOrganization="true"
    @handle-navigation="goToPreviousScene"
  />
</template>
<style scoped>

</style>
