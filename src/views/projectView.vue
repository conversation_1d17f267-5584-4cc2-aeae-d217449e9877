<script setup>
import mainCompView from './mainCompView.vue';
import { ref, defineProps, onUnmounted, onMounted, watch } from 'vue';
import router from '../router';
import { creationToolStore } from '../store';
import { useRoute } from 'vue-router';
import gsplatView from './gsplatView.vue';
import loaderView from './loaderView.vue';
import { cdn, getSplashCookie, Googleanalytics } from '../helpers/helper';
import RotatableView from './rotatableView.vue';
import { fetchImageURL } from '../helpers/API';
import seadragonOverlay from '../components/openseadragon/seadragonOverlay.vue';
// import PopUpMessageBox from '../components/ALEComponents/PopUpMessageBox/PopUpMessageBox.vue';
import Navbar from '../components/ALEComponents/NavBar/NavBar.vue';
import NewAleSideBar from '../components/ALEComponents/NewAleSideBar/NewAleSideBar.vue';
import CircularLoader from '../components/ALEComponents/CircularLoader/CircularLoader.vue';
import { isRealValue, removeQueryParams, setCookie, removeCurrencyParam } from '../helpers/helper';
const route = useRoute();
const Store = creationToolStore();
const props = defineProps({'sceneId': {type: String, default: ""}, 'projectId': {type: String, default: ""}});
const scenes=ref(false);
// var checkAutoExit = ref(false);
const removeLoader = ref(false);
const RenderLoader = ref(false);
// const closeFullscreenModal = ref(false);
const removeOverlay = ref(false);
const showPoster = ref(true), mountedLoader = ref(false);
const loaderStateDetermined = ref(false);
const shouldShowLoader = ref(false);
const isReplacingRoute = ref(false);
// Determine loader state when data is available
const determineLoaderState = () => {
  const projectData = Store.projectCardData[props.projectId]?.projectSettings?.ale;
  const splashCookie = getSplashCookie();
  if (scenes.value && Store.projectCardData[props.projectId]) {
    shouldShowLoader.value = (
      projectData?.welcome_video &&
      projectData?.welcome_thumbnail &&
      !splashCookie[props.projectId]
    );
    loaderStateDetermined.value = true;
  }
};

// Watch for changes that affect loader state
watch(
  () => [scenes.value, Store.projectCardData[props.projectId], route.fullPath],
  () => {
    determineLoaderState();
  },
  { immediate: true },
);

function getFilterAPI () {
  const frameQuery = new URLSearchParams(removeQueryParams(route.query)).toString();
  if (isRealValue(frameQuery)) {
    Store.getFilterDataPoints(route.params.organizationId, props.projectId, frameQuery);
  } else {
    Store.getFilterDataPoints(route.params.organizationId, props.projectId);
  }
}
function loadothers (){
  Object.values(Store.SceneData)?.map((item) => {
    if (item.sceneData.type!=="rotatable_image" && item.sceneData.type!=="gsplat" && item.sceneData.type !=="earth"){
      return fetchImageURL(cdn(item.sceneData.background.low_resolution));
    }
    return null;
  });
}

function getValidSceneFromOptions (scenes, options, sceneId) {
  // Find root scene
  let rootScene = Object.values(scenes).find((scene) => scene.sceneData.root === true);
  // If sceneId is provided and valid, use that
  if (sceneId && scenes && scenes[sceneId]) {
    rootScene = scenes[sceneId];
  }
  if (!rootScene) {
    return null;
  }

  const rootSceneId = rootScene.sceneData._id;

  // If no options, return root scene
  if (!options || Object.values(options).length === 0) {
    return { sceneId: rootSceneId, type: 'projectscene' };
  }

  // Check if root scene is hidden in options
  const rootOption = Object.values(options).find((option) => option.scene_id === rootSceneId);
  const isRootHidden = rootOption && rootOption.hide === true;

  if (!isRootHidden) {
    return { sceneId: rootSceneId, type: 'projectscene' };
  }

  // Root is hidden, find alternative
  // Priority 1: unitplan type
  let alternativeOption = Object.values(options).find((option) => option.type === 'unitplan');

  // Priority 2: any other scene that's not the root
  if (!alternativeOption) {
    alternativeOption = Object.values(options).find((option) => option.scene_id !== rootSceneId);
  }

  if (alternativeOption) {
    return { sceneId: alternativeOption.scene_id, type: alternativeOption.type, isRedirect: true};
  }

  return { sceneId: rootSceneId, type: 'projectscene' };
}
function moveToLocation (id, type, optionId, replace = false){
  if (replace) {
    isReplacingRoute.value = true;
  }
  // Check if there's a last visited route for this option
  if (optionId) {
    const lastVisitedRoute = Store.getLastVisitedRoute(optionId);
    if (lastVisitedRoute && lastVisitedRoute.name) {
      router.push(lastVisitedRoute);
      return;
    }
  }

  const routerMethod = replace ? router.replace : router.push;
  // Default navigation logic
  if (type==='masterscene'){
    routerMethod({name: 'masterScene', params: {sceneId: id}});
  } else if (type==='earth'){
    routerMethod({name: 'globeScene'});
  } else if (type==='projectscene') {
    routerMethod({name: 'projectScene', params: {sceneId: id}});
  } else if (type === 'unitplan'){
    routerMethod({name: 'unitplansviewer'});
  } else if (type === 'amenity'){
    routerMethod({name: 'amenity'});
  } else if (type==='map') {
    routerMethod({name: 'map'});
  } else {
    routerMethod({name: 'galleryview'});
  }
}

function replaceCurrentRoute (id, type) {
  moveToLocation(id, type, null, true);
}

Store.getOptions(route.params.organizationId, route.params.projectId);
Store.getProjectScenes(route.params.organizationId, props.projectId).then((res) => {
  Store.getMasterScenes(route.params.organizationId).then(() => {
    const scenesData = res;
    scenes.value = false;
    isReplacingRoute.value = false;
    const splashCookie = getSplashCookie();
    splashCookie[props.projectId] = true;
    const options = Store.sidebarOptions[props.projectId];
    const validScene = getValidSceneFromOptions(scenesData, options, props.sceneId);
    if (!props.sceneId) {
      if (validScene) {
        if (validScene.isRedirect) {
          replaceCurrentRoute(validScene.sceneId, validScene.type);
        } else {
          router.push({ name: "projectScene", params: { sceneId: validScene.sceneId } });
        }
      }
      // No scene ID in route - navigate to valid scene
      if (scenesData[props.sceneId] && !scenesData[props.sceneId].sceneData.root) {
        document.cookie = `splashLoader=${JSON.stringify(splashCookie)}; path=/;`;
      }
    } else {
      if (!scenesData[props.sceneId]) {
        if (validScene) {
          if (validScene.isRedirect) {
            replaceCurrentRoute(validScene.sceneId, validScene.type);
          } else {
            router.push({ name: "projectScene", params: { sceneId: validScene.sceneId}, query: {...route.query} });
          }
        }
        document.cookie = `splashLoader=${JSON.stringify(splashCookie)}; path=/;`;
      } else {
        if (validScene) {
          if (validScene.isRedirect) {
            replaceCurrentRoute(validScene.sceneId, validScene.type);
          }
        }
        if (scenesData[props.sceneId] && !scenesData[props.sceneId].sceneData.root) {
          document.cookie = `splashLoader=${JSON.stringify(splashCookie)}; path=/;`;
        }
      }
    }
    const isRedirectedToUnitPlan = validScene && validScene.isRedirect && validScene.type === 'unitplan';
    console.log('Debug - validScene:', validScene);
    console.log('Debug - isRedirectedToUnitPlan:', isRedirectedToUnitPlan);
    console.log('Debug - Will set scenes.value:', !isRedirectedToUnitPlan);

    if (!isRedirectedToUnitPlan) {
      scenes.value = scenesData;
      console.log('Debug - scenes.value set to:', scenesData);
    } else {
      console.log('Debug - scenes.value NOT set due to unitplan redirection');
    }
  });
});
Store.updateSceneType(route.fullPath);
// } else {
//   scenes.value=Store.SceneData;
//   if (!props.sceneId){
//     const id = Object.values(scenes.value).filter((scene) => {
//       return scene.sceneData.root===true;
//     })[0].sceneData._id;
//     router.push({name: "projectScene", params: {sceneId: id}});
//     if (scenes.value[props.sceneId] && !scenes.value[props.sceneId].sceneData.root) {
//       document.cookie = "splashLoader=true; path=/;";
//     }
//   } else {
//     if (scenes.value[props.sceneId] && !scenes.value[props.sceneId].sceneData.root) {
//       document.cookie = "splashLoader=true; path=/;";
//     }
//   }
// }

if (route.query.currency) {
  setCookie('selectedCurrency', route.query.currency.toUpperCase(), 1);
  Store.currencyData.currency = route.query.currency.toUpperCase();
  const queryObject = removeCurrencyParam(route.query);
  router.replace({ name: route.name, query: queryObject });
}

// if (Object.values(Store.buildingData).length===0){
Store.getListOfBuildings(props.projectId, route.params.organizationId).then((res) => {
  Object.keys(res).forEach((key) => {
    Store.getFloorplanDetails(key, route.params.projectId, route.params.organizationId);
  });
});
// } else {
//   Object.keys(Store.buildingData).forEach((key) => {
//     Store.getFloorplanDetails(key, route.params.projectId, route.params.organizationId);
//   });
// }
// if (Object.keys(Store.projectCardData).length === 0){
Store.getListofProjects(route.params.organizationId, route.params.projectId).then(() => {
});
// }
// if (!Store.unitData) {
Store.getListofUnits(props.projectId, route.params.organizationId);
// }

// if (Object.values(Store.allUnitCardData).length===0) {
Store.getCountForUnits(props.projectId, route.params.organizationId);
// }

// if (!Store.galleryCategories){
Store.getGalleryCategories(route.params.organizationId, route.params.projectId);
// }
// if (!Store.landmarkData) {
Store.getListofLandmark(route.params.projectId, route.params.organizationId);
// }

function goToPreviousScene () {
  var parentId = Store.SceneData[props.sceneId].sceneData.parent;
  if (isReplacingRoute.value) {
    isReplacingRoute.value = false;
    router.back();
    return;
  }
  if (parentId) {
    console.log(Store.SceneData, parentId);

    if (Store.SceneData[parentId].sceneData.master_scene) {
      router.push({name: 'masterScene', params: {sceneId: Store.SceneData[props.sceneId].sceneData.parent}});
    } else {
      router.push({name: 'projectScene', params: {sceneId: Store.SceneData[props.sceneId].sceneData.parent}});
    }
  }
}

// if (Object.values(Store.communityData).length === 0){
Store.getCommunities(route.params.projectId, route.params.organizationId);
// }
// if (!Store.listOfVrTourData) {
Store.getListOfVRId(route.params.organizationId, route.params.projectId);
// }
// if (!Store.organization_thumbnail) {
Store.getOrganization(route.params.organizationId);
// }
// if (!Store.sidebarOptions[route.params.projectId]) {
// Store.getOptions(route.params.organizationId, route.params.projectId);
// }
// if (!Store.amenityCategories) {
Store.getCategories(route.params.organizationId, props.projectId);
// }
// if (Object.keys(Store.amenityCardData).length===0){
Store.getListofAmenities(props.projectId, props.organizationId);
// }
// if (Object.values(Store.unitplanData).length===0) {
Store.getListOfUnitplan(props.projectId, route.params.organizationId);
// }
// if (Object.values(Store.filteredDataPoints).length === 0) {
getFilterAPI();
// }
watch(
  () => [route.query.community_id, route.query.building_id, route.query.floor_id],
  ([new_community_id, new_building_id, new_floor_id], [old_community_id, old_building_id, old_floor_id]) => {
    if (new_community_id !== old_community_id && new_community_id) {
      getFilterAPI();
    } else if ((new_building_id !== old_building_id && new_building_id) || (new_floor_id !== old_floor_id && new_floor_id)) {
      getFilterAPI();
    }
  },
);

const shouldRenderLoader =(() => {
  const urlParams = new URLSearchParams(window.location.search);
  RenderLoader.value = urlParams.has('salestoolGuest');
});
shouldRenderLoader();
Store.getTranslation(route.params.organizationId);
const noAddFavoritesToolTip = ref(false);

function favoriteViewTooltipClickHandler (event) {
  if (event.target.id !== 'goToFavotiteText' && event.target.id !== 'goToFavotiteSvg' && event.target.id !== 'goToFavorite'){
    noAddFavoritesToolTip.value = false;
  }
}
const fullScreenChangeHandler = () => {
  // SvgLoaded.value = false;
  setTimeout(() => {
    // SvgLoaded.value = true;
  }, 1000);
  if (!document.fullscreenElement && !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
    // Emit('onClick');
    Store.isFullScreen = false;
  }
};

// function setFullScreenCookie () {
//   if (!getCookie('fullscreen')) {
//     const expiryTime = new Date(Date.now() + (30 * 60000)); // 30 minutes in milliseconds
//     document.cookie = `fullscreen=true; path=/; expires=${expiryTime.toUTCString()};`;
//     closeFullscreenModal.value = true;
//   }
// }

// const onClickButton = (id) => {
//   if (id === 'fullscreen') {
//     setFullScreenCookie();
//     if (!document.fullscreenElement &&
//       !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
//       if (document.documentElement.requestFullscreen) {
//         document.documentElement.requestFullscreen();
//       } else if (document.documentElement.mozRequestFullScreen) {
//         document.documentElement.mozRequestFullScreen();
//       } else if (document.documentElement.webkitRequestFullscreen) {
//         document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
//       } else if (document.documentElement.msRequestFullscreen) {
//         document.documentElement.msRequestFullscreen();
//       }
//       checkAutoExit.value = false;
//       Store.isFullScreen = !Store.isFullScreen;
//     } else {
//       if (document.exitFullscreen) {
//         document.exitFullscreen();
//       } else if (document.mozCancelFullScreen) {
//         document.mozCancelFullScreen();
//       } else if (document.webkitExitFullscreen) {
//         document.webkitExitFullscreen();
//       } else if (document.msExitFullscreen) {
//         document.msExitFullscreen();
//       }
//       Store.isFullScreen = !Store.isFullScreen;
//       checkAutoExit.value = true;
//     }
//   }
// };

onMounted(() => {
  document.addEventListener('fullscreenchange', fullScreenChangeHandler);
  document.addEventListener('webkitfullscreenchange', fullScreenChangeHandler);
  document.addEventListener('mozfullscreenchange', fullScreenChangeHandler);
  document.addEventListener('MSFullscreenChange', fullScreenChangeHandler);
  document.addEventListener('click', favoriteViewTooltipClickHandler);
});
onUnmounted(() => {
  document.removeEventListener('fullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('webkitfullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('mozfullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('MSFullscreenChange', fullScreenChangeHandler);
});
window.addEventListener('resize', Store.callbackFunctionMonitorChanges);
Store.callbackFunctionMonitorChanges();
function onVideoEnded (){
  Store.currentSceneVideo=false;
}
function routeScene (){
  window.asd();
}
const onComponentLoaderViewMounted = () => {
  mountedLoader.value = true;
};

const callGoogleanalytics = () => {
  Googleanalytics("experience_start", {
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
  });
  Store.isExperienceStartEventSent = true;
};

watch(() => [Store.organizationDetails, Store.projectCardData],
  (newVal, oldVal) => {
    // Check if the array changed and both objects have content
    if (JSON.stringify(newVal) !== JSON.stringify(oldVal) &&
        Object.keys(newVal[0]).length !== 0 &&
        Object.keys(newVal[1]).length !== 0) {
      if (!Store.isNavigatedFromMasterScene && !Store.isExperienceStartEventSent){
        callGoogleanalytics();
      }
    }
  },
  { immediate: false, deep: true },
);
</script>
<template>
  <div
    :style="{ fontFamily: Store.loadedFont }"
    class="projectview"
  >
    <div
      v-if="Store.currentSceneVideo"
      class="absolute top-4 right-2 z-[99]"
    >
      <!-- Close Button -->
      <button
        class="p-2 bg-gray-200 rounded-full hover:bg-gray-300 focus:outline-none"
        @click="()=>{Store.currentSceneVideo=false}"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 text-gray-700"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 9.293l4.646-4.647a.5.5 0 01.708.708L10.707 10l4.647 4.646a.5.5 0 01-.708.708L10 10.707l-4.646 4.647a.5.5 0 01-.708-.708L9.293 10 4.646 5.354a.5.5 0 11.708-.708L10 9.293z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
    </div>
    <div
      v-if="Store.currentSceneVideo"
      class="h-full w-full fixed top-0 left-0 z-[9]"
    >
      <video
        :poster="Store.currentSceneVideoThumb"
        class="h-full w-full object-cover"
        muted
        preload="auto"
        playsinline
        :autoplay="true"
        @playing="()=>{
          showPoster=false;
          routeScene();
        }"
        @ended="onVideoEnded"
      >
        <source
          :src="Store.currentSceneVideo"
          type="video/mp4"
        >
      </video>
    <!-- <img v-show="showPoster" class="videoPoster" :src="Store.currentSceneVideoThumb"/> -->
    </div>

    <loaderView
      v-if="shouldShowLoader"
      :projectData="Store.projectCardData[props.projectId]"
      :removeLoader="removeLoader"
      @vue:mounted="onComponentLoaderViewMounted"
    />
    <Navbar
      v-if="scenes[route.params.sceneId]"
      :is-root="scenes[route.params.sceneId].sceneData.root && scenes[route.params.sceneId].sceneData.parent && Store.SceneData[scenes[route.params.sceneId].sceneData.parent].sceneData.master_scene?false:scenes[route.params.sceneId].sceneData.root"
      :thumbnail="Store.organization_thumbnail"
      @handle-navigation="goToPreviousScene"
    />
    <div
      v-if="loaderStateDetermined && (mountedLoader || !shouldShowLoader)"
      class="md:flex justify-center pointer-events-auto hidden"
    >
      <!-- <PopUpMessageBox
        v-if="!closeFullscreenModal"
        message="Enter to full screen mode for best experience"
        button="Enter"
        @close-x="setFullScreenCookie"
        @enter="onClickButton('fullscreen')"
      /> -->
    </div>
    <div
      v-if="loaderStateDetermined && (mountedLoader || !shouldShowLoader) && Store.svgVisibility.showSVG"
      class="absolute top-[50%] sm:z-[1]"
    >
      <NewAleSideBar
        :sidebarList="Store.sidebarOptions[route.params.projectId]"
        @select-option="moveToLocation"
      />
    </div>

    <gsplatView
      v-if="scenes[props.sceneId] && scenes[props.sceneId]['sceneData'].type.toLowerCase()==='gsplat'"
      :scenes="scenes"
      :scene-id="props.sceneId"
      :project-id="props.projectId"
      :organizationId="route.params.organizationId"
    />
    <RotatableView
      v-else-if="scenes[props.sceneId] && scenes[props.sceneId]['sceneData'].type.toLowerCase()==='rotatable_image'"
      :scenes="scenes"
      :scene-id="props.sceneId"
      :project-id="props.projectId"
      :organizationId="route.params.organizationId"
      @remove-loader="removeLoader = true"
    />
    <seadragonOverlay
      v-else-if="scenes[props.sceneId] && scenes[props.sceneId]['sceneData'].type.toLowerCase()==='deep_zoom'"
      :scenes="scenes"
      :scene-id="props.sceneId"
      :project-id="props.projectId"
      :organizationId="route.params.organizationId"
      @remove-loader="removeLoader = true"
    />
    <div
      v-else-if="props.sceneId && scenes && scenes[props.sceneId]"
      id="maincompview"
      class="h-full w-full"
    >
      <div
        v-if="!removeOverlay"
        class="absolute top-0 left-0 w-full h-full bg-black opacity-50 z-10"
      />
      <mainCompView
        :scenes="scenes"
        :scene-id="props.sceneId"
        :project-id="props.projectId"
        :organizationId="route.params.organizationId"
        @loadothers="loadothers"
        @remove-loader="removeLoader = true"
        @remove-overlay="(val)=>removeOverlay = val"
      />
    </div>
    <CircularLoader />
  </div>
</template>
<style scoped>
.videoPoster{
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.projectview{
  width:100%;
  height:100%
}

</style>
