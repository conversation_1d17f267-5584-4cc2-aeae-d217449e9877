<script setup>
import { creationToolStore } from '../store';
import { defineProps } from 'vue';
import { useRoute } from 'vue-router';
// Import Backbar from '../components/ALEComponents/BackBar/BackTopBar.vue';
// Import router from '../router';
// import MatterportView from '../components/ALEComponents/Matterport/MatterportView.vue';

const props = defineProps({'projectId': {type: String, default: ""}, 'organizationId': {type: String, default: ""}});
const Store = creationToolStore();
if (!Store.selectedTabOption) {
  Store.selectedTabOption = 'unitplan';
}
const route = useRoute();

if (Object.keys(Store.buildingData).length===0){
  Store.getListOfBuildings(props.projectId, props.organizationId).then((res) => {
    Object.keys(res).forEach((key) => {
      Store.getFloorplanDetails(key, route.params.projectId, route.params.organizationId);
    });
  });
  if (Object.values(Store.allUnitCardData).length===0) {
    Store.getCountForUnits(props.projectId, route.params.organizationId);
  }
  if (Object.values(Store.unitplanData).length===0) {
    Store.getListOfUnitplan(props.projectId, props.organizationId);
  }
  if (!Store.unitData) {
    Store.getListofUnits(props.projectId, props.organizationId);
  }
} else {
  Object.keys(Store.buildingData).forEach((key) => {
    Store.getFloorplanDetails(key, route.params.projectId, route.params.organizationId);
  });
}

window.addEventListener('resize', Store.callbackFunctionMonitorChanges);
Store.callbackFunctionMonitorChanges();

</script>
<template>
  <div
    :style="{ fontFamily: Store.loadedFont }"
    class="flex flex-col justify-start items-start w-full h-[inherit]"
  >
    <!-- <div class="sm:hidden w-full flex-auto  z-[4] ">
      <Backbar
        class="customStyle"
        :is-root="Store.SceneData[route.params.sceneId]?.sceneData.root"
        :scene-name="Store.allUnitCardData[route.query.unit_id]?.name"
        :thumbnail="Store.projectCardData?.[route.params.projectId]?.projectSettings?.general?.branding_logo"
        @go-back="goToPreviousScene"
      />
    </div> -->

    <!-- <div class="hidden">
      <MatterportView :space-id="null" />
    </div> -->
    <div class="position-relative w-full h-full flex-auto">
      <!-- <div class="bg-red-600 h-full w-full"></div> -->
      <router-view />
    </div>
  </div>
</template>
<style scoped>

.customStyle{
  position: relative;
}

</style>
