<script setup>
import {computed, onMounted, ref} from 'vue';
import Navbar from '../components/ALEComponents/NavBar/NavBar.vue';
import ButtonComponent from '../components/ALEComponents/ButtonComponent/ButtonComponent.vue';
import { creationToolStore } from '../store/index';
import { useRoute } from 'vue-router';
import { FwbImg, FwbP, FwbA } from 'flowbite-vue';
import TranslationComp from '../components/ALEComponents/TranslationComp/TranslationComp.vue';
import { cdn, getSplashCookie } from '../helpers/helper';
import SplitText from '../TextAnimations/SplitText/SplitText.vue';
const Store = creationToolStore();
const route = useRoute();
const emit = defineEmits(['exploreProject', 'vue:mounted']);
const props = defineProps({
  projectData: {type: Object,
    default () {
      return {};
    }},
  removeLoader: {type: Boolean,
    default () {
      return false;
    }},
});
const videoRef = ref(null);
const isSafari = ref(/^((?!chrome|android).)*safari/i.test(navigator.userAgent) ? true : false);
const currrentVideoPlayTime = ref(0);
Store.updateSceneType(route.fullPath);

const svg= `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path d="M15.913 8.45783C15.9995 8.23906 16.0222 7.99833 15.978 7.76609C15.9339 7.53386 15.8249 7.32055 15.665 7.15317L11.0933 2.36541C10.9878 2.25109 10.8617 2.15991 10.7223 2.09718C10.5828 2.03445 10.4329 2.00143 10.2811 2.00005C10.1294 1.99866 9.97885 2.02895 9.83839 2.08913C9.69793 2.14931 9.57032 2.23819 9.463 2.35057C9.35569 2.46296 9.27083 2.5966 9.21336 2.7437C9.15589 2.8908 9.12697 3.04841 9.12829 3.20734C9.12961 3.36627 9.16114 3.52333 9.22104 3.66936C9.28094 3.81539 9.36801 3.94747 9.47717 4.05788L12.0991 6.80366H1.14293C0.839806 6.80366 0.549097 6.92977 0.334756 7.15423C0.120416 7.3787 0 7.68315 0 8.0006C0 8.31805 0.120416 8.62249 0.334756 8.84696C0.549097 9.07143 0.839806 9.19754 1.14293 9.19754H12.0991L9.47831 11.9421C9.36915 12.0525 9.28208 12.1846 9.22218 12.3306C9.16228 12.4767 9.13075 12.6337 9.12943 12.7927C9.12811 12.9516 9.15703 13.1092 9.2145 13.2563C9.27197 13.4034 9.35683 13.537 9.46415 13.6494C9.57146 13.7618 9.69907 13.8507 9.83953 13.9109C9.97999 13.9711 10.1305 14.0013 10.2823 14C10.434 13.9986 10.584 13.9656 10.7234 13.9028C10.8629 13.8401 10.989 13.7489 11.0944 13.6346L15.6661 8.84683C15.772 8.7354 15.8559 8.60322 15.913 8.45783Z" fill="black"/>
                </svg>`;
const pullBanner = ref(true);
function exploreProject () {
  const splashCookie = getSplashCookie();
  splashCookie[route.params.projectId] = true;
  document.cookie = `splashLoader=${JSON.stringify(splashCookie)}; path=/;`;
  Store.splashLoader = true;
  pullBanner.value=false;
  setTimeout(() => {
    emit('exploreProject');
  }, 250);
}

function hideProgressBar (e) {
  e.target.style.display = "none";
}

const videoLoader = ref(false);
const handleVideoLoaded = () => {
  videoLoader.value=true;
};

const formatedDescription = computed(() => {
  const wordLimit = Store.isLandscape ? 10 : 15;
  return props.projectData.description.split(/\s+/).slice(0, wordLimit).join(' ');
});
const showFullDescription=ref(false);

const progressPercentage = ref(0);

const trackProgress = () => {
  // Const progressBar = document.querySelector('.progress-bar');
  const animationDuration = 5000; // Duration in milliseconds

  let startTime = null;
  const animate = (time) => {
    if (!startTime) {
      startTime = time;
    }
    const progress = time - startTime;
    const percentage = Math.min((progress / animationDuration) * 100, 100);

    progressPercentage.value = Math.round(percentage);

    if (progress < animationDuration) {
      requestAnimationFrame(animate);
    }
  };

  requestAnimationFrame(animate);
};
const updatedPercentage =computed(() => progressPercentage.value);

onMounted(() => {
  trackProgress();

  if (isSafari.value){
    setTimeout(() => {
      currrentVideoPlayTime.value = videoRef.value.currentTime;
    }, 4000);
  }
  emit('vue:mounted');
});

</script>
<template>
  <div
    v-if="projectData.projectSettings"
    class="banner-container h-full w-full fixed top-0 left-0 z-50"
    :class="{ 'pull-up': pullBanner }"
  >
    <div class="relative w-full h-full">
      <div class="linear-gradient w-full h-full absolute" />
      <Navbar
        class="z-[9]"
        :is-root=" Store.SceneData[route.params.sceneId] && Store.SceneData[route.params.sceneId].sceneData.root"
        :thumbnail="projectData.projectSettings.general.branding_logo"
        :showLogo="projectData.projectSettings.ale.welcome_video?false:true"
      />
      <FwbImg
        v-if="!videoLoader"
        class="object-cover h-full w-full"
        :src="cdn(projectData.projectSettings.ale.welcome_thumbnail)"
        alt=""
      />

      <video
        v-if="!isSafari"
        class="h-full w-full object-cover"
        muted
        loop
        :autoplay="true"
        @loadeddata="handleVideoLoaded"
      >

        <source
          :src="cdn(projectData.projectSettings.ale.welcome_video)"
          type="video/mp4"
        >

      </video>

      <div
        v-else
        class="relative object-cover h-full w-full"
      >
        <div
          v-if="currrentVideoPlayTime === 0"
          class="absolute top-0 left-0 object-cover h-full w-full z-10 "
        >
          <FwbImg
            class="object-cover h-full w-full"
            :src="projectData.projectSettings.ale.welcome_thumbnail"
            alt=""
          />
          <span class="overlayBlackGradient h-full w-full z-10 absolute top-0 left-0" />
        </div>

        <video
          ref="videoRef"
          class="w-full h-auto max-w-ful object-cover"
          autoplay
          muted
          controls
        >
          <source
            :src="projectData.projectSettings.ale.welcome_video"
            type="video/mp4"
          >
        </video>
      </div>
      <div>
        <div
          class="absolute bottom-2  z-10 flex flex-col justify-center items-start"
          :class="Store.isMobile || Store.isLandscape?'w-full':'w-10/12'"
        >
          <div
            class=" px-4 pb-2 z-[2] w-full flex flex-col  justify-start items-start"
            :class="Store.isLandscape?' gap-2':'md:pt-28 md:px-28 md:pb-20 pt-10 gap-[2rem]'"
          >
            <img
              class="w-[14rem] h-auto"
              :class="Store.isLandscape ? 'w-[9rem]':''"
              :src="Store.projectCardData?.[route.params.projectId]?.projectSettings?.general?.branding_logo"
            >
            <div
              v-if="!Store.isMobile && !Store.isLandscape"
              class="z-[9]  sm:block project_description max-h-48 overflow-y-auto md:align-justify align-left brightness-[.8]"
              :class="Store.isMobile?'min-h-4 max-h-20':''"
            >
              <FwbP class="md:w-full text-white md:text-md text-base font-light  ">
                <SplitText
                  :text="props.projectData.description"
                  :delay="20"
                  :duration="0"
                  ease="power3.out"
                  split-type="chars"
                  :from="{ opacity: 0, y: 40 }"
                  :to="{ opacity: 1, y: 0 }"
                  :threshold="0.1"
                  root-margin="-100px"
                  text-align="center"
                  @animation-complete="handleAnimationComplete"
                />
              </FwbP>
            </div>

            <div :class="Store.isLandscape ? 'flex w-full !justify-between' : 'flex flex-col gap-[2rem]'">
              <div
                v-if="Store.isMobile || Store.isLandscape"
                class="z-[9]  sm:block project_description  overflow-y-auto md:align-justify align-left brightness-[.8]"
                :class="Store.isMobile && !Store.isLandscape ?'min-h-4 max-h-20': Store.isLandscape? 'min-h-2 max-h-16 w-1/2' :'max-h-48'"
              >
                <FwbP
                  v-if="!showFullDescription"
                  class="md:w-full text-white md:text-md text-base font-light  "
                >
                  <TranslationComp
                    :key="Store.isLandscape"
                    :text="formatedDescription"
                  />. <FwbA
                    href="#"
                    class="text-blue-700 underline"
                    @click="()=>{ showFullDescription=true; }"
                  >
                    Read more
                  </FwbA>
                </FwbP>
                <FwbP
                  v-else
                  class="text-white"
                >
                  <TranslationComp
                    :text=" props.projectData.description"
                  />. <FwbA
                    class="text-blue-700 underline"
                    href="#"
                    @click="()=>{ showFullDescription=false; }"
                  >
                    Read less
                  </FwbA>
                </FwbP>
              </div>
              <ButtonComponent
                v-if="props.projectData && removeLoader && progressPercentage === 100"
                message="Explore"
                :svg="svg"
                @click="exploreProject()"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="radial-gradient-overlay" />

      <div
        v-if="updatedPercentage !== 100"
        class="loading-bar absolute bottom-0 w-full z-10"
        @animationend="hideProgressBar"
      >
        <div class="flex justify-end mb-1">
          <span class="text-xs font-medium text-gray-400 pr-1">{{ updatedPercentage }}%</span>
        </div>
        <div class="w-full bg-gray-700 h-1.5 rounded-r-full">
          <div
            class="bg-white h-1.5 transition-all duration-100 ease-out rounded-r-full"
            :style="`width: ${updatedPercentage}%`"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
.bgcolor{
  color: '#28334A'
}
.radial-gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(79.81% 79.81% at 57.53% 53.8%, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.51) 100%);
}

.overlayBlackGradient {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0.1%, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.00));
}

.linear-gradient {
    background: linear-gradient(182.23deg, rgba(116, 120, 127, 0) 4.06%, #171C23 98.17%);
    backdrop-filter: blur(0.7px);
}
.project_description {
    padding-right: 20px;
    color: #fff;
}
.project_description::-webkit-scrollbar-track {
    border-radius: 4px;
    background-color: transparent;
}

.project_description::-webkit-scrollbar {
    width: 4px;
    background-color: transparent;
    border-radius: 8px;
}

.project_description::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
    background-color: #fff;
}
.loading-bar {
  display: block;
  width: 100%;
}
.banner-container {
  background-color: black;
  transition: transform 0.2s ease-out;
  transform: translateY(-100%);
}

.banner-container.pull-up {
  transform: translateY(0);
}

</style>

<style lang="scss">

</style>
