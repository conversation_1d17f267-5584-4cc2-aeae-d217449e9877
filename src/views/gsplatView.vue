<script setup>
import gsplatComp from '../components/gsplatComp.vue';
import { ref, defineProps, watch} from 'vue';
import router from '../router';
import { creationToolStore } from '../store/index';
import { useRoute } from 'vue-router';
// Import breadCrumb from '../components/ALEComponents/BreadCrumb/BreadCrumb.vue';
import NewAleSideBar from '../components/ALEComponents/NewAleSideBar/NewAleSideBar.vue';
import { cdn, getSplashCookie } from '../helpers/helper';
// Import Backbar from '../components/ALEComponents/BackBar/BackTopBar.vue';

const props = defineProps({'sceneId': {type: String, default: ""}, 'scenes': {
  type: Object,
  default () {
    return {};
  },
}, 'organizationId': {type: String, default: ""}, projectId: {type: String, default: undefined}});
var response = {
  type: "gsplat",
  source: cdn(props.scenes[props.sceneId].sceneData.gsplat_link),
  cameraPosition: props.scenes[props.sceneId].sceneData.position,
  autoRotate: props.scenes[props.sceneId].sceneData.auto_rotate,
  polarAngle: props.scenes[props.sceneId].sceneData.polar_angle,
  distance: props.scenes[props.sceneId].sceneData.distance,
  fov: !window.innerWidth <= 640?45:60,
};
const route = useRoute();
const Store = creationToolStore();
const scene_data = ref(props.scenes[props.sceneId]);
const showSlider = ref(false);
if (!Store.organization_thumbnail) {
  Store.getOrganization(props.organizationId);
}
if (!Store.sidebarOptions[route.params.projectId]) {
  Store.getOptions(props.organizationId, route.params.projectId);
}
Store.activeOptionId = Store.SceneData[route.params.sceneId].sceneData.category;
watch(props, (newprops) => {
  scene_data.value = props.scenes[newprops.sceneId];
  Store.activeOptionId = Store.SceneData[newprops.sceneId].sceneData.category;
});
function moveToLocation (id, type, optionId){
  // Check if there's a last visited route for this option
  const lastVisitedRoute = Store.getLastVisitedRoute(optionId);

  if (lastVisitedRoute && lastVisitedRoute.name) {
    router.push(lastVisitedRoute);
    return;
  }

  // Default navigation logic
  if (type==='masterscene'){
    router.push({name: 'masterScene', params: {sceneId: id} });
  } else if (type==='earth'){
    router.push({name: 'globeScene'});
  } else if (type==='projectscene') {
    router.push({name: 'projectScene', params: {sceneId: id}});
  } else if (type === 'unitplan'){
    router.push({ name: 'unitplansviewer'});
  } else if (type === 'amenity'){
    router.push({ name: 'amenity'});
  } else if (type==='map') {
    router.push({name: 'map'});
  } else {
    router.push( { name: 'galleryview'} );
  }
}
// Function goToPreviousScene () {
//   If (Store.SceneData[route.params.sceneId].sceneData.parent) {
//     Router.push({name: 'projectScene', params: {sceneId: Store.SceneData[route.params.sceneId].sceneData.parent}});
//   }
// }

setTimeout(() => {
  showSlider.value=true;
}, 1000);
if (!Store.amenityCategories) {
  Store.getCategories(props.organizationId, props.projectId);
}
window.addEventListener('resize', Store.callbackFunctionMonitorChanges);
Store.callbackFunctionMonitorChanges();

if (props.scenes && !props.scenes[props.sceneId].sceneData.root) {
  const splashCookie = getSplashCookie();
  splashCookie[props.projectId] = true;
  document.cookie = `splashLoader=${JSON.stringify(splashCookie)}; path=/;`;
}

// Function gotoUnit (unit_id){
//   Router.push({name: "unit.Child", params: {unitplanId: Store.allUnitCardData[unit_id].unitplan_id}, query: {...route.query, unit_id: unit_id}});
// }
</script>
<template>
  <div class="absolute top-[50%] z-[4]">
    <NewAleSideBar
      :sidebarList="Store.sidebarOptions[route.params.projectId]"
      @select-option="moveToLocation"
    />
  </div>
  <!-- <div
    class="absolute top-0 z-[4] sm:hidden"
  >
    <Backbar
      :is-root="Store.SceneData[route.params.sceneId].sceneData.root"
      :scene-name="Store.SceneData[route.params.sceneId].sceneData.name"
      :thumbnail="Store.projectCardData?.[route.params.projectId]?.projectSettings?.general?.branding_logo"
      @go-back="goToPreviousScene"
    />
  </div> -->
  <!-- <div
    v-if="Store.breadCrumb.length>0"
    class="absolute w-full  top-0 z-[4]"
  >
    <DamacHeader
      :property-data="Store.unitData"
      @gotounit="GotounitCard"
      @open-favourites="openFavourites"
    />

    <div class="left-0 top-[0.45rem] z-[2]  w-full ">
      <breadCrumb
        :logo="Store.organization_thumbnail"
        :bread-crumb-data="Store.breadCrumb"
        @move-to-scene="moveToLocation"
        @to-full-screen="onClickButton"
      />
    </div>
  </div> -->
  <div id="maincompview">
    <gsplatComp
      ref="gsplatCompRef"
      :data="response"
      :layers="props.scenes[props.sceneId].svgData"
    />
  </div>
</template>
<style scoped>
</style>
