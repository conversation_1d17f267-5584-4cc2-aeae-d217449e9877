<script setup>
import M<PERSON>Viewer from './MLEViewer.vue';
import { creationToolStore } from '../store';
import { ref } from 'vue';
import { cdn } from '../helpers/helper';

const props = defineProps({
  projectId: {
    type: String,
    default: "",
  },
  organizationId: {
    type: String,
    default: "",
  },
  tourId: {
    type: String,
    default: "",
  },
});
const Store = creationToolStore();
const storagePath = `https://${import.meta.env.VITE_APP_BUCKET_CDN}/CreationtoolAssets/${props.organizationId}/projects/${props.projectId}/tours/${props.tourId}`;
const vrData = ref({});
Store.getListOfVRId(props.organizationId, props.projectId).then(() => {
  vrData.value = Store.listOfVrTourData[props.tourId];
});
Store.getTranslation(props.organizationId);
Store.getOrganization(props.organizationId);
Store.getListofProjects(props.organizationId, props.projectId);
window.addEventListener('resize', Store.callbackFunctionMonitorChanges);
Store.callbackFunctionMonitorChanges();
</script>
<template>
  <div class="relative h-full w-full">
    <MLEViewer
      v-if="Object.keys(vrData).length"
      :modelURL="cdn(vrData.model)"
      :cameraURL="cdn(vrData.camera)"
      :storagePath="storagePath"
      :initialRotation="vrData.initial_rotation"
      :tourLabels="vrData.labels"
      :isMobile="Store.isMobile"
    />
  </div>
</template>
<style scoped>
</style>
