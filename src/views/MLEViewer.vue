<script setup>
import { onMounted, onBeforeUnmount, ref, watch } from 'vue';
import * as THREE from 'three';
import { Cache } from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js';
// import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
import * as TWEEN from 'three/examples/jsm/libs/tween.module.js';
import CircularLoader from '../components/ALEComponents/CircularLoader/CircularLoader.vue';
import NearByFloatingButton from '../components/ALEComponents/NearByFloatingButton/NearByFloatingButton.vue';

const hotspotsVal = ref([]);
var hotspots = hotspotsVal.value;
const hotspotsMeshVal = ref([]);
var hotspotsMeshes = hotspotsMeshVal.value;
const containerVal = ref(null);
const modelRef = ref(null);
var model = modelRef.value;
const materialRef = ref(null);
var material = materialRef.value;
const imagenameRef = ref("");
var imagename = imagenameRef.value;
const imagePositionRef = ref(null);
var imagePosition = imagePositionRef.value;
const updateTextureRef = ref(false);
var updateTexture = updateTextureRef.value;
const startTransitionRef = ref(false);
const camera_position = ref(null), controls_target = ref(null), unitplan_redirected= ref(false);
const isMobileRef= ref(false);
const list = ref([]);
// Define LOD configurations
const lods = [
  { level: 2, rows: 2, columns: 2 },
  { level: 1, rows: 4, columns: 4 },
  { level: 0, rows: 8, columns: 8 },
];
var loadedTiles = lods.map(() => []); // Array for each LOD
const scene = new THREE.Scene();
const renderer = new THREE.WebGLRenderer({ antialias: true, powerPreference: "high-performance" });

// Optional: Provide a DRACOLoader instance to decode compressed mesh data
const dracoLoader = new DRACOLoader();
const camera = new THREE.PerspectiveCamera(90, window.innerWidth / window.innerHeight, 0.1, 1000);
const controls = new OrbitControls(camera, renderer.domElement);
const gltfloader = new GLTFLoader();

// var startTransition = startTransitionRef.value;
var showLoader = ref(true);
const bucketName = import.meta.env.VITE_APP_BUCKET_CDN;
const leftButton = `<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.375 10.5L2.625 10.5" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7 14.875L2.625 10.5L7 6.125" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`;
const rightButton = `<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.625 10.5L18.375 10.5" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14 14.875L18.375 10.5L14 6.125" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`;
const updateMaterial = ref(() => {});
const animate = ref(() => {});
const props = defineProps({
  modelURL: {
    type: String,
    default: "",
  },
  cameraURL: {
    type: String,
    default: "",
  },
  storagePath: {
    type: String,
    default: "",
  },
  initialRotation: {
    type: Object,
    default: () => ( {
      x: 0,
      p: 0,
      y: 0,
    }),
  },
  tourLabels: {
    type: Object,
    default: () => ({}),
  },
  isMobile: {
    type: Boolean,
    default: false,
  },
  activeLabel: {
    type: String,
    default: "",
  },
});
const selectionId = ref(0);
const emit = defineEmits(['currentImageIdEmit']);
function vertexShader () {
  return `
 precision mediump float;

varying vec3 vPosition; // Pass the transformed position to the fragment shader
void main() {
  // Calculate the world position of the vertex
  vec4 worldPosition = modelMatrix * vec4(position, 1.0);
  
  // Pass the world position to the fragment shader
  vPosition = worldPosition.xyz;
  
  // Project the position onto the screen
  gl_Position = projectionMatrix * viewMatrix * worldPosition;
}
  `;
}

function fragmentShader () {
  return `
precision highp float;

uniform sampler2D uTexture1;    // First equirectangular texture
uniform sampler2D uTexture2;    // Second equirectangular texture
uniform vec3 uRotation;         // Rotation angles (X, Y, Z)
uniform vec2 uOffset;           // Offset for U and V in texture space
uniform vec3 uWorldOffset1;     // First world offset for texture 1 in world space
uniform vec3 uWorldOffset2;     // Second world offset for texture 2 in world space
uniform float uBlendFactor;     // Blend factor for mixing textures
uniform float uWorldOffsetBlendFactor;  // Separate blend factor for world offsets
varying vec3 vPosition;         // World position of the vertex
uniform sampler2D uTexture2update;
uniform float updateBlendFactor;
varying vec2 vUv;

const float PI = 3.14159265359;

// Rotation matrices for X, Y, Z axes
mat3 rotationX(float angle) {
  float s = sin(angle);
  float c = cos(angle);
  return mat3(1.0, 0.0, 0.0, 0.0, c, -s, 0.0, s, c);
}

mat3 rotationY(float angle) {
  float s = sin(angle);
  float c = cos(angle);
  return mat3(c, 0.0, s, 0.0, 1.0, 0.0, -s, 0.0, c);
}

mat3 rotationZ(float angle) {
  float s = sin(angle);
  float c = cos(angle);
  return mat3(c, -s, 0.0, s, c, 0.0, 0.0, 0.0, 1.0);
}

// Function to calculate spherical UV coordinates
vec2 sphericalUV(vec3 v, vec3 worldOffset) {
  vec3 worldPosition = v + worldOffset;

  // Apply rotation to the world position
  worldPosition = rotationZ(uRotation.z) * rotationY(uRotation.y) * rotationX(uRotation.x) * worldPosition;

  // Calculate longitude and latitude for spherical mapping
  float longitude = atan(worldPosition.z, worldPosition.x);
  float latitude = asin(worldPosition.y / length(worldPosition));

  // Convert to equirectangular UV coordinates
  float u = (longitude / PI + 1.0) * 0.5;
  float vCoord = (latitude / (0.5 * PI) + 1.0) * 0.5;

  // Add texture offset and mirror if necessary
  vec2 uv = vec2(u, vCoord) + uOffset;

  return fract(uv);
}


void main() {
  // Blend world offsets using the separate world offset blend factor
  vec3 blendedWorldOffset = mix(uWorldOffset1, uWorldOffset2, uWorldOffsetBlendFactor);

  // Calculate UV coordinates based on the blended world offset
  vec2 uv1 = sphericalUV(vPosition, uWorldOffset1); // UV for texture 1
  vec2 uv2 = sphericalUV(vPosition, uWorldOffset2); // UV for texture 2

  // Sample both textures using their respective UV coordinates
  vec4 color1 = texture2D(uTexture1, uv1);
  vec4 color2 = texture2D(uTexture2, uv2);
  vec4 color2Update = texture2D(uTexture2update, uv2);

  vec4 updateColor = mix(color2, color2Update, updateBlendFactor);

  // Blend the two colors based on the texture blend factor
  vec4 blendedColor = mix(color1, updateColor, uBlendFactor);

  // Output the final color
  gl_FragColor = blendedColor;
}

  `;
}

if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
  isMobileRef.value=true;
} else {
  isMobileRef.value=false;
}
function moveTo (data){
  selectionId.value = list.value.findIndex((item) => item.id === data.id);
  emit("currentImageIdEmit", data.id);
  const raypoint = new THREE.Vector3(data.camera_position.x, data.camera_position.y, data.camera_position.z);
  camera_position.value = null;
  controls_target.value = null;
  if (data.camera_position && data.controls_target){
    camera_position.value = data.camera_position;
    if (data.controls_target.x !== 0 && data.controls_target.y !== 0 && data.controls_target.z !== 0) {
      controls_target.value = data.controls_target;
    }
  }
  updateMaterial.value(data.camera_name, raypoint);
}
function setupUnitplanRedirect (id){
  const labelObj = {
    "value": props.tourLabels[id].name,
    "id": id,
    "camera_name": props.tourLabels[id].camera_name,
    "camera_position": props.tourLabels[id].camera_position,
    "controls_target": props.tourLabels[id].controls_target,
  };
  unitplan_redirected.value =true;
  moveTo(labelObj);
}
onMounted(() => {

  var raycaster;

  var mouse;
  var needsUpdate = false;
  var animating = false;
  let mousedown = 0;
  let timerForRaycast;
  var updateMaterialtimer;
  let lastUpdateTime = 0;
  const updateInterval = 50; // Update every 250ms
  const batchSize = 8;
  const lodLoadDelay = 1000; // Delay between LOD loads in ms
  let currentLODIndex = 0;
  let currentTexture = null;
  let nextTexture = null;
  // let blendFactor = 0;
  // let isBlending = false;
  const container = containerVal.value;
  // document.body.appendChild(container);
  // const clock =  new THREE.Clock();
  // camera.position.set(0,0,1)
  renderer.setSize(window.innerWidth, window.innerHeight);
  renderer.setPixelRatio(window.devicePixelRatio);
  container.appendChild(renderer.domElement);

  const maxAnisotropy = renderer.capabilities.getMaxAnisotropy();

  // controls.addEventListener('change', render); // use if there is no animation loop
  // controls.update();
  controls.enableDamping = true;
  controls.dampingFactor = 0.075;

  //   controls.addEventListener('end', () => {
  //     updateCameraOrbit();
  //   });

  const light = new THREE.AmbientLight('white', 2); // soft white light
  scene.add(light);

  // Load cursor Texture
  const textureLoader = new THREE.TextureLoader();
  const hotspotTexture = textureLoader.load('/assets/MLE/FloorHotspot_white.png');
  const cursorTexture = textureLoader.load('/assets/MLE/FloorHotspot_blue.png');
  const hotspotSelectedTexture = textureLoader.load('/assets/MLE/FloorHotspot_blueGlow.png');

  // Plane Geometry with Hotspot Texture
  const hotspotGeometry = new THREE.PlaneGeometry(0.4, 0.4);
  const cursorMaterial = new THREE.MeshBasicMaterial({ map: cursorTexture, depthTest: false, depthWrite: false, transparent: true, opacity: 0.75 });
  // const hotspotMaterial = new THREE.MeshBasicMaterial({ map: hotspotTexture, transparent: true, opacity: 0.75 })
  // Create a cursor object (optional: use a sprite for better visuals)
  // const cursorGeometry = new THREE.CircleGeometry(0.1, 32);
  // const cursorMaterial = new THREE.MeshBasicMaterial({ color: "white", side: THREE.DoubleSide, depthTest: false, depthWrite: false, transparent: true, opacity: 0.8 });
  const cursor = new THREE.Mesh(hotspotGeometry, cursorMaterial);
  cursor.rotation.x = -Math.PI / 2; // Align with the model's surface
  cursor.renderOrder = 999;
  scene.add(cursor);
  // console.log(cursor)
  dracoLoader.setDecoderPath(`https://${bucketName}/0AR/draco/`);
  gltfloader.setDRACOLoader(dracoLoader);
  function updateCameraOrbit () {
    // Update OrbitControls target to a point just in front of the camera

    var forward = new THREE.Vector3();
    camera.getWorldDirection(forward);
    const forwardwithOffset = forward.clone().multiplyScalar(0.0001);
    // forward.z=forward.z*0.000001
    controls.target.copy(camera.position).add(forwardwithOffset);
  }

  // update texture from one image to another
  updateMaterial.value = (name, position) => {
    if (!animating) {
      clearTimeout(updateMaterialtimer);
      material.uniforms.uTexture1.value = material.uniforms.uTexture2.value;
      updateTexture = false;
      currentLODIndex = 0;
      lastUpdateTime = 0;
      loadedTiles = lods.map(() => []);

      // name = name.replace('(', '');
      // name = name.replace(')', '');
      // name = name.replace('_', '');
      // console.log(name, position)
      imagename = name;
      imagePosition = position;

      material.uniforms.uBlendFactor.value = 0;

      updateMaterialtimer = setTimeout(() => {

        updateTexture = true;
        material.uniforms.uWorldOffset2.value.set(imagePosition.x * -1, imagePosition.y * -1, imagePosition.z * -1);
      }, 200);

      // new THREE.TextureLoader().load('renders/' + name + '.jpg', (panoramicTexture) => {
      //     panoramicTexture.anisotropy = maxAnisotropy
      //     panoramicTexture.minFilter = THREE.LinearMipmapLinearFilter;
      //     panoramicTexture.magFilter = THREE.LinearFilter;
      //     material.uniforms.uTexture2.value = panoramicTexture;
      //     material.uniforms.uWorldOffset2.value.set(position.x * -1, position.y, position.z * -1);
      //     material.needsUpdate = true;
      //     callback(true)

      // })
    }
  };

  // Function to update the world offset blend
  function updateWorldOffset ({ worldOffsetBlendFactor }) {
    material.uniforms.uWorldOffsetBlendFactor.value = worldOffsetBlendFactor;
  }

  // Adding cameras gltf
  gltfloader.load(props.cameraURL, function (gltf) {
    const model = gltf.scene;
    model.traverse((child) => {
      if (child.type === "PerspectiveCamera") {
        // console.log(child)
        // child.userData.type = "hotspot"
        hotspots.push(child);
      }
    });
  });

  // Adding model gltf
  gltfloader.load(props.modelURL, function (gltf) {
    model = gltf.scene;
    model.userData.type = "sphere";
    // console.log('renders/' + hotspots[0].name + '.jpg')
    const initialposition = hotspots[0].position;
    const initialrotation = props.initialRotation;

    // Create shader material
    material = new THREE.ShaderMaterial({
      uniforms: {
        uTexture1: { type: 't', value: null },
        uTexture2: { type: 't', value: null },
        uTexture2update: { value: null },
        updateBlendFactor: { value: 1.0 },
        uRotation: { type: 'v3', value: new THREE.Vector3(initialrotation.x, initialrotation.y, initialrotation.z) },  // Rotation angles in radians
        uOffset: { type: 'v2', value: new THREE.Vector2(0.0, 0.0) },         // UV offset
        uWorldOffset1: { type: 'v3', value: new THREE.Vector3(0, 0, 0) }, // Initial world space offset (no offset)
        uWorldOffset2: { type: 'v3', value: new THREE.Vector3(0, 0, 0) },
        uZOffset: { type: 'f', value: 0.0 },  // Z-axis offset
        uMirrorU: { type: 'f', value: 0.0 },  // No horizontal mirroring
        uMirrorV: { type: 'f', value: 0.0 },  // No vertical mirroring
        uBlendFactor: { value: 0.0 },
        uWorldOffsetBlendFactor: { value: 0.0 },
      },
      vertexShader: vertexShader(),
      fragmentShader: fragmentShader(),
      side: 2,
    });

    // Create the GUI
    // const gui = new GUI();

    // // Create controls for rotation
    // const rotationFolder = gui.addFolder('Rotation');
    // rotationFolder.add(material.uniforms.uRotation.value, 'x', -Math.PI, Math.PI).name('Rotation X');
    // rotationFolder.add(material.uniforms.uRotation.value, 'y', -Math.PI, Math.PI).name('Rotation Y');
    // rotationFolder.add(material.uniforms.uRotation.value, 'z', -Math.PI, Math.PI).name('Rotation Z');
    // rotationFolder.open();

    // // Create controls for offset
    // const offsetFolder = gui.addFolder('Offset');
    // offsetFolder.add(material.uniforms.uOffset.value, 'x', -1, 1).name('Offset U');
    // offsetFolder.add(material.uniforms.uOffset.value, 'y', -1, 1).name('Offset V');
    // offsetFolder.open();

    // // Create controls for world offset
    // const worldOffsetFolder = gui.addFolder('World Offset');
    // worldOffsetFolder.add(material.uniforms.uWorldOffset1.value, 'x', -10, 10).name('World Offset X');
    // worldOffsetFolder.add(material.uniforms.uWorldOffset1.value, 'y', -10, 10).name('World Offset Y');
    // worldOffsetFolder.add(material.uniforms.uWorldOffset1.value, 'z', -10, 10).name('World Offset Z');
    // worldOffsetFolder.open();

    // // Create controls for Z offset
    // gui.add(material.uniforms.uZOffset, 'value', -10, 10).name('Z Offset');
    // gui.add(material.uniforms.uBlendFactor, 'value', 0, 1).name('Blend Factor');

    // // Create controls for mirroring
    // const mirrorFolder = gui.addFolder('Mirroring');
    // mirrorFolder.add(material.uniforms.uMirrorU, 'value', -1, 1).name('Mirror U').step(2); // -1 for mirror, 1 for normal
    // mirrorFolder.add(material.uniforms.uMirrorV, 'value', -1, 1).name('Mirror V').step(2); // -1 for mirror, 1 for normal
    // mirrorFolder.open();

    // Rotate texture by 90 degrees and offset 0.2 in U, 0.1 in V
    // material.uniforms.uRotation.value = 0; // 90-degree rotation
    // material.uniforms.uOffset.value.set(-4.390, 2.325);  // Offset U by 0.2, V by 0.1
    // material.uniforms.uZOffset.value = -0.021;
    // console.log(material)

    // Add outer sphere to fill gaps in model
    const geometry = new THREE.SphereGeometry(500, 32, 16);
    const sphere = new THREE.Mesh(geometry, material);
    model.traverse((child) => {
      if (child.isMesh) {
        child.material = material;
        sphere.position.copy(child.position);
      }
    });

    scene.add(model);

    scene.add(sphere);

    camera.position.set(initialposition.x, initialposition.y, initialposition.z);
    updateCameraOrbit();
    function projectHotspotsOnFloor (hotspots, model) {
      const floorRaycaster = new THREE.Raycaster();
      const downward = new THREE.Vector3(0, -1, 0);
      hotspots.forEach((hotspot) => {
        floorRaycaster.set(hotspot.position.clone(), downward); // Downward ray
        const intersects = floorRaycaster.intersectObject(model, true); // floorMesh is your 3D floor
        if (intersects.length > 0) {
          hotspot.userData.floorPosition = intersects[0].point; // Save the projected position
        }
      });
    }
    function createHotspotMesh (point) {

      // hotspot geometry, create custom texture instead of this
      // const hotspotGeometry = new THREE.CircleGeometry(0.1, 32); // Adjust size as needed
      const hotspotMaterial = new THREE.MeshBasicMaterial({ map: hotspotTexture, transparent: true, opacity: 0.5 });
      const hotspot = new THREE.Mesh(hotspotGeometry, hotspotMaterial);

      hotspot.position.copy(point.userData.floorPosition); // Use floorPosition if projected
      hotspot.position.y= hotspot.position.y+0.002;
      hotspot.rotation.x = -Math.PI / 2; // Rotate to align with the floor
      hotspot.userData = { id: point.id, targetPosition: point.position, targetName: point.name, type: "hotspot"}; // Add metadata for interactivity
      return hotspot;
    }

    function addHotspotsToScene (hotspots, scene, hotspotsMeshes) {
      hotspots.forEach((point) => {
        const hotspot = createHotspotMesh(point);
        scene.add(hotspot);
        point.userData.geometry=hotspot;
        hotspotsMeshes.push(hotspot);
      });
      if (showLoader.value) {
        showLoader.value = false;
      }
    }

    // create hotspots on the floor
    setTimeout(() => {
      projectHotspotsOnFloor(hotspots, model);
      addHotspotsToScene(hotspots, scene, hotspotsMeshes);
    }, 500);
    const initialLabel = Object.values(props.tourLabels).filter((item) => item.order===1)[0];

    if (!unitplan_redirected.value) {
      if (Object.values(props.tourLabels).length>0){
        const raypoint = new THREE.Vector3(initialLabel.camera_position.x, initialLabel.camera_position.y, initialLabel.camera_position.z);
        const distances = [];
        hotspots.forEach((hotspot) => {
          const dist = raypoint.distanceTo(hotspot.position);
          distances.push(dist);
          hotspot.userData.distance = dist;
        });
        const minDist = Math.min.apply(null, distances);
        hotspots.forEach((hotspot) => {
          if (hotspot.userData.distance === minDist) {
            if (initialLabel.camera_position && initialLabel.controls_target){
              camera_position.value = initialLabel.camera_position;
              if (initialLabel.controls_target.x !== 0 && initialLabel.controls_target.y !== 0 && initialLabel.controls_target.z !== 0) {
                controls_target.value = initialLabel.controls_target;
              }
            }
            console.log(hotspot.name, initialLabel);

            updateMaterial.value(hotspot.name, hotspot.position);
          }
        });
      } else {
        updateMaterial.value(hotspots[0].name, initialposition);
      }
    }
    if (props.activeLabel){
      setupUnitplanRedirect(props.activeLabel);
    }

  });

  // handle mouse movements
  const addRaycaster = () => {
    let mousemove = 0;

    // Create a raycaster
    raycaster = new THREE.Raycaster();

    // Create a mouse vector to store normalized device coordinates
    mouse = new THREE.Vector2();

    containerVal.value.addEventListener('mousedown', () => {
      mousedown = 1;
      clearTimeout(timerForRaycast);
      needsUpdate = false;

    });

    // Function to handle mouse move event
    function onMouseMove (event) {
      // Calculate normalized device coordinates (-1 to +1)
      mouse.x = ((event.clientX / window.innerWidth) * 2) - 1;
      mouse.y = -((event.clientY / window.innerHeight) * 2) + 1;
      mousedown !== 0 ? mousemove = 1 : mousemove = 0;
      // mousemove=1
      // if (!mousedown && !animating) {
      //     timerForRaycast= setTimeout(()=>{needsUpdate = true;},1000)
      // }

      // doRayCasting();

    }

    // Add an event listener for the mouse move event
    containerVal.value.addEventListener('mousemove', onMouseMove);

    // Function to handle click event and transition to next hotspot
    function onClick () {
      camera_position.value = null;
      controls_target.value = null;
      needsUpdate = false;
      if (!mousemove) {
        raycaster.setFromCamera(mouse, camera);

        // Find all intersected objects
        const intersects = raycaster.intersectObjects(scene.children.filter((obj) => obj.userData.type === 'hotspot' || obj.userData.type === 'sphere'));

        // Check if any object was clicked
        if (intersects.length > 0) {
          const object = intersects[0].object;
          // const hotspot = object;
          if (object.userData.type === "hotspot") {
            // Convert to array of entries so we can search
            selectionId.value = list.value.findIndex((item) => item.camera_name === object.userData.targetName);
            updateMaterial.value(object.userData.targetName, object.userData.targetPosition);
            object.material.opacity = 0.7;
            object.material.map= hotspotSelectedTexture;
            // animateCameraPosition(camera.position, hotspot.userData.sphere.position, 1200);
            // animateSphere(hotspot)
          } else {
            const raypoint = intersects[0].point;
            const distances = [];
            hotspots.forEach((hotspot) => {
              const dist = raypoint.distanceTo(hotspot.position);
              distances.push(dist);
              hotspot.userData.distance = dist;
            });

            const minDist = Math.min.apply(null, distances);

            hotspots.forEach((hotspot) => {
              if (hotspot.userData.distance === minDist) { // && currentHotspot!=hotspot) {
                // model.visible=true;
                selectionId.value = list.value.findIndex((item) => item.camera_name === hotspot.name);
                cursor.visible = false;
                hotspot.userData.geometry.material.opacity = 0.7;
                hotspot.userData.geometry.material.map= hotspotSelectedTexture;
                updateMaterial.value(hotspot.name, hotspot.position);

                // setTimeout(() => {

                // }, 2000)

                // animateSphere(hotspot)
              }
            });

          }

        }

      }

      // mousemove=0;
      // hotspotsMeshes.forEach((hotspotmesh) => {
      //   hotspotmesh.visible=true
      //   //hotspotmesh.material.map= hotspotTexture
      // });

    }
    containerVal.value.addEventListener('mouseup', () => {
      mousedown = 0;
      onClick();
      setTimeout(mousemove = 0, 2000);
      timerForRaycast = setTimeout(() => {
        if (!animating) {
          needsUpdate = true;
        }
      }, 500);

    });

    // Add an event listener for the click event
    // document.addEventListener('click', onClick);
  };

  // tracker the cursor with raycasting to trace agains the model. Runs every tick when needsupdate is true
  function doRayCasting () {
    if (needsUpdate && !isMobileRef.value) {
      raycaster.setFromCamera(mouse, camera);

      // Find all intersected objects
      const intersects = raycaster.intersectObjects(scene.children.filter((obj) => obj.userData.type === 'hotspot' || obj.userData.type === 'sphere'));
      // Check if any object was clicked
      if (intersects.length > 0) {
        const object = intersects[0].object;
        if (object.userData.type === "hotspot") {
          // console.log(object)
          object.material.opacity = 1;
          object.material.map= hotspotSelectedTexture;
          cursor.visible = false;
          document.body.style.cursor = "pointer";
        } else {
          document.body.style.cursor = "auto";
          cursor.visible = true;
          hotspotsMeshes.forEach((hotspotmesh) => {
            hotspotmesh.material.opacity = 0.5;
            hotspotmesh.material.map= hotspotTexture;
          },
          );
        }
        const intersection = intersects[0];
        cursor.position.copy(intersection.point);
        const normal = intersection.face.normal.clone();
        normal.transformDirection(intersection.object.matrixWorld);
        // cursor.position.addScaledVector(normal, 0.01);
        cursor.lookAt(cursor.position.clone().add(normal));
      }
      if (mousedown || animating) {
        needsUpdate = false;

      }
      // else {
      //     // Hide cursor if nothing is intersected
      //     cursor.visible = false;
      // }
    }
  }

  // Function to update the texture blend uniforms only
  function updateTextureBlend ({ blendFactor }) {
    material.uniforms.uBlendFactor.value = blendFactor;
  }

  // Animate camera position
  const animateCameraPosition = (targetPosition, duration) => {
    const easing=TWEEN.Easing.Sinusoidal.InOut;
    // let startTime;
    // let elapsedTime = 0;
    // model.visible=true;
    // controls.enabled = false;
    let targetTarget;
    // targetPosition.y = camera.position.y;
    if (camera_position.value &&  controls_target.value){
      targetPosition = camera_position.value;
      targetTarget = controls_target.value;
    }
    const tween = new TWEEN.Tween(camera.position).to(targetPosition, duration);
    tween.easing(easing);
    tween.start();
    tween.onStart(function () {
      // startTime = performance.now();

    }.bind(this));
    tween.onUpdate(function () {
      // elapsedTime = performance.now() - startTime;
      updateCameraOrbit();
    }.bind(this));
    tween.onComplete(function () {
      updateCameraOrbit();

    }.bind(this));

    // Initial states for blending and offsetting
    const startBlend = { blendFactor: 0.0 };
    const targetBlend = { blendFactor: 1.0 };

    const startWorldOffsetBlend = { worldOffsetBlendFactor: 0.0 };
    const targetWorldOffsetBlend = { worldOffsetBlendFactor: 1.0 };

    // TWEEN.js for world offset blending (decoupled from texture blending)
    const offsetTween = new TWEEN.Tween(startWorldOffsetBlend)
      .to(targetWorldOffsetBlend, 0)  // 3-second offset transition
      .easing(easing)  // Smoother easing for world offset
      .onUpdate(updateWorldOffset)
      .onComplete(() => {
        material.uniforms.uWorldOffset1.value.copy(material.uniforms.uWorldOffset2.value);

        material.uniforms.uWorldOffsetBlendFactor.value = 0;
        if (camera_position.value && controls_target.value) {
          const ttween = new TWEEN.Tween(controls.target).to(targetTarget, duration);
          ttween.easing(easing);
          ttween.start();
          ttween.onComplete(() => {
            animating = false;
            startTransitionRef.value = false;
            timerForRaycast = setTimeout(() => {
              needsUpdate = true;
            }, 100);
          });
        } else {
          animating = false;
          startTransitionRef.value = false;
          timerForRaycast = setTimeout(() => {
            needsUpdate = true;
          }, 100);
        }
        hotspotsMeshes.forEach((hotspotmesh) => {
          hotspotmesh.material.map=hotspotTexture;
          hotspotmesh.visible=true;
          if (hotspotmesh.userData.targetName===imagename){
            hotspotmesh.visible=false;
          }
        });
        // setTimeout(() => {
        //    // material.uniforms.uTexture1.value = material.uniforms.uTexture2.value
        //    // material.uniforms.uBlendFactor.value = 0

        // }, 250)

        // model.visible=false;
      });

    // TWEEN.js for texture blending
    const blendTween = new TWEEN.Tween(startBlend)
      .to(targetBlend, duration)  // 5-second blend duration
      .easing(easing)
      .onUpdate(updateTextureBlend)
      .onComplete(() => {
        // Optionally start world offset tween here, or run it concurrently
        offsetTween.start();
      });

    // Start the texture blending animation
    blendTween.start();

  };

  // Helper function to combine tiles into a single texture
  function combineAndApplyTiles (tiles, rows, columns) {
    if (!updateTexture) {
      return null;
    }
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    const tileSize = 512; // Adjust to your tile size
    canvas.width = tileSize * columns;
    canvas.height = tileSize * rows;

    tiles.forEach((tile, i) => {
      if (!updateTexture) {
        return;
      }
      const x = (i % columns) * tileSize;
      const y = Math.floor(i / columns) * tileSize;
      if (tile) {
        context.drawImage(tile.image, x, y, tileSize, tileSize);
      }
    });

    const texture = new THREE.Texture(canvas);
    // texture.encoding = THREE.sRGBEncoding;
    texture.anisotropy = maxAnisotropy;
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.generateMipmaps = false;
    texture.minFilter = THREE.LinearFilter;
    // texture.magFilter = THREE.LinearFilter;
    texture.needsUpdate = true;
    if (!updateTexture) {
      return null;
    }
    return texture;
  }

  // Function to load tiles for an LOD in batches
  function loadTilesForLOD (lod) {
    if (!updateTexture) {
      return;
    }
    // const tileSize = 512; // Replace with actual size if different
    const tilesToLoad = [];
    const loader = new THREE.TextureLoader();
    if (!lod) {
      return;
    }
    // Collect tiles to load
    for (let row = 0; row < lod.rows; row++) {
      if (!updateTexture) {
        return;
      }
      for (let col = 0; col < lod.columns; col++) {
        if (!updateTexture) {
          return;
        }
        const tileIndex = (row * lod.columns) + col;
        // console.log(lod)
        // if (!loadedTiles[lod.level][tileIndex]) {
        const url = `${props.storagePath}/${imagename}/lod_${lod.level}/tile_${row}_${col}.jpg`;
        // console.log(url)
        tilesToLoad.push({ url, index: tileIndex });
        // }
      }
      // if (lod.level == 2 && row == 1) {
      //     startTransitionRef.value = true
      // }
    }

    // Helper to load a batch of tiles
    function loadBatch (batch) {
      if (!updateTexture) {
        return null;
      }
      return Promise.all(
        batch.map((tile) =>
          new Promise((resolve, reject) => {
            if ((tile.url).includes(`${imagename}/`)){
              loader.load(
                tile.url,
                (texture) => {
                  // console.log(tile.url)
                  if (!updateTexture) {
                    return;
                  }
                  loadedTiles[lod.level][tile.index] = texture;
                  if (lod.level === 2 && tile.index === 1) {
                    startTransitionRef.value = true;
                  }
                  resolve(texture);
                },
                undefined,
                (err) => {
                  reject(err);
                },
              );
            }
          }),
        ),
      );
    }

    // Process tiles in batches
    return new Promise((resolve) => {
      if (!updateTexture) {
        return;
      }
      let batches = [];
      // console.log(tilesToLoad)
      for (let i = 0; i < tilesToLoad.length; i += batchSize) {
        if (!updateTexture) {
          return;
        }
        batches.push(tilesToLoad.slice(i, i + batchSize));
      }

      let currentBatchIndex = 0;

      function processNextBatch () {
        if (!updateTexture) {
          loadedTiles = lods.map(() => []);
          batches=[];
          return;
        }
        if (currentBatchIndex >= batches.length) {
          // console.log(batches.length)
          // Combine and resolve once all batches are loaded
          resolve(combineAndApplyTiles(loadedTiles[lod.level], lod.rows, lod.columns));
          if (currentLODIndex >= lods.length) {
            updateTexture = false;
            // currentLODIndex = 0;
            // lastUpdateTime = 0;
            // loadedTiles = lods.map(() => []);
            // loadedTiles = lods.map(() => []);
            return;
          }

          currentLODIndex++;

          return;
        }

        loadBatch(batches[currentBatchIndex]).then(() => {
          // console.log(currentBatchIndex)
          if (!updateTexture) {
            loadedTiles = lods.map(() => []);
            batches=[];
            return;
          }
          currentBatchIndex++;
          setTimeout(processNextBatch, updateInterval); // Adjust delay if needed
        });
      }

      processNextBatch();
    });
  }

  // Update logic with blending
  function updateTileLoading () {
    const currentTime = Date.now();
    if (!updateTexture) {
      return;
    }
    if (currentLODIndex >= lods.length) {
      currentLODIndex = 2;
      // updateTexture = false;
      // currentLODIndex = 0;
      // lastUpdateTime = 0;
      // material.uniforms.uTexture1.value = material.uniforms.uTexture2.value
      // material.uniforms.uBlendFactor.value=0;
      // console.log(loadedTiles)
      // loadedTiles = lods.map(() => []);
      // return
    }
    // console.log(currentLODIndex)

    const lod = lods[currentLODIndex];
    // console.log(lods)
    if (!lod) {
      return;
    }
    if (currentTime - lastUpdateTime > lodLoadDelay) {
      if (!updateTexture) {
        return;
      }
      loadTilesForLOD(lod).then((texture) => {
        if (!updateTexture) {
          return;
        }
        // console.log(texture)
        nextTexture = texture;
        // blendFactor = 0;
        currentTexture = nextTexture;
        material.uniforms.uTexture2.value = currentTexture;
        // if (!animating) {
        //     if (!updateTexture) return;
        //     material.uniforms.uTexture1.value = currentTexture

        // }

        material.uniforms.uTexture2update.value = nextTexture;

        //  material.needsUpdate = true;

      });

      lastUpdateTime = currentTime;

    }

  }

  function onWindowResize () {

    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();

    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);

  }
  window.addEventListener('resize', onWindowResize);
  function render () {
    // const delta = clock.getDelta();
    // requestAnimationFrame(render);
    TWEEN.update();
    renderer.render(scene, camera);
    controls.update();
    doRayCasting();
    // console.log(updateTexture)
    // console.log(updateTexture)
    if (updateTexture) {
      updateTileLoading();
    }

  }
  animate.value = () => {

    renderer.setAnimationLoop(render);

  };

  addRaycaster();
  animate.value();

  watch(startTransitionRef, (newVal) => {
    if (newVal === true) {
      animating = true;
      // setTimeout(() => {

      animateCameraPosition(imagePosition, 1000);
      // }, 0);
    }
  });
});
function NearByFloatingButtonFilterData (data){
  list.value =[];
  Object.entries(data)
    .sort(([, a], [, b]) => {
      const hasOrderA = typeof a.order === "number";
      const hasOrderB = typeof b.order === "number";

      if (hasOrderA && hasOrderB) {
        return a.order - b.order;
      }
      if (hasOrderA) {
        return -1;
      } // a comes before b
      if (hasOrderB) {
        return 1;
      }  // b comes before a
      return 0; // keep original order if neither has 'order'
    })
    .forEach(([key, value]) => {
      list.value.push({
        value: value.name,
        id: key,
        camera_name: value.camera_name,
        camera_position: value.camera_position,
        controls_target: value.controls_target,
      });
    });

  return list.value;
}

function removeMaterial (obj) {

  if (obj.material) {
    obj.material.dispose();
    if (obj.material.map) {
      obj.material.map.dispose();
    }
    if (obj.material.lightMap) {
      obj.material.lightMap.dispose();
    }
    if (obj.material.aoMap) {
      obj.material.aoMap.dispose();
    }
    if (obj.material.emissiveMap) {
      obj.material.emissiveMap.dispose();
    }
    if (obj.material.bumpMap) {
      obj.material.bumpMap.dispose();
    }
    if (obj.material.normalMap) {
      obj.material.normalMap.dispose();
    }
    if (obj.material.displacementMap) {
      obj.material.displacementMap.dispose();
    }
    if (obj.material.roughnessMap) {
      obj.material.roughnessMap.dispose();
    }
    if (obj.material.metalnessMap) {
      obj.material.metalnessMap.dispose();
    }
    if (obj.material.alphaMap) {
      obj.material.alphaMap.dispose();
    }
  }

}
function removeGeometry (obj) {
  if (obj.geometry) {
    obj.geometry.dispose();
    obj.geometry.attributes.color = {};
    obj.geometry.attributes.normal = {};
    obj.geometry.attributes.position = {};
    obj.geometry.attributes.uv = {};
    obj.geometry.attributes = {};
    obj.material = {};
  }
}
function clearCache () {
  if (Cache && Cache.files) {
    for (const elem in Cache.files) {
      Cache.files[elem] = "";
      Cache.remove(elem);
    }
  }
}
onBeforeUnmount(() => {
  scene.traverse(function (obj) {
    removeMaterial(obj);
    removeGeometry(obj);
  });
  clearCache();
  cancelAnimationFrame(animate.value);
  TWEEN.removeAll();
  dracoLoader.dispose();
  controls.dispose();
  gltfloader.unregister();
  renderer.dispose();
  // clearMeshMemory();
});
watch(() => props.activeLabel, (newVal) => {
  setupUnitplanRedirect(newVal);
});
</script>
<template>
  <CircularLoader v-if="showLoader" />
  <div
    id="container"
    ref="containerVal"
  />
  <div
    class="fixed w-full flex justify-center z-[3]"
    :class="(props.isMobile ? 'bottom-32' : 'bottom-10')"
  >
    <NearByFloatingButton
      class="flex"
      :class="isMobile ? 'w-full' : 'sm:w-[38%] md:w-[48%] lg:w-[60%] xl:[75%]'"
      :itemsList="NearByFloatingButtonFilterData(props.tourLabels)"
      :sliderButton="true"
      :active="selectionId"
      :leftButton="leftButton"
      :rightButton="rightButton"
      :objectIconKey="`icon`"
      :objectNameKey="`value`"
      @button-clicked="moveTo"
    />
  </div>
</template>

<style scoped>
#container {
    position: absolute;
    top: 0;
    left: 0;
    /* width: 100%;
    height: 100%; */
    /* image-rendering: pixelated; */
}
</style>
