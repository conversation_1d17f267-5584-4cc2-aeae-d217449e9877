<script setup>
import { onMounted, watch, ref, computed } from 'vue';
import NearByFloatingButton from '../components/ALEComponents/NearByFloatingButton/NearByFloatingButton.vue';
import { creationToolStore } from '../store';
// import data from '../../landmark.json'
import { useRoute } from 'vue-router';
import LandMarkCard from '../components/ALEComponents/LandMarkCard/LandMarkCard.vue';
import NewAleSideBar from '../components/ALEComponents/NewAleSideBar/NewAleSideBar.vue';
import NavBar from '../components/ALEComponents/NavBar/NavBar.vue';
import router from '../router';
import { mapStyles } from '../helpers/mapStyles';

var google;
const activeCategory = ref(0);
const Store = creationToolStore();
const mapRef = ref(null);
const route = useRoute();
const project = ref(null);
const data = ref({});
const selectedLandmark = ref(null);

// Computed property for isRoot to make it reactive
const isRoot = computed(() => {
  return !Store.getLastVisitedRoute('previous_map');
});

// Initialize translation map if it doesn't exist
if (!Store.translationMap) {
  Store.translationMap = {};
}
if (!Store.sidebarOptions[route.params.projectId]) {
  Store.getOptions(route.params.organizationId, route.params.projectId).then((res) => {
    const mapOption = Object.values(res).find(
      (item) => item.name.toLowerCase() === "map",
    );
    if (mapOption) {
      Store.activeOptionId = mapOption._id;
    }
  });
} else {
  const mapOption = Object.values(
    Store.sidebarOptions[route.params.projectId],
  ).find((item) => item.name.toLowerCase() === "map");
  if (mapOption) {
    Store.activeOptionId = mapOption._id;
  }
}
if (!Store.organization_thumbnail) {
  Store.getOrganization(route.params.organizationId);
}
Store.updateSceneType(route.fullPath);
// Function to handle clicks outside the landmark card
function handleClickOutside (event) {
  const landmarkCard = document.querySelector('.landmark-card');
  const isClickOnMarker = event.target.closest && (
    event.target.closest('[title]') || // Check if click is on a marker (markers have title attribute)
    event.target.closest('.gm-marker') || // Google Maps marker class
    event.target.closest('[data-marker]') // Any element with data-marker attribute
  );

  if (selectedLandmark.value && landmarkCard && !landmarkCard.contains(event.target) && !isClickOnMarker) {
    console.log('Click outside detected, closing landmark card');
    selectedLandmark.value = null;
  }
}

window.addEventListener('resize', Store.callbackFunctionMonitorChanges);
Store.callbackFunctionMonitorChanges();

const leftButton = `<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.375 10.5L2.625 10.5" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7 14.875L2.625 10.5L7 6.125" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`;
const rightButton = `<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.625 10.5L18.375 10.5" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14 14.875L18.375 10.5L14 6.125" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`;

let MapLib, MarkerLib, DirectionsRendererLib;
let renderer;
const markers = [];

async function loadGoogleMapsApi () {
  if (!window.google || !window.google.maps) {
    await new Promise((resolve) => {
      const script = document.createElement("script");
      script.src = `https://maps.googleapis.com/maps/api/js?key=${import.meta.env.VITE_MAPS_API_KEY}&libraries=maps`;
      script.async = true;
      script.defer = true;
      script.onload = resolve;
      document.head.appendChild(script);
    });
  }
  google = window.google;
  const [{ Map }, { Marker }, { DirectionsRenderer  }] = await Promise.all([
    google.maps.importLibrary('maps'),
    google.maps.importLibrary('marker'),
    google.maps.importLibrary("routes"),
  ]);

  MapLib               = Map;
  MarkerLib            = Marker;
  DirectionsRendererLib = DirectionsRenderer;
}

function clearMap () {
  markers.forEach((m) => m.setMap(null));
  markers.length = 0;
  if (renderer) {
    renderer.setDirections({ routes: [] });
  }  // clear drawn route
}

function drawCategory (index) {
  if (!mapRef.value) {
    return;
  }
  clearMap();
  const categories = Object.keys(data.value.category);
  const catName   = categories[index];

  const placesObj = data.value.category[catName];
  const bounds = new google.maps.LatLngBounds();

  Object.values(placesObj).forEach((loc) => {
    if (!loc?.lat & !loc?.long){
      return;
    }
    const customIcon = {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
        <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M42 20.4697C42 35.2857 24.5 47.0058 24.5 47.0058C24.5 47.0058 7 33.9589 7 20.4697C7 10.8214 14.835 3 24.5 3C34.165 3 42 10.8214 42 20.4697Z" fill="#B378FF"/>
          <path d="M19.181 42.3855C21.0394 44.2358 23.527 46.1993 24.4846 47.0061C26 46.0002 30.0133 43.0754 34 38.5002C37.8582 34.0724 39.5112 30.5039 39.9532 29.4038C37.7434 31.8241 31.9721 34.3928 27.5783 36.0046C23.3797 37.5448 18.3727 38.9403 15.5 38.5002C16.0893 39.307 17.4132 40.6253 19.181 42.3855Z" fill="#7E29EB"/>
          <path d="M24.4997 36.1581C33.1473 36.1581 40.1576 29.1478 40.1576 20.5002C40.1576 11.8525 33.1473 4.84229 24.4997 4.84229C15.8521 4.84229 8.8418 11.8525 8.8418 20.5002C8.8418 29.1478 15.8521 36.1581 24.4997 36.1581Z" fill="white"/>
          <path d="M16.667 28.3335H33.3337" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M26.667 13.3335H23.3337C21.9548 13.3335 21.667 13.6213 21.667 15.0002V28.3335H28.3337V15.0002C28.3337 13.6213 28.0459 13.3335 26.667 13.3335Z" stroke="black" stroke-linejoin="round"/>
          <path d="M21.6667 20.8335H19.1667C17.7878 20.8335 17.5 21.1212 17.5 22.5002V28.3335H21.6667V20.8335Z" stroke="black" stroke-linejoin="round"/>
          <path d="M30.833 20.8335H28.333V28.3335H32.4997V22.5002C32.4997 21.1212 32.2119 20.8335 30.833 20.8335Z" stroke="black" stroke-linejoin="round"/>
          <path d="M25 13.3332V11.6665" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M25 28.3332V26.6665" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M24.167 22.4998H25.8337M24.167 19.5832H25.8337M24.167 16.6665H25.8337" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      `),
      scaledSize: new google.maps.Size(40, 40), // control size here
    };
    const pos = { lat: +loc.lat, lng: +loc.long };

    const marker = new MarkerLib({
      position: pos,
      map: mapRef.value,
      icon: customIcon,
      title: loc.name,
    });

    bounds.extend(pos);
    markers.push(marker);

    /* click → fetch the stored JSON and draw the route */
    marker.addListener('click', async (event) => {
      // Prevent the click from bubbling up to the document
      if (event && event.stopPropagation) {
        event.stopPropagation();
      }

      selectedLandmark.value = loc;
      if (loc?.driving) {
        try {
          const res  = await fetch(loc.driving);
          const json = await res.json();
          if (json.status === 'OK') {
            renderer.setDirections(json);
          } else {
            console.warn('Route JSON status:', json.status);
          }
        } catch (err) {
          console.error('Could not load route JSON', err);
        }
      }
    });
  });
  if (!bounds.isEmpty()) {
    mapRef.value.fitBounds(bounds);
  }
}

function NearByFloatingButtonFilterData (data) {
  const list = Object.keys(data).map((element) => {
    return {name: element};
  });
  return list;
}

async function initMap () {
  await loadGoogleMapsApi();

  /* origin / main site */
  const originPos = { lat: +data.value.lat, lng: +data.value.lng };

  const map = new MapLib(document.getElementById('map'), {
    center: originPos,
    zoom: 10,
    mapTypeId: 'roadmap',
    styles: mapStyles,
  });

  mapRef.value = map;

  /* one-off: draw the “main site” marker in a distinct colour */
  new MarkerLib({
    position: originPos,
    map,
    title: 'Origin / Main site',
  });

  // originMarker.addListener('click', () => {
  //   const router = useRoute(); // Not correct – see below
  //   router.push({ name: 'YourTargetRouteName', params: { id: '123' } });
  // });

  /* renderer shared by every route */
  renderer = new DirectionsRendererLib({
    map,
    suppressMarkers: true,
  });

  /* draw the first category straight away */
  drawCategory(activeCategory.value);
}

function formatData (landmarklist) {
  const category={};
  for (const landMark in landmarklist){
    if (!category[landmarklist[landMark].category]){
      category[landmarklist[landMark].category] = {};
      category[landmarklist[landMark].category][landmarklist[landMark]._id] = landmarklist[landMark];
    } else {
      category[landmarklist[landMark].category][landmarklist[landMark]._id] = landmarklist[landMark];
    }
  }
  return category;
}

const observer = new MutationObserver(() => {
  const cameraControl = document.querySelector('gmp-internal-camera-control');
  const streetview = document.getElementsByClassName('gm-svpc')[0];
  if (cameraControl) {
    cameraControl.style.display = 'none';
    streetview.style.display = 'none';
    observer.disconnect(); // Stop observing once it's hidden
  }
});

observer.observe(document.body, { childList: true, subtree: true });

onMounted(async () => {
  if (Object.keys(Store.projectCardData).length === 0){
    await Store.getListofProjects(route.params.organizationId, route.params.projectId).then((res) => project.value = res.data[route.params.projectId]);
  } else {
    project.value = Store.projectCardData[route.params.projectId];
  }
  data.value.lat = project.value?.projectSettings.general.lat;
  data.value.lng = project.value?.projectSettings.general.long;

  if (Object.keys(Store.landmarkData).length === 0){
    await Store.getListofLandmark(route.params.projectId, route.params.organizationId).then((res) => data.value.category = formatData (res.data));
  } else {
    data.value.category = formatData (Store.landmarkData);
  }

  // Initialize translations
  if (!Store.translationMap) {
    await Store.getTranslation(route.params.organizationId);
  }

  initMap();

  // Add click event listener to handle clicks outside landmark card
  document.addEventListener('click', handleClickOutside);
});

/* switch category whenever the floating button changes it */
watch(activeCategory, (idx) => drawCategory(idx));

const handleBackNavigation =() => {
  const previousMapRoute = Store.getLastVisitedRoute('previous_map');
  router.push(previousMapRoute);
};

function moveToLocation (id, type, optionId){
  // Check if there's a last visited route for this option
  const lastVisitedRoute = Store.getLastVisitedRoute(optionId);

  if (lastVisitedRoute && lastVisitedRoute.name) {
    router.push(lastVisitedRoute);
    return;
  }

  // Default navigation logic
  if (type==='masterscene'){
    router.push({name: 'masterScene', params: {sceneId: id} });
  } else if (type==='earth'){
    router.push({name: 'globeScene'});
  } else if (type==='projectscene') {
    router.push({name: 'projectScene', params: {sceneId: id}});
  } else if (type === 'unitplan'){
    router.push({ name: 'unitplansviewer'});
  } else if (type === 'amenity'){
    router.push({ name: 'amenity'});
  } else if (type==='map') {
    router.push({name: 'map'});
  } else {
    router.push( { name: 'galleryview'} );
  }
}

onMounted(() => {
  window.removeEventListener('resize', Store.callbackFunctionMonitorChanges);
  // Clean up click event listener
  document.removeEventListener('click', handleClickOutside);
});
</script>

<template>
  <div>
    <div
      id="map"
      class="top-0 left-0 h-full w-full flex"
    />
    <div class="absolute top-[50%] z-[4]">
      <NewAleSideBar
        v-if="Object.values(Store.sidebarOptions).length"
        :sidebarList="Store.sidebarOptions[route.params.projectId]"
        @select-option="moveToLocation"
      />
    </div>
    <NavBar
      :is-root="isRoot"
      @handle-navigation="handleBackNavigation"
    />
    <LandMarkCard
      v-if="selectedLandmark"
      :key="selectedLandmark._id || 'landmark'"
      :name="selectedLandmark.name"
      :description="selectedLandmark.description"
      :distance="selectedLandmark.distance"
      :carTiming="selectedLandmark.car_timing"
      :transitTiming="selectedLandmark.transit_timing"
      :walkTiming="selectedLandmark.walk_timing"
      :landmarkId="selectedLandmark._id"
      :thumbnail="selectedLandmark.thumbnail"
      :isMobile="false"
      class="descriptionAnimation items-center absolute landmark-card bottom-24 left-1/2 transform -translate-x-1/2"
    />
    <div
      class="fixed w-full flex flex-col items-center z-[3] gap-4"
      :class="Store.isMobile ? 'bottom-24' : 'bottom-10'"
    >
      <NearByFloatingButton
        v-if="data?.category && NearByFloatingButtonFilterData(data['category']).length > 1"
        class="flex"
        :class="'w-full sm:w-[38%] md:w-[48%] lg:w-[60%] xl:[75%]'"
        :itemsList="NearByFloatingButtonFilterData(data['category'])"
        :sliderButton="true"
        :active="activeCategory"
        :leftButton="leftButton"
        :rightButton="rightButton"
        :objectNameKey="`name`"
        @button-clicked="(item,index)=>activeCategory=index"
      />
    </div>
  </div>
</template>

<style scoped>
#map {
  width: 100vw;
  height: 100vh;
}
gmp-internal-camera-control {
  display: none !important;
}
.descriptionAnimation
{
  animation:des-popup 0.5s cubic-bezier(0.45, 0.05, 0.55, 0.95) forwards;
}
</style>
