
<script setup>
import { useRoute } from 'vue-router';
import { onMounted, ref } from 'vue';
import { creationToolStore } from '../store';
import ProjectCards from '../components/ALEComponents/ProjectCards/ProjectCards.vue';
import Navbar from '../components/ALEComponents/NavBar/NavBar.vue';

const Store = creationToolStore();
const route = useRoute();
const projectsData = ref([]);
const showPage = ref(false);
onMounted(() => {
  Store.getOrganizationByUniqueId(route.params.organizationId).then((org) => {
    Store.getOrganization(org._id);
    Store.getTranslation(org._id);
    Store.getListofProjects(org._id).then(() => {
      projectsData.value = Store.projectCardData;
      showPage.value = true;
    });
  });

});
Store.callbackFunctionMonitorChanges();
const fontType = Store.projectCardData[route.params.projectId]?.projectSettings?.theme?.font_type
      ?? Store.organizationDetails?.font_type;
Store.loadedFont = fontType;
Store.updateSceneType(route.fullPath);
</script>

<template>
  <div
    v-if="showPage"
    :style="{ fontFamily: Store.loadedFont }"
    class="w-full h-full bg-[#111928]"
  >
    <div class="relative h-[10%] w-full">
      <Navbar
        :is-organization="true"
        :is-root="true"
      />
    </div>

    <div
      v-if="!Store.isMobile"
      class="flex flex-col h-[90%] w-full justify-around"
    >
      <div class="w-[803px] h-[10%] flex items-start flex-col relative left-[2%] gap-4">
        <div class="w-full h-full flex flex-col justify-around">
          <p class="self-stretch text-white  text-xl font-normal ">
            Projects
          </p>
          <p className="text-gray-400 text-sm font-normal">
            Select Projects to explore
          </p>
        </div>
        <!-- <div class="w-full flex flex-row items-center gap-4">
          <div class="h-[40px] w-[40%]">
            <div className="h-full w-full px-4 py-3 bg-[#1f2a37] rounded-lg border border-gray-600 justify-start items-center gap-2.5 inline-flex">
              <div className="grow shrink basis-0 h-[18px] justify-start items-center gap-2.5 flex">
                <div className="w-4 h-4 relative">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      id="search-outline"
                      clip-path="url(#clip0_6193_1746)"
                    >
                      <g id="Vector">
                        <path
                          d="M6.40008 12.8C5.13426 12.8 3.89687 12.4246 2.84439 11.7214C1.7919 11.0182 0.971586 10.0186 0.48718 8.84917C0.00277303 7.67973 -0.12397 6.3929 0.122979 5.15142C0.369927 3.90994 0.979475 2.76957 1.87454 1.87452C2.76961 0.979463 3.90999 0.369922 5.15148 0.122977C6.39298 -0.123968 7.67982 0.002773 8.84928 0.487174C10.0187 0.971574 11.0183 1.79188 11.7215 2.84435C12.4248 3.89683 12.8002 5.1342 12.8002 6.4C12.7982 8.0968 12.1233 9.72356 10.9235 10.9234C9.72367 12.1232 8.0969 12.7981 6.40008 12.8ZM6.40008 1.6C5.45072 1.6 4.52267 1.88152 3.73331 2.40895C2.94394 2.93638 2.32871 3.68604 1.9654 4.56312C1.6021 5.4402 1.50704 6.40532 1.69225 7.33643C1.87746 8.26754 2.33463 9.12282 3.00593 9.79411C3.67723 10.4654 4.53251 10.9226 5.46363 11.1078C6.39475 11.293 7.35988 11.1979 8.23698 10.8346C9.11407 10.4713 9.86374 9.85609 10.3912 9.06674C10.9186 8.27738 11.2001 7.34935 11.2001 6.4C11.1989 5.12735 10.6927 3.90719 9.79283 3.00729C8.89292 2.10739 7.67274 1.60127 6.40008 1.6Z"
                          fill="#9CA3AF"
                        />
                        <path
                          d="M15.2002 16C14.988 16 14.7846 15.9156 14.6346 15.7656L11.4345 12.5656C11.2888 12.4147 11.2082 12.2126 11.21 12.0029C11.2118 11.7931 11.2959 11.5925 11.4443 11.4441C11.5926 11.2958 11.7932 11.2117 12.003 11.2099C12.2128 11.208 12.4148 11.2887 12.5657 11.4344L15.7658 14.6344C15.8776 14.7463 15.9538 14.8888 15.9846 15.044C16.0155 15.1991 15.9996 15.36 15.9391 15.5061C15.8786 15.6523 15.7761 15.7772 15.6445 15.8651C15.513 15.953 15.3584 16 15.2002 16Z"
                          fill="#9CA3AF"
                        />
                      </g>
                    </g>
                    <defs>
                      <clipPath id="clip0_6193_1746">
                        <rect
                          width="16"
                          height="16"
                          fill="white"
                        />
                      </clipPath>
                    </defs>
                  </svg>
                </div>
                <p className="grow shrink basis-0 text-gray-400 text-sm font-normal leading-[17.50px]">
                  Search project
                </p>
              </div>
            </div>
          </div>
          <div>
            <div className="h-[40px] justify-start items-center gap-3 inline-flex">
              <div className="text-gray-400 text-sm font-normal leading-[21px]">
                Sort by :
              </div>
              <div className="justify-start items-center gap-5 flex">
                <div className="px-4 py-0.5 h-[30px] bg-gray-700 rounded-md justify-center items-center gap-1 flex cursor-pointer">
                  <p className="text-center text-white text-xs font-normal leading-[18px]">
                    All
                  </p>
                </div>
                <div className="px-4 py-0.5 h-[30px] bg-[#1f2a37] rounded-md justify-center items-center gap-1 flex cursor-pointer">
                  <p className="text-center text-gray-400 text-xs font-normal leading-[18px]">
                    Villa
                  </p>
                </div>
                <div className="px-4 py-0.5 h-[30px] bg-[#1f2a37] rounded-md justify-center items-center gap-1 flex cursor-pointer">
                  <p className="text-center text-gray-400 text-xs font-normal leading-[18px]">
                    Tower
                  </p>
                </div>
                <div className="px-4 py-0.5 h-[30px] bg-gray-700 rounded-md justify-center items-center gap-1 flex cursor-pointer">
                  <p className="text-center text-white text-xs font-normal leading-[18px]">
                    Location
                  </p>
                  <div className="w-4 h-4 relative origin-top-left">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g id="chevron-left-outline">
                        <path
                          id="Vector"
                          d="M12.6668 9.76867L12.1668 9.76878C12.1668 9.67435 12.1276 9.57846 12.0491 9.50375L12.049 9.50369L8.32032 5.95533C8.3203 5.95531 8.32028 5.9553 8.32026 5.95528C8.2409 5.87981 8.12824 5.83334 8.00589 5.83334C7.88351 5.83334 7.77083 5.87983 7.69146 5.95533L3.96277 9.50369L3.96282 9.50375L3.95644 9.50962C3.91615 9.54665 3.88547 9.58958 3.8649 9.63515L3.40918 9.42944L3.8649 9.63515C3.84436 9.68066 3.83395 9.72861 3.83351 9.77643C3.83307 9.82425 3.84261 9.87229 3.86226 9.918C3.88194 9.96379 3.91174 10.0071 3.95121 10.0446C3.99075 10.0823 4.03911 10.1133 4.0941 10.1347C4.14913 10.1561 4.20888 10.1672 4.26965 10.1667C4.33042 10.1662 4.3899 10.1541 4.44441 10.1318C4.4989 10.1096 4.54656 10.0778 4.58527 10.0397L4.58522 10.0396L4.59151 10.0337L7.6612 7.11242L8.00589 6.7844L8.35058 7.11242L11.4202 10.0336M12.6668 9.76867L12.1668 9.76856C12.1668 9.83943 12.1448 9.9109 12.1005 9.97405L12.5097 10.2614L12.1005 9.97405C12.0559 10.0375 11.9899 10.0906 11.908 10.1229C11.826 10.1552 11.7344 10.164 11.6458 10.1473C11.5573 10.1305 11.4792 10.0897 11.4202 10.0336M12.6668 9.76867C12.6668 9.53342 12.5685 9.30782 12.3937 9.14149L12.6668 9.76867ZM11.4202 10.0336L11.4203 10.0337L11.0756 10.3959L11.4202 10.0336Z"
                          fill="white"
                          stroke="white"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div> -->
      </div>
      <div
        class=" w-[96%] h-[80%] flex items-center mx-auto gap-4"
      >
        <ProjectCards
          :projectsData="projectsData"
          @movetoproject="movetoProjectScene"
        />
      </div>
    </div>

    <div
      v-if="Store.isMobile"
      class="flex flex-col gap-4 min-h-[100%] w-full bg-[#111928]"
    >
      <div class="w-[90%] h-[100px] flex items-start justify-between flex-col relative mx-auto gap-4 top-2">
        <div class="w-full flex flex-col gap-1">
          <p class="self-stretch text-white  text-xl font-normal ">
            Projects
          </p>
          <p className="text-gray-400 text-xs font-normal">
            Select Projects to explore
          </p>
        </div>
        <!-- <div class="w-[100%] flex flex-row items-center justify-between">
          <div class="h-[35px] w-[75%]">
            <div className="h-full w-full px-4 py-3 bg-[#1f2a37] rounded-lg border border-gray-600 justify-start items-center gap-2.5 inline-flex">
              <div className="grow shrink basis-0 h-[18px] justify-start items-center gap-2.5 flex">
                <div className="w-4 h-4 relative">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      id="search-outline"
                      clip-path="url(#clip0_6193_1746)"
                    >
                      <g id="Vector">
                        <path
                          d="M6.40008 12.8C5.13426 12.8 3.89687 12.4246 2.84439 11.7214C1.7919 11.0182 0.971586 10.0186 0.48718 8.84917C0.00277303 7.67973 -0.12397 6.3929 0.122979 5.15142C0.369927 3.90994 0.979475 2.76957 1.87454 1.87452C2.76961 0.979463 3.90999 0.369922 5.15148 0.122977C6.39298 -0.123968 7.67982 0.002773 8.84928 0.487174C10.0187 0.971574 11.0183 1.79188 11.7215 2.84435C12.4248 3.89683 12.8002 5.1342 12.8002 6.4C12.7982 8.0968 12.1233 9.72356 10.9235 10.9234C9.72367 12.1232 8.0969 12.7981 6.40008 12.8ZM6.40008 1.6C5.45072 1.6 4.52267 1.88152 3.73331 2.40895C2.94394 2.93638 2.32871 3.68604 1.9654 4.56312C1.6021 5.4402 1.50704 6.40532 1.69225 7.33643C1.87746 8.26754 2.33463 9.12282 3.00593 9.79411C3.67723 10.4654 4.53251 10.9226 5.46363 11.1078C6.39475 11.293 7.35988 11.1979 8.23698 10.8346C9.11407 10.4713 9.86374 9.85609 10.3912 9.06674C10.9186 8.27738 11.2001 7.34935 11.2001 6.4C11.1989 5.12735 10.6927 3.90719 9.79283 3.00729C8.89292 2.10739 7.67274 1.60127 6.40008 1.6Z"
                          fill="#9CA3AF"
                        />
                        <path
                          d="M15.2002 16C14.988 16 14.7846 15.9156 14.6346 15.7656L11.4345 12.5656C11.2888 12.4147 11.2082 12.2126 11.21 12.0029C11.2118 11.7931 11.2959 11.5925 11.4443 11.4441C11.5926 11.2958 11.7932 11.2117 12.003 11.2099C12.2128 11.208 12.4148 11.2887 12.5657 11.4344L15.7658 14.6344C15.8776 14.7463 15.9538 14.8888 15.9846 15.044C16.0155 15.1991 15.9996 15.36 15.9391 15.5061C15.8786 15.6523 15.7761 15.7772 15.6445 15.8651C15.513 15.953 15.3584 16 15.2002 16Z"
                          fill="#9CA3AF"
                        />
                      </g>
                    </g>
                    <defs>
                      <clipPath id="clip0_6193_1746">
                        <rect
                          width="16"
                          height="16"
                          fill="white"
                        />
                      </clipPath>
                    </defs>
                  </svg>
                </div>
                <p className="grow shrink basis-0 text-gray-400 text-sm font-normal leading-[17.50px]">
                  Search project
                </p>
              </div>
            </div>
          </div>
          <div class="w-[12%]">
            <div className="w-10 h-10 p-3.5 bg-[#1f2a37] rounded-full justify-center items-center inline-flex relative left-0">
              <div className="w-auto h-5 relative">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="adjustments-vertical-outline">
                    <g id="Vector">
                      <path
                        d="M4.75632 8.94931V1.99998C4.75632 1.82317 4.68797 1.6536 4.56632 1.52858C4.44467 1.40355 4.27968 1.33331 4.10764 1.33331C3.9356 1.33331 3.7706 1.40355 3.64895 1.52858C3.5273 1.6536 3.45896 1.82317 3.45896 1.99998V8.94931C3.03552 9.0878 2.66592 9.36114 2.40364 9.7298C2.14136 10.0984 2 10.5433 2 11C2 11.4567 2.14136 11.9015 2.40364 12.2702C2.66592 12.6388 3.03552 12.9122 3.45896 13.0506V14C3.45896 14.1768 3.5273 14.3464 3.64895 14.4714C3.7706 14.5964 3.9356 14.6666 4.10764 14.6666C4.27968 14.6666 4.44467 14.5964 4.56632 14.4714C4.68797 14.3464 4.75632 14.1768 4.75632 14V13.0506C5.17976 12.9122 5.54935 12.6388 5.81163 12.2702C6.07391 11.9015 6.21527 11.4567 6.21527 11C6.21527 10.5433 6.07391 10.0984 5.81163 9.7298C5.54935 9.36114 5.17976 9.0878 4.75632 8.94931ZM4.10764 11.8333C3.94727 11.8333 3.7905 11.7844 3.65715 11.6929C3.52381 11.6013 3.41988 11.4712 3.35851 11.3189C3.29714 11.1666 3.28108 10.9991 3.31237 10.8374C3.34365 10.6758 3.42088 10.5273 3.53428 10.4107C3.64768 10.2942 3.79216 10.2148 3.94945 10.1827C4.10674 10.1505 4.26977 10.167 4.41794 10.2301C4.5661 10.2932 4.69274 10.4 4.78183 10.537C4.87093 10.674 4.91849 10.8352 4.91849 11C4.91814 11.2209 4.8326 11.4326 4.68062 11.5888C4.52863 11.7451 4.32258 11.833 4.10764 11.8333Z"
                        fill="#9CA3AF"
                      />
                      <path
                        d="M14 11C13.9985 10.5436 13.8564 10.0994 13.5943 9.73106C13.3321 9.3627 12.9632 9.08906 12.5405 8.94931V1.99998C12.5405 1.82317 12.4721 1.6536 12.3505 1.52858C12.2288 1.40355 12.0638 1.33331 11.8918 1.33331C11.7198 1.33331 11.5548 1.40355 11.4331 1.52858C11.3115 1.6536 11.2431 1.82317 11.2431 1.99998V8.94931C10.8197 9.0878 10.4501 9.36114 10.1878 9.7298C9.92552 10.0984 9.78415 10.5433 9.78415 11C9.78415 11.4567 9.92552 11.9015 10.1878 12.2702C10.4501 12.6388 10.8197 12.9122 11.2431 13.0506V14C11.2431 14.1768 11.3115 14.3464 11.4331 14.4714C11.5548 14.5964 11.7198 14.6666 11.8918 14.6666C12.0638 14.6666 12.2288 14.5964 12.3505 14.4714C12.4721 14.3464 12.5405 14.1768 12.5405 14V13.0506C12.9632 12.9109 13.3321 12.6373 13.5943 12.2689C13.8564 11.9005 13.9985 11.4564 14 11ZM11.8918 11.8333C11.7314 11.8333 11.5747 11.7844 11.4413 11.6929C11.308 11.6013 11.204 11.4712 11.1427 11.3189C11.0813 11.1666 11.0652 10.9991 11.0965 10.8374C11.1278 10.6758 11.205 10.5273 11.3184 10.4107C11.4318 10.2942 11.5763 10.2148 11.7336 10.1827C11.8909 10.1505 12.0539 10.167 12.2021 10.2301C12.3503 10.2932 12.4769 10.4 12.566 10.537C12.6551 10.674 12.7026 10.8352 12.7026 11C12.7023 11.2209 12.6168 11.4326 12.4648 11.5888C12.3128 11.7451 12.1067 11.833 11.8918 11.8333Z"
                        fill="#9CA3AF"
                      />
                      <path
                        d="M10.1079 4.99998C10.1064 4.5436 9.96436 4.09942 9.70218 3.73106C9.44001 3.3627 9.07114 3.08905 8.64839 2.94931V1.99998C8.64839 1.82317 8.58005 1.6536 8.4584 1.52858C8.33675 1.40355 8.17175 1.33331 7.99971 1.33331C7.82767 1.33331 7.66268 1.40355 7.54103 1.52858C7.41938 1.6536 7.35103 1.82317 7.35103 1.99998V2.94931C6.92759 3.0878 6.558 3.36114 6.29572 3.7298C6.03344 4.09845 5.89208 4.54329 5.89208 4.99998C5.89208 5.45667 6.03344 5.90151 6.29572 6.27016C6.558 6.63882 6.92759 6.91216 7.35103 7.05065V14C7.35103 14.1768 7.41938 14.3464 7.54103 14.4714C7.66268 14.5964 7.82767 14.6666 7.99971 14.6666C8.17175 14.6666 8.33675 14.5964 8.4584 14.4714C8.58005 14.3464 8.64839 14.1768 8.64839 14V7.05065C9.07114 6.9109 9.44001 6.63726 9.70218 6.2689C9.96436 5.90054 10.1064 5.45636 10.1079 4.99998ZM7.99971 5.83331C7.83934 5.83331 7.68257 5.78444 7.54923 5.69287C7.41589 5.6013 7.31196 5.47115 7.25059 5.31888C7.18922 5.16661 7.17316 4.99906 7.20444 4.8374C7.23573 4.67575 7.31296 4.52727 7.42636 4.41072C7.53976 4.29418 7.68424 4.21481 7.84153 4.18266C7.99881 4.1505 8.16185 4.16701 8.31001 4.23008C8.45818 4.29315 8.58481 4.39996 8.67391 4.537C8.76301 4.67405 8.81056 4.83516 8.81056 4.99998C8.81022 5.22089 8.72468 5.43264 8.57269 5.58885C8.4207 5.74505 8.21466 5.83296 7.99971 5.83331Z"
                        fill="#9CA3AF"
                      />
                    </g>
                  </g>
                </svg>
              </div>
            </div>
          </div>
        </div> -->
      </div>
      <div
        :class="Object.keys(projectsData).length >= 3 ? '':'justify-center'"
        class="w-[90%] h-[450px] flex flex-col items-center mx-auto overflow-x-auto scrollbar gap-4"
      >
        <ProjectCards
          :projectsData="projectsData"
          @movetoproject="movetoProjectScene"
        />
      </div>
      <!-- <div class="w-full">
        <div class="h-12 p-3 bg-white/10 rounded-lg justify-center items-center gap-2 inline-flex relative left-5 ">
          <span class="text-center"><p class="text-white text-xs font-medium  capitalize">Powered by</p></span>
          <div class="w-6 h-[17.45px] relative">
            <svg
              width="47"
              height="44"
              viewBox="0 0 47 44"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M20.7375 34.3002C20.0319 35.03 19.1296 35.1765 18.3109 35.2307C16.6304 35.3419 14.9249 35.3925 13.2523 35.2323C11.0115 35.0177 8.81697 34.5546 6.82385 33.3547C3.15876 31.1479 0.961691 28.0116 0.211853 23.7726C-0.199354 21.4479 -0.0144306 19.2138 0.788345 17.0617C2.32631 12.9386 5.29601 10.2142 9.42253 8.74321C10.9497 8.19877 12.5299 7.99348 14.1585 8.00016C20.5084 8.02622 26.8585 7.99816 33.2084 8.01457C38.838 8.02911 43.0052 10.6165 45.5544 15.5486C48.0614 20.3991 47.3074 26.6676 43.2944 30.7052C40.8284 33.1862 37.9905 34.6768 34.4665 35.0734C32.3025 35.317 30.158 35.118 28.0124 35.2768C27.3248 35.3276 27.2354 35.0345 27.242 34.4523C27.2685 32.1217 27.2014 29.7889 27.2774 27.4603C27.3096 26.4756 27.1041 25.7491 26.16 25.3741C25.5766 25.1423 25.2061 24.2107 24.6111 24.4661C23.6632 24.8728 22.6742 25.4596 22.0518 26.2464C21.6758 26.7215 21.9507 27.7185 21.9486 28.4811C21.9435 30.4407 21.9469 30.4407 20.0456 30.4407C18.3292 30.4407 18.3292 30.4407 18.3291 28.7783C18.3291 27.4923 18.3536 26.2054 18.3169 24.9204C18.3029 24.4282 18.4488 24.0748 18.8325 23.7956C20.5979 22.5112 22.3572 21.2181 24.1334 19.9488C24.4157 19.747 24.6616 19.594 25.1084 19.9322C26.9708 21.3413 28.9085 22.6513 30.8203 23.9952C31.1787 24.2471 31.112 24.6171 31.112 24.9719C31.1117 26.8609 31.1049 28.7498 31.1189 30.6387C31.1212 30.9493 30.9293 31.3672 31.5531 31.4146C34.9663 31.674 38.1122 31.0011 40.4819 28.3873C44.1095 24.386 44.1801 18.8817 40.5108 14.9368C38.9366 13.2444 36.918 12.0835 34.502 11.8692C28.4199 11.3299 22.3247 11.7828 16.2378 11.6238C13.7407 11.5585 11.2667 11.6494 9.02193 12.8957C3.62746 15.8906 2.19518 22.3575 5.46056 26.9984C7.40579 29.763 10.2632 31.4918 13.9028 31.4149C16.2729 31.3648 18.6465 31.4556 21.0155 31.3831C22.1142 31.3496 22.1577 31.8738 21.8173 32.5932C21.5405 33.1783 21.3835 33.8577 20.7375 34.3002Z"
                fill="white"
              />
            </svg>
          </div>
          <span class="text-center"><p class="text-white text-xs font-medium capitalize">PropVR</p></span>
        </div>
      </div> -->
    </div>
  </div>
</template>

<style scoped>

</style>
